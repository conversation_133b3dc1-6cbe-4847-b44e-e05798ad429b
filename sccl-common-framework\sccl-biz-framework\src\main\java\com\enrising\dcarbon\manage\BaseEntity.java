package com.enrising.dcarbon.manage;

import com.baomidou.mybatisplus.annotation.*;
import com.enrising.dcarbon.collector.CollectedDatasource;
import com.enrising.dcarbon.string.StringUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 基础实体
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-10 16:01
 * @email <EMAIL>
 */
@Getter
@Setter
@Accessors(chain = true)
public class BaseEntity implements CollectedDatasource {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    protected Long id;
    /**
     * String格式的ID
     */
    @TableField(exist = false)
    protected String idString;
    /**
     * 创建时间
     */
    protected Date createTime;
    /**
     * 上次更新时间
     */
    protected Date updateTime;
    /**
     * 删除标识
     */
    @TableLogic(delval = "1", value = "0")
    protected Integer delFlag;

    public BaseEntity setId(Long id) {
        this.id = id;
        if (id != null) {
            idString = "" + id;
        }
        return this;
    }

    public String getIdString() {
        if (StringUtils.isEmpty(idString) && id != null) {
            idString = "" + id;
        }
        return idString;
    }

    /**
     * 将该类下转成子类
     *
     * @return T
     * <AUTHOR> ^ ▽ ^ *)
     * @date 2023/5/11 9:28
     */
    @SuppressWarnings("unchecked")
    public <T> T convert() {
        return (T) this;
    }

    @SuppressWarnings("unchecked")
    public <T> T convert(Class<T> type) {
        return (T) this;
    }

    @Override
    public String getAuditKey() {
        return idString;
    }

    @Override
    public boolean isAvailable() {
        return true;
    }
}
