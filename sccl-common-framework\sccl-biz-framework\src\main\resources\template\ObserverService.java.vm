package ${package.ServiceImpl};

import com.enrising.dcarbon.bean.SpringUtil;
import com.enrising.dcarbon.manage.ObserverService;
import ${package.Entity}.${entity};
import ${package.Mapper}.${table.mapperName};
import ${package.Service}.${table.serviceName};
import org.springframework.stereotype.Service;

/**
 * ${table.comment}
 * 表名：${table.name}
 * 基于观察者模式的无状态Service实现层
 *
 * <AUTHOR>
 * @date ${date}
 */
public class ${table.serviceImplName} extends ObserverService<${entity}> implements ${table.serviceName} {
    /**
     * 该静态方法可以直接返回Mapper，在使用基于观察者模式的Service时，监听器可以调用该方法来获取Mapper，而无需使得监听器纳入Spring上下文
     *
     * @return ${package.Mapper}.${table.mapperName}
     * <AUTHOR>
     * @date ${date}
     */
    public static ${table.mapperName} getMapper(){
        return SpringUtil.getBean(${table.mapperName}.class);
    }
}
