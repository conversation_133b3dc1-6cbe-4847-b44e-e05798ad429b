package com.enrising.dcarbon.demo.collector;

import com.enrising.dcarbon.collector.AbstractCollectorTemplate;
import com.enrising.dcarbon.collector.RouteEvent;
import com.enrising.dcarbon.observer.EventListener;

import java.util.List;

/**
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-12 14:14
 * @email <EMAIL>
 */
public class DemoRouteEventListener implements EventListener<RouteEvent> {
    @Override
    public void onEvent(RouteEvent event) {
        List<DemoDatasource> datasourceList = AbstractCollectorTemplate.convert(event.getSource(), DemoDatasource.class);
        System.out.println("路由"+datasourceList.size()+"条数据，内容如下");
        datasourceList.forEach(System.out::println);
    }
}
