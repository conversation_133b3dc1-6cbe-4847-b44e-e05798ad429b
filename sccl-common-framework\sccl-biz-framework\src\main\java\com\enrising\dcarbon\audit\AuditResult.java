package com.enrising.dcarbon.audit;

import com.enrising.dcarbon.manage.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;


/**
 * 可保存的稽核结果类
 *
 * <AUTHOR>
 * @date 2022-12-01
 */
@Getter
@Setter
public class AuditResult extends BaseEntity implements RefereeDatasource, Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 稽核的对象主键
     */
    private String auditKey;
    /**
     * 节点主题
     */
    private String nodeTopic;
    /**
     * 节点步序
     */
    private Integer step;
    /**
     * 评判消息
     */
    private String refereeMessage;
    /**
     * 内容类型
     */
    private String contentType;
    /**
     * 评判内容
     */
    private String content;
}
