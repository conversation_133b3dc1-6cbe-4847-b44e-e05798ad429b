package com.enrising.dcarbon.demo.audit;

import com.enrising.dcarbon.audit.Auditable;
import com.enrising.dcarbon.audit.RefereeDatasource;
import com.enrising.dcarbon.audit.AbstractReferee;
import com.enrising.dcarbon.audit.RefereeChain;
import com.enrising.dcarbon.audit.RefereeNode;
import com.enrising.dcarbon.audit.RefereeResult;
import com.enrising.dcarbon.id.IdGenerator;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * 演示评判器
 *
 * <AUTHOR> <PERSON>
 * @Date 2022/11/11 11:23
 * @Email <EMAIL>
 */
@Slf4j
public class DemoReferee extends AbstractReferee {
    public DemoReferee() {
        super("testReferee");
    }

    @Override
    public RefereeResult doReferee(RefereeResult lastRefereeResult, Auditable auditable, Map<Class<? extends RefereeDatasource>, List<RefereeDatasource>> refereeDatasourceList) {
        //获取已完成的节点的结果
        List<RefereeResult> refereeResults = RefereeChain.currentRefereeChain
                .get()
                .collectResults();

        //获取链中的某个节点
        RefereeNode node = RefereeChain.currentRefereeChain
                .get()
                .findNode("node");
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            log.warn("稽核中断");
            //e.printStackTrace();
        }
        RefereeResult refereeResult = new RefereeResult();
        refereeResult.setRefereeMessage(IdGenerator.getNextIdAsString());
        return refereeResult;
    }
}
