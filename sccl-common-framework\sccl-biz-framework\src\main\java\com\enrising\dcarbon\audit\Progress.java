package com.enrising.dcarbon.audit;

import lombok.Data;

/**
 * 进度对象
 *
 * <AUTHOR>
 * @Date 2022/11/16 17:02
 * @Email <EMAIL>
 */
@Data
public class Progress {
    private String progressKey;
    /**
     * 总数
     */
    private int totalCount;
    /**
     * 剩余数
     */
    private int pendingCount;
    /**
     * 已完成数
     */
    private int finishedCount;
    /**
     * 当前完成比
     */
    private double finishedPercent;

    public boolean isOver() {
        return totalCount == finishedCount;
    }
}
