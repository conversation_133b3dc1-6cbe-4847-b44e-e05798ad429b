package com.enrising.dcarbon.statistic.repository;

import com.enrising.dcarbon.bean.SpringUtil;
import com.enrising.dcarbon.statistic.StatisticalEntity;
import com.enrising.dcarbon.statistic.StatisticalIndex;
import com.enrising.dcarbon.statistic.codec.StatisticalDeserializer;
import com.enrising.dcarbon.statistic.codec.StatisticalSerializer;
import com.enrising.dcarbon.statistic.mapper.StatisticalMapper;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 统计指标DB存储库，该类应该在Spring上下文初始化后创建
 *
 * <AUTHOR>
 * @Date 2022/10/26 15:13
 * @Email <EMAIL>
 */
public class StatisticalDBRepository implements StatisticalRepository<StatisticalEntity> {
    private static StatisticalMapper MAPPER = SpringUtil.getBean(StatisticalMapper.class);

    @Override
    public boolean save(List<StatisticalIndex> statisticalIndices, StatisticalSerializer<StatisticalEntity> serializer) {
        isMapperInitialized();
        List<StatisticalEntity> entities = statisticalIndices
                .stream()
                .map(serializer::serialize)
                .collect(Collectors.toList());
        return MAPPER.insertList(entities) == entities.size();
    }

    @Override
    public List<StatisticalIndex> query(StatisticalDeserializer<StatisticalEntity> deserializer, StatisticalEntity query) {
        isMapperInitialized();
        List<StatisticalEntity> entities = MAPPER.selectList(query);
        if (CollectionUtils.isEmpty(entities)) {
            return Collections.emptyList();
        }
        return entities
                .stream()
                .map(deserializer::deserialize)
                .collect(Collectors.toList());
    }

    @Override
    public boolean update(StatisticalEntity param, StatisticalEntity query) {
        isMapperInitialized();
        List<StatisticalEntity> entities = MAPPER.selectList(query);
        if (CollectionUtils.isEmpty(entities)) {
            return false;
        }
        List<Long> ids = entities
                .stream()
                .map(StatisticalEntity::getId)
                .collect(Collectors.toList());
        return MAPPER.updateAll(param, ids) >= 0;
    }

    @Override
    public boolean delete(StatisticalEntity query) {
        List<StatisticalEntity> entities = MAPPER.selectList(query);
        if (CollectionUtils.isEmpty(entities)) {
            return false;
        }
        List<Long> ids = entities
                .stream()
                .map(StatisticalEntity::getId)
                .collect(Collectors.toList());
        return MAPPER.deleteByIds(ids
                .stream()
                .map(String::valueOf)
                .toArray(value -> new String[0])) == ids.size();
    }

    private void isMapperInitialized() {
        if (MAPPER == null) {
            MAPPER = SpringUtil.getBean(StatisticalMapper.class);
            if (MAPPER == null) {
                throw new IllegalStateException("Mapper无法初始化");
            }
        }
    }
}
