# 登录日志分页查询测试示例

## 1. 分页查询（POST方式，使用PageHelper）

### 基础分页查询

```bash
curl -X POST "http://localhost:8080/system/loginLog/list" \
  -d "pageNum=1&pageSize=10"
```

### 带条件的分页查询

```bash
# 查询用户testuser的登录记录，第1页，每页5条
curl -X POST "http://localhost:8080/system/loginLog/list" \
  -d "pageNum=1&pageSize=5&username=testuser"

# 查询登录状态为2（成功有警告）的记录
curl -X POST "http://localhost:8080/system/loginLog/list" \
  -d "pageNum=1&pageSize=10&status=2"

# 查询指定时间范围的记录
curl -X POST "http://localhost:8080/system/loginLog/list" \
  -d "pageNum=1&pageSize=10&startTime=2024-01-01 00:00:00&endTime=2024-01-31 23:59:59"

# 查询指定IP地址的记录
curl -X POST "http://localhost:8080/system/loginLog/list" \
  -d "pageNum=1&pageSize=10&ipAddress=192.168"

# 复合条件查询
curl -X POST "http://localhost:8080/system/loginLog/list" \
  -d "pageNum=1&pageSize=5&username=testuser&status=2&ipAddress=192.168&startTime=2024-01-01 00:00:00&endTime=2024-01-31 23:59:59"
```

## 2. 不分页查询（GET方式）

```bash
# 查询所有记录（不分页）
curl -X GET "http://localhost:8080/system/loginLog/listAll"

# 带条件查询（不分页）
curl -X GET "http://localhost:8080/system/loginLog/listAll?username=testuser&status=2"
```

## 3. 响应格式

### 分页查询响应（TableDataInfo格式）

```json
{
  "code": 0,
  "total": 150,
  "rows": [
    {
      "id": 1,
      "userId": 123,
      "username": "testuser",
      "loginTime": "2024-01-15 14:30:00.000",
      "ipAddress": "*************",
      "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
      "status": 1,
      "remark": "登录成功"
    },
    {
      "id": 2,
      "userId": 123,
      "username": "testuser",
      "loginTime": "2024-01-15 02:30:15.000",
      "ipAddress": "*************",
      "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
      "status": 2,
      "remark": "登录成功；我们注意到您的账户在 2024-01-15 02:30:15 有一次非常规时间的登录尝试"
    }
  ]
}
```

### 不分页查询响应（AjaxResult格式）

```json
{
  "code": 0,
  "msg": "操作成功",
  "data": [
    {
      "id": 1,
      "userId": 123,
      "username": "testuser",
      "loginTime": "2024-01-15 14:30:00.000",
      "ipAddress": "*************",
      "userAgent": "Mozilla/5.0...",
      "status": 2,
      "remark": "登录成功；异常提示信息"
    }
  ]
}
```

## 4. 分页参数说明

### 请求参数

- `pageNum`: 页码，从1开始，通过请求参数传递
- `pageSize`: 每页记录数，通过请求参数传递
- `username`: 用户名模糊查询
- `userId`: 精确匹配用户ID
- `status`: 登录状态（0-失败，1-成功，2-成功有警告）
- `ipAddress`: IP地址模糊查询
- `remark`: 备注信息模糊查询
- `startTime`: 开始时间，格式 yyyy-MM-dd HH:mm:ss
- `endTime`: 结束时间，格式 yyyy-MM-dd HH:mm:ss

### 响应字段（分页）

- `code`: 状态码（0表示成功）
- `total`: 总记录数
- `rows`: 当前页数据列表

### 响应字段（不分页）

- `code`: 状态码（0表示成功）
- `msg`: 消息
- `data`: 数据列表

## 5. 前端集成示例

### JavaScript/Ajax（分页查询）

```javascript
function queryLoginLogs(pageNum = 1, pageSize = 10, conditions = {}) {
  const params = new URLSearchParams();
  params.append('pageNum', pageNum);
  params.append('pageSize', pageSize);
  
  // 添加查询条件
  Object.keys(conditions).forEach(key => {
    if (conditions[key] !== null && conditions[key] !== undefined && conditions[key] !== '') {
      params.append(key, conditions[key]);
    }
  });
  
  $.ajax({
    url: '/system/loginLog/list',
    type: 'POST',
    data: params.toString(),
    contentType: 'application/x-www-form-urlencoded',
    success: function(response) {
      if (response.code === 0) {
        console.log('总记录数:', response.total);
        console.log('数据:', response.rows);
        
        // 更新表格数据
        updateTable(response.rows);
        
        // 更新分页组件
        updatePagination(pageNum, Math.ceil(response.total / pageSize), response.total);
      }
    },
    error: function(xhr, status, error) {
      console.error('查询失败:', error);
    }
  });
}

// 使用示例
queryLoginLogs(1, 10, {
  username: 'testuser',
  status: 2,
  startTime: '2024-01-01 00:00:00',
  endTime: '2024-01-31 23:59:59'
});
```

### Vue.js示例（使用ElementUI表格）

```vue
<template>
  <div>
    <!-- 查询条件 -->
    <el-form :model="queryForm" inline @submit.prevent="handleQuery">
      <el-form-item label="用户名">
        <el-input v-model="queryForm.username" placeholder="请输入用户名"/>
      </el-form-item>
      <el-form-item label="登录状态">
        <el-select v-model="queryForm.status" placeholder="请选择状态">
          <el-option label="全部" value=""/>
          <el-option label="失败" :value="0"/>
          <el-option label="成功" :value="1"/>
          <el-option label="成功有警告" :value="2"/>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">查询</el-button>
        <el-button @click="handleReset">重置</el-button>
      </el-form-item>
    </el-form>
    
    <!-- 数据表格 -->
    <el-table :data="tableData" v-loading="loading">
      <el-table-column prop="username" label="用户名"/>
      <el-table-column prop="loginTime" label="登录时间"/>
      <el-table-column prop="ipAddress" label="IP地址"/>
      <el-table-column prop="status" label="状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status === 0" type="danger">失败</el-tag>
          <el-tag v-else-if="scope.row.status === 1" type="success">成功</el-tag>
          <el-tag v-else-if="scope.row.status === 2" type="warning">成功有警告</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="备注" show-overflow-tooltip/>
    </el-table>
    
    <!-- 分页组件 -->
    <el-pagination
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
      :current-page="pagination.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pagination.pageSize"
      :total="pagination.total"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
  </div>
</template>

<script>
export default {
  data() {
    return {
      loading: false,
      tableData: [],
      queryForm: {
        username: '',
        status: ''
      },
      pagination: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      }
    };
  },
  mounted() {
    this.loadData();
  },
  methods: {
    async loadData() {
      this.loading = true;
      try {
        const params = new URLSearchParams();
        params.append('pageNum', this.pagination.pageNum);
        params.append('pageSize', this.pagination.pageSize);
        
        // 添加查询条件
        Object.keys(this.queryForm).forEach(key => {
          if (this.queryForm[key] !== '') {
            params.append(key, this.queryForm[key]);
          }
        });
        
        const response = await this.$http.post('/system/loginLog/list', params.toString(), {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          }
        });
        
        if (response.data.code === 0) {
          this.tableData = response.data.rows;
          this.pagination.total = response.data.total;
        }
      } catch (error) {
        console.error('加载数据失败:', error);
        this.$message.error('加载数据失败');
      } finally {
        this.loading = false;
      }
    },
    
    handleQuery() {
      this.pagination.pageNum = 1;
      this.loadData();
    },
    
    handleReset() {
      this.queryForm = {
        username: '',
        status: ''
      };
      this.pagination.pageNum = 1;
      this.loadData();
    },
    
    handleCurrentChange(page) {
      this.pagination.pageNum = page;
      this.loadData();
    },
    
    handleSizeChange(size) {
      this.pagination.pageSize = size;
      this.pagination.pageNum = 1;
      this.loadData();
    }
  }
};
</script>
```

## 6. 注意事项

1. **分页参数**：pageNum和pageSize通过请求参数传递，不是在请求体中
2. **响应格式**：分页查询使用TableDataInfo格式，不分页查询使用AjaxResult格式
3. **时间过滤**：如果有startTime和endTime参数，会在后端进行时间范围过滤
4. **PageHelper**：系统使用PageHelper自动处理分页逻辑 
