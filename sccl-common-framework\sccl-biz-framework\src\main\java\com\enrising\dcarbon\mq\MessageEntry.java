package com.enrising.dcarbon.mq;

/**
 * 消息条目
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-26 9:45
 * @email <EMAIL>
 */
public class MessageEntry<M> {
    protected long messageId;
    protected M message;
    protected long expiringTime;

    public MessageEntry() {
    }

    public MessageEntry<M> setMessageId(long messageId) {
        this.messageId = messageId;
        return this;
    }

    public MessageEntry<M> setMessage(M message) {
        this.message = message;
        return this;
    }

    public MessageEntry<M> setExpiringTime(long expiringTime) {
        this.expiringTime = expiringTime;
        return this;
    }

    public long getMessageId() {
        return messageId;
    }

    public M getMessage() {
        return message;
    }

    public long getExpiringTime() {
        return expiringTime;
    }
}
