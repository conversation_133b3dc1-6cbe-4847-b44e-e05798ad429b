package com.sccl.common.utils;

import com.sccl.common.lang.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;

/**
 * IP地址工具类
 *
 * <AUTHOR>
 */
public class IpUtils {
    private static final String UNKNOWN = "unknown";
    private static final String LOCAL_IPv6 = "0:0:0:0:0:0:0:1";
    private static final String LOCAL_IPv4 = "127.0.0.1";

    /**
     * 常见的代理头，按优先级排序
     */
    private static final List<String> PROXY_HEADERS = Arrays.asList(
            "X-Real-IP",           // Nginx代理
            "X-Forwarded-For",     // 标准代理头
            "x-forwarded-for",     // 小写版本
            "Proxy-Client-IP",     // Apache代理
            "WL-Proxy-Client-IP",  // WebLogic代理
            "HTTP_CLIENT_IP",      // HTTP代理
            "HTTP_X_FORWARDED_FOR" // HTTP代理
    );

    /**
     * 获取客户端真实IP地址
     * 优化处理多层代理的情况，避免重复IP导致限流问题
     *
     * @param request HTTP请求对象
     * @return 客户端真实IP地址
     */
    public static String getIpAddr(HttpServletRequest request) {
        if (request == null) {
            return UNKNOWN;
        }

        // 按优先级尝试从代理头中获取IP
        String ip = getFirstValidIpFromHeaders(request);

        // 如果仍然未找到，则使用远程地址
        if (StringUtils.isBlank(ip)) {
            ip = request.getRemoteAddr();
        }

        // 处理IPv6的本地回环地址
        return LOCAL_IPv6.equals(ip) ? LOCAL_IPv4 : ip;
    }

    /**
     * 从请求头中查找第一个有效的IP地址
     * 处理多层代理情况，取最前面的真实客户端IP
     */
    private static String getFirstValidIpFromHeaders(HttpServletRequest request) {
        for (String header : PROXY_HEADERS) {
            String headerValue = request.getHeader(header);
            if (StringUtils.isNotBlank(headerValue) && !UNKNOWN.equalsIgnoreCase(headerValue)) {
                // 处理多个IP的情况（多层代理会产生逗号分隔的IP列表）
                String realIp = extractRealIpFromProxyChain(headerValue);
                if (StringUtils.isNotBlank(realIp)) {
                    return realIp;
                }
            }
        }
        return null;
    }

    /**
     * 从代理链中提取真实的客户端IP
     * 格式：client_ip, proxy1_ip, proxy2_ip
     *
     * @param proxyChain 代理链字符串
     * @return 真实的客户端IP
     */
    private static String extractRealIpFromProxyChain(String proxyChain) {
        if (StringUtils.isBlank(proxyChain)) {
            return null;
        }

        // 按逗号分割IP列表
        String[] ips = proxyChain.split(",");

        // 遍历IP列表，找到第一个有效的非内网IP
        for (String ip : ips) {
            String trimmedIp = ip.trim();
            if (isValidPublicIp(trimmedIp)) {
                return trimmedIp;
            }
        }

        // 如果没有找到公网IP，返回第一个有效IP
        for (String ip : ips) {
            String trimmedIp = ip.trim();
            if (isValidIp(trimmedIp)) {
                return trimmedIp;
            }
        }

        return null;
    }

    /**
     * 判断是否为有效的公网IP
     * 排除内网IP和特殊IP
     */
    private static boolean isValidPublicIp(String ip) {
        if (!isValidIp(ip)) {
            return false;
        }

        // 排除内网IP段
        if (isPrivateIp(ip)) {
            return false;
        }

        return true;
    }

    /**
     * 判断是否为有效IP格式
     */
    private static boolean isValidIp(String ip) {
        if (StringUtils.isBlank(ip) || UNKNOWN.equalsIgnoreCase(ip)) {
            return false;
        }

        // 简单的IP格式验证
        String[] parts = ip.split("\\.");
        if (parts.length != 4) {
            return false;
        }

        try {
            for (String part : parts) {
                int num = Integer.parseInt(part);
                if (num < 0 || num > 255) {
                    return false;
                }
            }
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 判断是否为内网IP
     */
    private static boolean isPrivateIp(String ip) {
        if (StringUtils.isBlank(ip)) {
            return false;
        }

        // 本地回环地址
        if (ip.startsWith("127.") || LOCAL_IPv4.equals(ip)) {
            return true;
        }

        // 内网IP段
        if (ip.startsWith("192.168.") ||
            ip.startsWith("10.") ||
            (ip.startsWith("172.") && isInRange172(ip))) {
            return true;
        }

        return false;
    }

    /**
     * 判断是否在**********-**************范围内
     */
    private static boolean isInRange172(String ip) {
        try {
            String[] parts = ip.split("\\.");
            if (parts.length >= 2) {
                int secondOctet = Integer.parseInt(parts[1]);
                return secondOctet >= 16 && secondOctet <= 31;
            }
        } catch (NumberFormatException e) {
            // 忽略解析错误
        }
        return false;
    }
}
