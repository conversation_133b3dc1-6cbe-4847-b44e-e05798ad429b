package com.enrising.dcarbon.state;

import com.alibaba.druid.support.spring.stat.annotation.Stat;

/**
 * 状态机抽象接口
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-09 16:34
 * @email <EMAIL>
 */
public interface StateMachine {
    /**
     * 状态机初始化，此操作不会发生状态改变
     *
     * @param stateful 有状态的数据
     * @return void
     * <AUTHOR> ^ ▽ ^ *)
     * @date 2023/5/10 11:03
     */
    void initialize(Stateful stateful);

    /**
     * 状态机状态改变，状态改变的相关操作在此方法内部定义
     *
     * @param state 要变化到的状态
     * @return void
     * <AUTHOR> ^ ▽ ^ *)
     * @date 2023/5/10 11:03
     */
    void stateChange(State state);

    /**
     * 判断当前状态机的状态是否和给定状态一致
     *
     * @param stateType 状态类型
     * @return boolean
     * <AUTHOR> ^ ▽ ^ *)
     * @date 2023/5/10 11:04
     */
    default boolean isCurrentStateMatch(Class<? extends State> stateType) {
        State current = currentState();
        if (current == null) {
            throw new IllegalStateException("当前状态机还未初始化");
        }
        return stateType.isAssignableFrom(current.getClass());
    }

    /**
     * 获取当前状态机中的状态
     *
     * @return com.enrising.dcarbon.state.State
     * <AUTHOR> ^ ▽ ^ *)
     * @date 2023/5/10 11:05
     */
    State currentState();
}
