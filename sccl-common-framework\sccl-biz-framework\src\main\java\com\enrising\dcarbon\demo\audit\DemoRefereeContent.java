package com.enrising.dcarbon.demo.audit;

import com.enrising.dcarbon.audit.RefereeResult;
import com.enrising.dcarbon.audit.AbstractRefereeContent;
import com.enrising.dcarbon.audit.AuditResult;
import lombok.ToString;

/**
 * 示列评判结果
 *
 * <AUTHOR>
 * @date 2022-12-01 17:31
 * @email <EMAIL>
 */
@ToString
public class DemoRefereeContent extends AbstractRefereeContent {
    /**
     * 报账单ID
     */
    private Long mssAccountId;

    /**
     * 台账对应的电表id
     */
    private Long ammeterid;
    /**
     * 能耗站址id
     */
    private String stationcode;
    /**
     * 集团站址id
     */
    private String stationcode_source;
    /**
     * 比对的台账id
     */
    private Long pcid_compare;

    /**
     * 创建一个新的评判结果内容类
     *
     * @param refereeResult 节点输出的评判结果
     * @param step          节点步序
     * @param auditKey      稽核对象主键
     * <AUTHOR>
     * @date 2022/12/1 16:28
     */
    public DemoRefereeContent(RefereeResult refereeResult, int step, String auditKey) {
        super(refereeResult, step, auditKey);
    }

    public static void main(String[] args) {
        RefereeResult refereeResult = new RefereeResult("测试节点结果", true, "测试成功");

        //创建评审内容对象
        DemoRefereeContent content = new DemoRefereeContent(refereeResult, 1, "1");
        content.ammeterid = 123L;
        content.pcid_compare = 1L;
        content.stationcode = "12313123";
        //...

        //获取稽核明细对象实体
        AuditResult auditResult = content.getAuditResult();
        System.out.println(auditResult);

        //保存实体
        //mapper.insert(auditResult);

        //实体转内容对象
        DemoRefereeContent demoRefereeContent = (DemoRefereeContent) AbstractRefereeContent.forAuditResultWithJson(auditResult);
        System.out.println(demoRefereeContent);
    }
}
