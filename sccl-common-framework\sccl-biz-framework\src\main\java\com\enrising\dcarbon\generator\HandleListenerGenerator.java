package com.enrising.dcarbon.generator;


import com.baomidou.mybatisplus.generator.config.po.TableInfo;
import com.enrising.dcarbon.date.DateUtils;
import com.enrising.dcarbon.property.MapParamsBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;

import java.io.File;
import java.util.List;
import java.util.Map;

/**
 * 生成基于观察者模式的Service的相关配置
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-30 17:16
 * @email <EMAIL>
 */
@Slf4j
public class HandleListenerGenerator extends AbstractTemplateGenerator {
    private final Map<String, Object> metadata;

    private HandleListenerGenerator(Map<String, Object> metadata) {
        super("/template/HandleListener.java.vm");
        this.metadata = metadata;
    }

    public static Builder builder(Class<?> eventType) {
        return new Builder(eventType);
    }

    @Override
    public Map<String, Object> metadata() {
        return metadata;
    }

    @Override
    public String dirPath() {
        return metadata.get("path") + "\\" + ((String) metadata.get("parentPackage")).replace(".", "\\") + "\\listener" + ((boolean) metadata.get("isUseStatefulObserverService") ? "\\handle" : "");
    }

    @Override
    public void generate(String fileName) {
        super.generate(metadata.get("listenerName") + ".java");
        if (metadata.containsKey("isUseStatefulObserverService") && (boolean) metadata.get("isUseStatefulObserverService")) {
            File file = new File(metadata.get("path") + "\\" + ((String) metadata.get("parentPackage")).replace(".", "\\") + "\\listener\\state");
            if (!file.exists() && !file.mkdirs()) {
                log.warn("生成state目录失败");
            }
        }
    }

    public static class Builder extends MapParamsBuilder {
        public Builder(Class<?> eventType) {
            addParam("eventName", eventType.getSimpleName());
        }

        public HandleListenerGenerator build() {
            return new HandleListenerGenerator(getParams());
        }
    }
}
