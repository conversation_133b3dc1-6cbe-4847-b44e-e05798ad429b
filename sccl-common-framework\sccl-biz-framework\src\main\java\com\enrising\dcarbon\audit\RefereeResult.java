package com.enrising.dcarbon.audit;

import lombok.Getter;
import lombok.Setter;

/**
 * 评判结果，强烈推荐该类通过继承拓展
 *
 * <AUTHOR>
 * @Date 2022/11/10 17:22
 * @Email <EMAIL>
 */
@Getter
@Setter
public class RefereeResult implements Cloneable {
    /**
     * 评判主题：如评判：本次报账是否与上期一致
     */
    protected String topic;
    /**
     * 是否评判为true
     */
    protected boolean isTrue;
    /**
     * 是否评判为false
     */
    protected boolean isFalse;
    /**
     * 评判的消息结果
     */
    protected String refereeMessage;

    public RefereeResult(boolean isTrue, String refereeMessage) {
        this.isTrue = isTrue;
        this.isFalse = !isTrue;
        this.refereeMessage = refereeMessage;
    }

    public RefereeResult(String topic, boolean isTrue, String refereeMessage) {
        this.topic = topic;
        this.isTrue = isTrue;
        this.isFalse = !isTrue;
        this.refereeMessage = refereeMessage;
    }

    public RefereeResult() {
    }

    /**
     * 子类中如果有非基础类型的对象参数，请务必重写该方法
     *
     * @return java.lang.Object
     * <AUTHOR>
     * @date 2022/11/23 16:26
     */
    @Override
    protected Object clone() throws CloneNotSupportedException {
        return super.clone();
    }
}
