package com.enrising.dcarbon.demo.statedemo;

import com.enrising.dcarbon.observer.Event;
import com.enrising.dcarbon.observer.EventManger;
import com.enrising.dcarbon.state.StateChangeEvent;

/**
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-17 10:01
 * @email <EMAIL>
 */
public class EventManagerInstance {
    public static final EventManger EVENT_MANGER = new EventManger();

    static {
        EVENT_MANGER.addListener(StateChangeEvent.class, new StateChangeEventListener1());
    }
}
