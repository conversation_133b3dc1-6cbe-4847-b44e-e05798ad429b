package com.enrising.dcarbon.property;

import com.enrising.dcarbon.reflect.ObjectUtil;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-31 10:20
 * @email <EMAIL>
 */
public class MapParamsBuilder {
    private final Map<String, Object> params = new HashMap<>();

    protected MapParamsBuilder() {
    }

    public static MapParamsBuilder newInstance() {
        return new MapParamsBuilder();
    }

    public MapParamsBuilder addParam(String name, Object value) {
        params.put(name, value);
        return this;
    }

    public MapParamsBuilder addAllParams(Map<String, Object> params) {
        this.params.putAll(params);
        return this;
    }

    public MapParamsBuilder addObjectParams(Object bean) {
        if (ObjectUtil.isNull(bean)) {
            return this;
        }
        Field[] fields = ObjectUtil.getObjectFields(bean);
        for (Field field : fields) {
            field.setAccessible(true);
            try {
                Object value = field.get(bean);
                if (value == null) {
                    continue;
                }
                params.put(field.getName(), value);
            } catch (Exception ignored) {
            }
        }
        return this;
    }

    @SuppressWarnings("unchecked")
    public <T> T getParam(String name) {
        return (T) params.get(name);
    }

    public Object removeAndGet(String name) {
        return params.remove(name);
    }

    public boolean exist(String name) {
        return params.containsKey(name);
    }

    public Set<String> names() {
        return params.keySet();
    }

    public MapParamsBuilder remove(String name) {
        params.remove(name);
        return this;
    }

    public Map<String, Object> getParams() {
        return this.params;
    }

    public MapParamsBuilder clear() {
        params.clear();
        return this;
    }
}
