package com.enrising.dcarbon.manage;

import com.enrising.dcarbon.observer.Event;
import com.enrising.dcarbon.observer.EventManger;
import com.enrising.dcarbon.state.ObserverStateMachine;

/**
 * 具备处理事件管理器和状态事件管理的的事件，操作事件应该继承该事件，对应的操作监听器可以利用状态事件管理器创建状态机管理状态
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-11 15:15
 * @email <EMAIL>
 */
public class WebHandleEvent<T> extends Event<T> {
    public WebHandleEvent() {
        super();
    }

    public WebHandleEvent(T source) {
        super(source);
    }

    /**
     * 处理事件管理器
     */
    private ResponsiveEventManager responsiveEventManager;
    /**
     * 状态事件管理器
     */
    private EventManger stateEventManager;
    /**
     * 当前请求的上下文
     */
    private PerRequestContext currentRequestContext;

    public ResponsiveEventManager getWebEventManager() {
        return responsiveEventManager;
    }

    public EventManger getStateEventManager() {
        return stateEventManager;
    }

    public WebHandleEvent<T> setCurrentRequestContext(PerRequestContext currentRequestContext) {
        this.currentRequestContext = currentRequestContext;
        return this;
    }

    public PerRequestContext getCurrentRequestContext() {
        return currentRequestContext;
    }

    public WebHandleEvent<T> setWebEventManager(ResponsiveEventManager responsiveEventManager) {
        this.responsiveEventManager = responsiveEventManager;
        return this;
    }

    public WebHandleEvent<T> setStateEventManager(EventManger stateEventManager) {
        this.stateEventManager = stateEventManager;
        return this;
    }

    /**
     * 如果该事件包含状态事件管理器的话使用该状态管理器创建一个状态机
     *
     * @return com.enrising.dcarbon.state.ObserverStateMachine
     * <AUTHOR> ^ ▽ ^ *)
     * @date 2023/5/26 11:19
     */
    public ObserverStateMachine newObserverStateMachine() {
        if (stateEventManager != null) {
            return new ObserverStateMachine(stateEventManager);
        }
        return null;
    }
}
