# **四川省水电费数据对接接口文档 V1.2**

## **文档历史**

| 版本号 | 修改日期       | 修改内容                        | 修改人员   |
|:----|:-----------|:----------------------------|:-------|
| 1.0 | 2025-05-15 | 新建                          | 黄学平    |
| 1.1 | 2025-06-09 | 新增生产环境EOP调用地址               | 黄学平    |
| 1.2 | 2025-06-11 | 明确地市编码来源，增加地市编码附件，强调数据上报完整性 | Gemini |

## **1\. 综述**

### **1.1 编制说明**

为满足中国电信集团集中MSS新一代计划建设系统IDC效益分析模块水电费数据的质量要求，特制定本规范。

### **1.2 适用范围**

《中国电信集团集中MSS新一代计划建设系统IDC效益分析模块水电费数据对接接口文档》用以指导省侧（四川省）水电费数据上报。

## **2\. API**

### **2.1 水电费数据上报接口**

#### **2.1.1 API简介**

**API功能介绍：**

* **DCOOS 测试环境:**
    * **SKU名称：** IDC水电能耗上报的接口包年
    * **SKUID：** 1912814726456242200
    * **EOP调用地址：** http://**************:12500/serviceAgent/rest/ghsjWeb/idc/waterElectricity
* **云桥环境（生产环境）:**
    * **SKU名称：** 各省IDC水电能耗上报包年
    * **SKUID：** 1930804157223022592
    * **调用地址:** http://eop-api-cloud.mss.ctc.com:12500/serviceAgent/rest/EOP/idc/waterElectricity
    * **注：** eop-api-cloud.mss.ctc.com 域名对应的IP 为 *************

#### **2.1.2 API操作**

**接口总览**

| 序号 | 场景       | HTTP动作 | 资源地址                  |
|:---|:---------|:-------|:----------------------|
| 1  | 水电费用上报接口 | POST   | /idc/waterElectricity |

**输入参数 (Body)**

| 参数          | 类型     | 说明                   |
|:------------|:-------|:---------------------|
| callId      | String | 调用者，四川省固定为**510000** |
| timeStamp   | Long   | 调用时间, 14位时间戳         |
| signStr     | String | 签名，详见附件二             |
| operateType | String | 操作类型，默认为 Add         |
| data        | Array  | 数据集，见下方说明            |

**data 数据集对象结构**

| 参数编码                     | 类型     | 参数名称        | 备注                            |
|:-------------------------|:-------|:------------|:------------------------------|
| dateDim                  | String | 时间维度        | 格式: YYYYMM                    |
| province                 | String | 省份编码        | 四川省固定为**510000**              |
| province\_name           | String | 省份名         | 四川省固定为**四川省**                 |
| city                     | String | 城市编码        | \*\*必须使用《附件三》\*\*中的 org\_code |
| city\_name               | String | 城市名         | \*\*必须使用《附件三》\*\*中的 org\_name |
| dc\_id                   | String | 数据中心编码      |                               |
| dc\_name                 | String | 数据中心名称      |                               |
| pue\_act                 | String | 电能运行效率      |                               |
| water\_consumption       | String | 每月出账水费用水量   | 单位：吨                          |
| water\_bill              | String | 每月出账水费（不含税） | 单位：元                          |
| electricity\_consumption | String | 每月用电量       | 单位：度                          |
| electricity\_bill        | String | 出账电费（不含税）   | 单位：元                          |

**输出参数**

接口调用成功后，将返回JSON格式的数据。

| 参数      | 类型     | 说明                   |
|:--------|:-------|:---------------------|
| code    | Number | 结果代码，200 表示成功，其他表示失败 |
| message | String | 结果信息，如 "成功"          |
| data    | Array  | 返回数据                 |

**报文样例**

* **请求 (Request):**
  POST /idc/waterElectricity
  Content-Type: application/json

  {
  "callId": "510000",
  "timeStamp": 1744618177448,
  "signStr": "a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6",
  "operateType": "Add",
  "data": \[
  {
  "dateDim": "202505",
  "province": "510000",
  "province\_name": "四川省",
  "city": "510001",
  "city\_name": "成都分公司",
  "dc\_id": "SC\_CD\_001",
  "dc\_name": "成都XX数据中心",
  "pue\_act": "1.45",
  "water\_consumption": "150.5",
  "water\_bill": "752.50",
  "electricity\_consumption": "350000",
  "electricity\_bill": "280000.00"
  }
  \]
  }
* **返回 (Response):**
  {
  "code": 200,
  "message": "成功",
  "data": \[\]
  }

#### **2.1.3 同步时间及频率**

* 每月定时上报1次。
* **每月上报时，需确保《附件三》中列出的所有市州分公司的数据均有上报，不可遗漏。**
* 数据如有漏报，可按需调用补充上报。

## **附件**

### **附件一：省份编码 (部分)**

| 所属省份    | 省份编码       |
|:--------|:-----------|
| **四川省** | **510000** |
| 北京市     | 110000     |
| 上海市     | 310000     |
| 广东省     | 440000     |
| 重庆市     | 500000     |

### **附件二：签名方式**

签名串通过MD5加密生成。

**生成规则:**

String expectedSign \= DigestUtils.md5Hex("timeStamp:" \+ timestamp \+ "\&idcAppKey:" \+ idcAppKey \+ "\&funcName:" \+ requestURI \+ idcAppSecret);

**参数说明:**

* timestamp: 请求体中的 timeStamp 参数值。
* idcAppKey: 签约申请时分配的Key。
* idcAppSecret: 签约申请时分配的密钥 (Secret)。
* requestURI: 接口的资源地址，固定为 idc/waterElectricity。

### **附件三：四川省地市组织机构编码**

city 和 city\_name 字段必须严格按照下表 (power\_city\_organization\_idc) 的 org\_code 和 org\_name 进行填充。

**表结构:** power\_city\_organization\_idc

CREATE TABLE \`power\_city\_organization\_idc\` (
\`id\` int(11) NOT NULL AUTO\_INCREMENT COMMENT '自增主键',
\`org\_name\` varchar(10) NOT NULL COMMENT '组织名称',
\`org\_code\` varchar(10) NOT NULL COMMENT '格式化后的组织代码',
PRIMARY KEY (\`id\`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='对接集团IDC电量机构表';

**数据样例:**

| org\_name | org\_code |
|:----------|:----------|
| 成都分公司     | 510001    |
| 自贡分公司     | 510003    |
| 攀枝花分公司    | 510004    |
| 泸州分公司     | 510005    |
| ...       | ...       |

## **技术支持**

* **联系人:** 黄学平
* **电话:** 15951818756
* **邮箱:** <EMAIL>

---

## 备注

每个数据都可以被复写

IDC电表由文件导入，许老师提供

PUE可以参考统计分析的菜单
