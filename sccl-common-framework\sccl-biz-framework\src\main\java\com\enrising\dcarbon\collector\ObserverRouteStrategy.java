package com.enrising.dcarbon.collector;

import java.util.List;

/**
 * 基于观察者模式的路由策略，数据将会通过事件发布，监听器的添加请使用{@link RouteEventManagerInstance#getInstance()}获取事件管理器实例后添加
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-12 11:21
 * @email <EMAIL>
 */
public class ObserverRouteStrategy implements CollectedDatasourceRouteStrategy {
    protected final RouteEventFactory factory;

    /**
     * 创建一个观察者路由策略实例，客户端需要提供发布的事件工厂
     *
     * @param factory 事件工厂
     * <AUTHOR> ^ ▽ ^ *)
     * @date 2023/5/15 16:32
     */
    public ObserverRouteStrategy(RouteEventFactory factory) {
        this.factory = factory;
    }

    @Override
    public void route(List<CollectedDatasource> datasourceList) {
        RouteEvent event = factory.create(datasourceList);
        RouteEventManagerInstance
                .getInstance()
                .publishEventSync(event, event.getClass(), true);
    }
}
