package com.enrising.dcarbon.demo.state;

import com.enrising.dcarbon.demo.state.states.AgendaState;
import com.enrising.dcarbon.demo.state.states.DraftState;
import com.enrising.dcarbon.demo.state.states.FinishedState;
import com.enrising.dcarbon.demo.state.states.ProcessState;
import com.enrising.dcarbon.state.State;
import com.enrising.dcarbon.state.Stateful;
import lombok.Data;

/**
 * 测试的具有状态的基础数据
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-10 10:15
 * @email <EMAIL>
 */
@Data
public class DemoBaseData implements Stateful {
    /**
     * 测试ID
     */
    private Long id;
    /**
     * 测试状态 0-草稿 1-待办 2-审批中 3-生效
     */
    private Integer status;

    @Override
    public State currentState() {
        switch (status) {
            case 0:
                return new DraftState(EventManagerInstance.EVENT_MANGER, this);
            case 1:
                return new AgendaState(EventManagerInstance.EVENT_MANGER, this);
            case 2:
                return new ProcessState(EventManagerInstance.EVENT_MANGER, this);
            case 3:
                return new FinishedState(EventManagerInstance.EVENT_MANGER, this);
        }
        return null;
    }
}
