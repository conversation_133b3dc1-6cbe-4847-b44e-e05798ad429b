package com.enrising.dcarbon.state;

/**
 * 空状态
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-12 9:47
 * @email <EMAIL>
 */
public class EmptyCommonState extends AbstractCommonState implements EmptyState {
    @Override
    public void change() {
    }

    @Override
    public Object getSource() {
        return null;
    }

    @Override
    public StateMachine currentStateMachine() {
        return null;
    }
}
