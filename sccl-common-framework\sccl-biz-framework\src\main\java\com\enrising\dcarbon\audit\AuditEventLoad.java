package com.enrising.dcarbon.audit;

import lombok.Getter;

import java.util.List;
import java.util.Map;

/**
 * 稽核事件发布的负载内容
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-08-04 14:20
 * @email <EMAIL>
 */
@Getter
public class AuditEventLoad {
    /**
     * 当前稽核的对象
     */
    private final Auditable currentAuditableObject;

    /**
     * 当前稽核的结果
     */
    private RefereeResult refereeResult;

    /**
     * 当前用于稽核的数据源
     */
    private Map<Class<? extends RefereeDatasource>, List<RefereeDatasource>> currentAuditDatasource;

    /**
     * 当前稽核发生的异常
     */
    private Throwable throwable;

    /**
     * 是否稽核通过，注意该通过指的是没有异常发生，而不是稽核结果通过
     */
    private final boolean isAuditSuccess;

    /**
     * 节点名称
     */
    private final String nodeName;

    public AuditEventLoad(Auditable currentAuditableObject, RefereeResult refereeResult, Map<Class<? extends RefereeDatasource>, List<RefereeDatasource>> currentAuditDatasource, String nodeName) {
        this.currentAuditableObject = currentAuditableObject;
        this.refereeResult = refereeResult;
        this.currentAuditDatasource = currentAuditDatasource;
        isAuditSuccess = refereeResult != null;
        this.nodeName = nodeName;
    }

    public AuditEventLoad(Auditable currentAuditableObject, String nodeName, Throwable throwable) {
        this.currentAuditableObject = currentAuditableObject;
        this.throwable = throwable;
        isAuditSuccess = false;
        this.nodeName = nodeName;
    }
}
