package com.enrising.dcarbon.audit;


import com.enrising.dcarbon.codec.JsonUtil;

/**
 * 基于JSON的序列化和反序列化器
 *
 * <AUTHOR>
 * @date 2022-12-01 16:06
 * @email <EMAIL>
 */
public class JsonSerializerAndDeserializer implements RefereeContentSerializer<String>, RefereeContentDeserializer<AbstractRefereeContent, String> {
    @Override
    public AbstractRefereeContent deserialize(String content, Class<AbstractRefereeContent> contentType) {
        if (contentType == null || !AbstractRefereeContent.class.isAssignableFrom(contentType)) {
            return null;
        }
        return JsonUtil.jsonString2Instance(content, contentType);
    }

    @Override
    public String serialize(AbstractRefereeContent content) {
        content.ignoreParentAttributes();
        return JsonUtil.pojoToJsonString(content);
    }
}
