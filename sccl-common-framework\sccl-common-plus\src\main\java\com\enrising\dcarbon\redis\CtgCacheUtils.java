package com.enrising.dcarbon.redis;

import com.ctg.itrdc.cache.pool.CtgJedisPool;
import com.ctg.itrdc.cache.pool.ProxyJedis;
import com.enrising.dcarbon.lang.Callback;
import com.enrising.dcarbon.lang.Deserializer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import redis.clients.jedis.Pipeline;
import redis.clients.jedis.Response;
import redis.clients.jedis.ScanParams;
import redis.clients.jedis.ScanResult;

import java.io.Closeable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@ConditionalOnProperty(name = "ctg.cache.enable", havingValue = "true")
@Component
public class CtgCacheUtils {
    public static CtgJedisPool jedisPool;
    public final static String CTG_CACHE = "ctg.cache.enable";
    public final static long DEFAULT_EXPIRE = 60 * 60 * 24;

    @Autowired
    public void setCtgJedisPool(CtgJedisPool pool) {
        CtgCacheUtils.jedisPool = pool;
    }

    //如果没有连接成功 尝试连接的次数 文档里面有说明 有时候异常是前置机的问题 多连几次有可能能连上
    private static int reTryCount = 3;

    /**
     * 获取ctg jedis客户端对象
     *
     * @return
     */
    private static ProxyJedis getProxyJedis() {
        ProxyJedis jedis = null;
        for (int i = 0; i < reTryCount; i++) {
            try {
                jedis = jedisPool.getResource();
                if (jedis != null) break;
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return jedis;
    }

    /**
     * 根据key返回值
     *
     * @param key
     * @return
     */
    public static String ctgGet(String key) {
        ProxyJedis jedis = null;
        String value = null;
        try {
            jedis = getProxyJedis();
            value = jedis.get(key);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                jedis.close();
            } catch (Throwable e) {
            }
        }
        return value;
    }

    /**
     * 判断key是否存在
     *
     * @param key
     * @return boolean
     * @Title: ctgHasKey
     * @author: UFO
     * @date: 2020年4月24日 下午4:05:01
     * @throws:
     */
    public static boolean ctgHasKey(String key) {
        ProxyJedis jedis = null;
        boolean hasKey = false;
        try {
            jedis = getProxyJedis();
            hasKey = jedis.exists(key);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                jedis.close();
            } catch (Throwable e) {
            }
        }
        return hasKey;
    }

    /**
     * 根据key删除值
     *
     * @param key
     * @return
     */
    public static Long ctgDel(String key) {
        ProxyJedis jedis = null;
        Long value = 0l;
        try {
            jedis = getProxyJedis();
            if (jedis.exists(key)) value = jedis.del(key);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                jedis.close();
            } catch (Throwable e) {
            }
        }
        return value;
    }

    public static Long ctgDelKes(String[] keys) {
        ProxyJedis jedis = null;
        Long value = 0l;
        try {
            jedis = getProxyJedis();
            value = jedis.del(keys);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                jedis.close();
            } catch (Throwable e) {
            }
        }
        return value;
    }

    /**
     * 存值
     *
     * @param key
     * @param value
     * @param expire 有效期时间 单位秒
     */
    public static String ctgSet(String key, String value, long expire) {
        try (ProxyJedis jedis = getProxyJedis()) {
            /*
			 * 该方法是： 存储数据到缓存中，并制定过期时间和当Key存在时是否覆盖。
				nxxx： 只能取NX或者XX，如果取NX，则只有当key不存在是才进行set，如果取XX，则只有当key已经存在时才进行set
				expx： 只能取EX或者PX，代表数据过期时间的单位，EX代表秒，PX代表毫秒。
				time： 过期时间，单位是expx所代表的单位。
			 * */
            boolean keyExist = jedis.exists(key);
            // NX是不存在时才set， XX是存在时才set， EX是秒，PX是毫秒
            if (keyExist) {
                jedis.del(key);
            }

		    /*SetParams params=new SetParams();
		    params.px(expire);
		    params.nx();
		    return jedis.set(key, value, params);*/
            return jedis.set(key, value, "NX", "EX", expire);
        } catch (Exception e) {
            e.printStackTrace();
            return "0";
        }
    }


    /**
     * 执行管道
     *
     * @param callback 回调
     * @return java.util.List<java.lang.Object>
     * <AUTHOR> ^ ▽ ^ *)
     * @date 2023/7/13 14:37
     */
    public List<Object> executePipeline(Callback<Pipeline> callback) {
        Pipeline pipeline = null;
        List<Object> res = new ArrayList<>();
        try (ProxyJedis jedis = getProxyJedis()) {
            pipeline = jedis.pipelined();
            if (pipeline != null) {
                callback.callback(pipeline);
                Response<List<Object>> response = pipeline.exec();
                res.addAll(response.get());
            } else {
                throw new NullPointerException("open pipeline error");
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeQuietly(pipeline);
        }
        return res;
    }

    public void scanBinaryKeys(Callback<List<byte[]>> callback, int limit) {
        try (ProxyJedis jedis = getProxyJedis()) {
            ScanParams scanParams = new ScanParams().count(limit);
            byte[] cursor = ScanParams.SCAN_POINTER_START_BINARY;
            ScanResult<byte[]> scanResult;
            do {
                scanResult = jedis.scan(cursor, scanParams);
                List<byte[]> scannedKeys = scanResult.getResult();
                if (callback != null) {
                    try {
                        callback.callback(scannedKeys);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                cursor = scanResult.getCursorAsBytes();
            } while (!Arrays.equals(cursor, ScanParams.SCAN_POINTER_START_BINARY));
        }
    }

    public void scanStringKeys(Callback<List<String>> callback, int limit) {
        try (ProxyJedis jedis = getProxyJedis()) {
            ScanParams scanParams = new ScanParams().count(limit);
            String cursor = ScanParams.SCAN_POINTER_START;
            ScanResult<String> scanResult;
            do {
                scanResult = jedis.scan(cursor, scanParams);
                List<String> scannedKeys = scanResult.getResult();
                if (callback != null) {
                    try {
                        callback.callback(scannedKeys);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                cursor = scanResult.getStringCursor();
            } while (!cursor.equals(ScanParams.SCAN_POINTER_START));
        }
    }

    public void scanPatternKeys(Callback<List<String>> callback, Deserializer<String, byte[]> deserializer, String pattern, int limit) {
        try (ProxyJedis jedis = getProxyJedis()) {
            ScanParams scanParams = new ScanParams()
                    .count(limit)
                    .match(pattern);
            byte[] cursor = ScanParams.SCAN_POINTER_START_BINARY;
            ScanResult<byte[]> scanResult;
            do {
                scanResult = jedis.scan(cursor, scanParams);
                List<byte[]> scannedKeys = scanResult.getResult();
                if (callback != null) {
                    try {
                        callback.callback(scannedKeys
                                .stream()
                                .map(item -> deserializer.deserialize(item, String.class))
                                .collect(Collectors.toList()));
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                cursor = scanResult.getCursorAsBytes();
            } while (!Arrays.equals(cursor, ScanParams.SCAN_POINTER_START_BINARY));
        }
    }


    public static String ctgSet(String key, String value) {
        return ctgSet(key, value, DEFAULT_EXPIRE);
    }

    /**
     * 存值
     *
     * @param key
     * @param value
     * @param expire 有效期时间 单位秒
     * @return
     */
    public static String ctgZSet(String key, String value, long expire) {
        try (ProxyJedis jedis = getProxyJedis()) {
            /*
			 * 该方法是： 存储数据到缓存中，并制定过期时间和当Key存在时是否覆盖。
				nxxx： 只能取NX或者XX，如果取NX，则只有当key不存在是才进行set，如果取XX，则只有当key已经存在时才进行set
				expx： 只能取EX或者PX，代表数据过期时间的单位，EX代表秒，PX代表毫秒。
				time： 过期时间，单位是expx所代表的单位。
			 * */
            boolean keyExist = jedis.exists(key);
            // NX是不存在时才set， XX是存在时才set， EX是秒，PX是毫秒
            if (keyExist) {
                jedis.del(key);
            }
            return jedis.set(key, value, "NX", "EX", expire);
        } catch (Exception e) {
            e.printStackTrace();
            return "0";
        }
    }


    public static String ctgZSet(String key, String value) {
        return ctgZSet(key, value, DEFAULT_EXPIRE);
    }

    /**
     * 模糊查询key
     *
     * @param keyPattern
     * @return
     */
    public static List<String> ctgGetAllKey(String keyPattern) {
        try (ProxyJedis jedis = getProxyJedis()) {
            String scanRet = "0";
            // 设置每次scan个数
            int scanCount = 100;
            ScanParams scanParams = new ScanParams();
            scanParams.count(scanCount);
            List<String> retList = new ArrayList<String>();
            do {
                ScanResult<String> ret = jedis.scan(scanRet, scanParams.match(keyPattern));
                scanRet = ret.getStringCursor();//ret.getCursor();
                retList.addAll(ret.getResult());
            } while (!scanRet.equals("0"));

            return retList;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static Long ctgSSet(String key, String[] value) {
        try (ProxyJedis jedis = getProxyJedis()) {
            return jedis.sadd(key, value);
        } catch (Exception e) {
            e.printStackTrace();
            return 0l;
        }
    }

    public static String[] ctgSGet(String key) {
        try (ProxyJedis jedis = getProxyJedis()) {

            return jedis
                    .smembers(key)
                    .toArray(new String[0]);
        } catch (Exception e) {
            e.printStackTrace();
            return new String[0];
        }
    }

    /**
     * 集合是否包含
     *
     * @param key
     * @return
     */
    public static boolean ctgSIsmember(String key, String value) {
        try (ProxyJedis jedis = getProxyJedis()) {
            return jedis.sismember(key, value);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    public static void closeQuietly(Closeable closeable) {
        if (closeable != null) {
            try {
                closeable.close();
            } catch (Exception ignored) {

            }
        }
    }
}
