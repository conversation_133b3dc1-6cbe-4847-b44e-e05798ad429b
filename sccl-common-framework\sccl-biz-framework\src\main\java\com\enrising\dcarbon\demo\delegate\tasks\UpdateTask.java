package com.enrising.dcarbon.demo.delegate.tasks;

import com.enrising.dcarbon.delegate.NamedTask;
import com.enrising.dcarbon.demo.delegate.HandleType;

/**
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-10 15:08
 * @email <EMAIL>
 */
public class UpdateTask implements NamedTask {
    @Override
    public String taskName() {
        return HandleType.UPDATE.getName();
    }

    @Override
    public void doing() {

    }
}
