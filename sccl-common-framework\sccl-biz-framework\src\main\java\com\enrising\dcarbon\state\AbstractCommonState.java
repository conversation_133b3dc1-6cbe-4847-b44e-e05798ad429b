package com.enrising.dcarbon.state;

/**
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-11 16:28
 * @email <EMAIL>
 */
public abstract class AbstractCommonState implements State {
    private StateMachine fromStateMachine;

    public AbstractCommonState(StateMachine fromStateMachine) {
        this.fromStateMachine = fromStateMachine;
    }

    public AbstractCommonState() {
    }

    public AbstractCommonState setFromStateMachine(StateMachine fromStateMachine) {
        this.fromStateMachine = fromStateMachine;
        return this;
    }

    @Override
    public StateMachine currentStateMachine() {
        return fromStateMachine;
    }
}
