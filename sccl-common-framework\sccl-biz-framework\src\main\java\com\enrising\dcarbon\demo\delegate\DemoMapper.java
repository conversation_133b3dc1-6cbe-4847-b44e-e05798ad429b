package com.enrising.dcarbon.demo.delegate;


import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-10 15:10
 * @email <EMAIL>
 */
public class DemoMapper {
    private static final Map<Long, DemoBaseData> virtualDB = new HashMap<>();

    public int insert(DemoBaseData data) {
        virtualDB.put(data.getId(), data);
        return 1;
    }

    public DemoBaseData query(long id) {
        DemoBaseData data = virtualDB.get(id);
        if (data.getDelFlag() == 1) {
            return null;
        }
        return data;
    }

    public int updateContent(long id, String content) {
        if (!virtualDB.containsKey(id)) {
            return 0;
        }
        query(id).setContent(content);
        return 1;
    }

    public int delete(long id) {
        if (!virtualDB.containsKey(id)) {
            return 0;
        }
        query(id).setDelFlag(1);
        return 1;
    }
}
