package com.enrising.dcarbon.generator;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.generator.config.*;
import com.baomidou.mybatisplus.generator.config.po.TableInfo;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import com.enrising.dcarbon.date.DateUtils;
import com.enrising.dcarbon.manage.BaseEntity;
import com.enrising.dcarbon.manage.ObserverService;
import com.enrising.dcarbon.manage.StatefulBaseEntity;
import com.enrising.dcarbon.manage.StatefulObserverService;
import com.enrising.dcarbon.manage.event.*;
import com.enrising.dcarbon.property.MapParamsBuilder;
import com.jingge.autojob.util.convert.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Mapper;

import java.util.*;

/**
 * 框架生成器
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-29 10:06
 * @email <EMAIL>
 */
@Slf4j
public class FrameworkGenerator {
    private GlobalConfig globalConfig;
    private PackageConfig packageConfig;
    private DataSourceConfig dataSourceConfig;
    private StrategyConfig strategyConfig;
    private ReadableAutoGenerator autoGenerator;
    private String path;
    private String parent;
    private boolean isUseObserverService;
    private boolean isUseStatefulObserverService;
    private Builder builder;

    public static Builder builder() {
        return new Builder();
    }

    public static Builder builder(String url, String username, String password) {
        return new Builder(url, username, password);
    }

    public static void bootstrap() {
        new BootstrapBuilder().bootstrap();
    }

    public static class Builder {

        public Builder(String url, String username, String password) {
            this.url = url;
            this.username = username;
            this.password = password;
        }

        public Builder() {
        }

        /**
         * 生成代码的路径
         */
        protected String path;

        /**
         * 数据库地址
         */
        protected String url = "*************************************************************************************************************************************************";

        protected String username = "ecm";

        protected String schema = "carbon";

        protected String password = "Ecmec2019?!";

        /**
         * 要生成的表名
         */
        protected final List<String> tableNames = new ArrayList<>();

        /**
         * 表前缀列表
         */
        protected final List<String> tablePrefixes = new ArrayList<>();

        /**
         * 作者
         */
        protected String author = "generator";

        /**
         * 父包路径
         */
        protected String parentPackage;

        /**
         * ID生成方式，默认采用雪花算法生成
         */
        protected IdType idType = IdType.ASSIGN_ID;

        /**
         * 是否使用基于观察者模式的无状态的Service实现，和isUseStatefulObserverService同时为true时该选项优先级更高
         */
        protected boolean isUseObserverService;

        /**
         * 是否使用基于观察者模式的有状态的Service实现，和isUseStatefulObserverService同时为true时该选项优先级更高
         */
        protected boolean isUseStatefulObserverService;

        /**
         * 生成的实体日期类型
         */
        protected DateType dateType = DateType.ONLY_DATE;

        /**
         * 数据库字段命名是否规范（下划线分割），不规范的命名生成的实体将会直接映射字段名
         */
        protected boolean isColumnsIrregularNaming = false;

        /**
         * 如果文件存在时是否允许覆盖
         */
        protected boolean isAllowOverwriteAll = false;

        protected boolean isAllowOverwriteMapper = false;

        protected boolean isAllowOverwriteEntity = false;

        protected boolean isAllowOverwriteListeners = false;

        public Builder setPath(String path) {
            this.path = path;
            return this;
        }

        public Builder setSchema(String schema) {
            this.schema = schema;
            return this;
        }

        public Builder setParentPackage(String parentPackage) {
            this.parentPackage = parentPackage;
            return this;
        }

        public Builder setColumnsIrregularNaming(boolean columnsIrregularNaming) {
            isColumnsIrregularNaming = columnsIrregularNaming;
            return this;
        }

        public Builder addTableName(String tableName) {
            this.tableNames.add(tableName);
            return this;
        }

        public Builder setAllowOverwriteEntity(boolean allowOverwriteEntity) {
            isAllowOverwriteEntity = allowOverwriteEntity;
            return this;
        }

        public Builder addTablePrefix(String tablePrefix) {
            this.tablePrefixes.add(tablePrefix);
            return this;
        }

        public Builder setAllowOverwriteListeners(boolean allowOverwriteListeners) {
            isAllowOverwriteListeners = allowOverwriteListeners;
            return this;
        }

        public Builder setIdType(IdType idType) {
            this.idType = idType;
            return this;
        }

        public Builder setDateType(DateType dateType) {
            this.dateType = dateType;
            return this;
        }

        public Builder setUseObserverService(boolean useObserverService) {
            isUseObserverService = useObserverService;
            return this;
        }

        public Builder setUseStatefulObserverService(boolean useStatefulObserverService) {
            isUseStatefulObserverService = useStatefulObserverService;
            return this;
        }

        public Builder setAllowOverwriteMapper(boolean allowOverwriteMapper) {
            isAllowOverwriteMapper = allowOverwriteMapper;
            return this;
        }

        public Builder setAuthor(String author) {
            this.author = author;
            return this;
        }

        public Builder setAllowOverwriteAll(boolean allowOverwriteAll) {
            isAllowOverwriteAll = allowOverwriteAll;
            return this;
        }

        public Builder(String parentPackage) {
            this.parentPackage = parentPackage;
        }

        public FrameworkGenerator build() {
            FrameworkGenerator generator = new FrameworkGenerator();
            GlobalConfig.Builder builder = new GlobalConfig.Builder();
            generator.globalConfig = builder
                    .dateType(dateType)
                    .outputDir(path)
                    .author(author)
                    .build();
            generator.dataSourceConfig = new DataSourceConfig.Builder(url, username, password)
                    .schema(schema)
                    .build();
            StrategyConfig.Builder strategyBuilder = new StrategyConfig.Builder();
            if (isUseObserverService) {
                strategyBuilder
                        .serviceBuilder()
                        .superServiceImplClass(ObserverService.class)
                        .entityBuilder()
                        .superClass(BaseEntity.class);
            } else if (isUseStatefulObserverService) {
                strategyBuilder
                        .serviceBuilder()
                        .superServiceImplClass(StatefulObserverService.class)
                        .entityBuilder()
                        .superClass(StatefulBaseEntity.class);
            } else {
                strategyBuilder
                        .serviceBuilder()
                        .superServiceImplClass(ServiceImpl.class)
                        .entityBuilder()
                        .superClass(BaseEntity.class);
            }
            if (isAllowOverwriteAll) {
                strategyBuilder
                        .entityBuilder()
                        .enableFileOverride()
                        .serviceBuilder()
                        .enableFileOverride()
                        .mapperBuilder()
                        .enableFileOverride()
                        .controllerBuilder()
                        .enableFileOverride();
                isAllowOverwriteListeners = true;
            }
            if (isAllowOverwriteMapper) {
                strategyBuilder
                        .mapperBuilder()
                        .enableFileOverride();
            }
            if (isAllowOverwriteEntity) {
                strategyBuilder
                        .entityBuilder()
                        .enableFileOverride();
            }
            if (isColumnsIrregularNaming) {
                strategyBuilder
                        .entityBuilder()
                        .columnNaming(NamingStrategy.no_change);
            } else {
                strategyBuilder
                        .entityBuilder()
                        .columnNaming(NamingStrategy.underline_to_camel);
            }
            generator.strategyConfig = strategyBuilder
                    .addInclude(tableNames.toArray(new String[0]))
                    .addTablePrefix(tablePrefixes.toArray(new String[0]))

                    .serviceBuilder()
                    .formatServiceFileName("%sService")
                    .formatServiceImplFileName("%sServiceImp")

                    .entityBuilder()
                    .idType(idType)
                    .addSuperEntityColumns("id", "del_flag", "create_time", "update_time")
                    .enableLombok()
                    .enableTableFieldAnnotation()
                    .addIgnoreColumns("idString")
                    .formatFileName("%sEntity")
                    .naming(NamingStrategy.underline_to_camel)

                    .mapperBuilder()
                    .formatMapperFileName("%sMapper")
                    .formatMapperFileName("%sMapper")
                    .enableBaseResultMap()
                    .mapperAnnotation(Mapper.class)
                    .enableBaseColumnList()

                    .controllerBuilder()
                    .enableHyphenStyle()
                    .enableRestStyle()
                    .formatFileName("%sController")
                    .build();
            generator.packageConfig = new PackageConfig.Builder()
                    .parent(parentPackage)
                    .entity("entity")
                    .mapper("mapper")
                    .service("service")
                    .xml("mapper")
                    .build();
            generator.autoGenerator = new ReadableAutoGenerator(generator.dataSourceConfig);
            generator.autoGenerator.global(generator.globalConfig);
            generator.autoGenerator.packageInfo(generator.packageConfig);
            generator.autoGenerator.strategy(generator.strategyConfig);
            generator.builder = this;
            generator.parent = parentPackage;
            generator.path = path;
            generator.isUseObserverService = isUseObserverService;
            generator.isUseStatefulObserverService = isUseStatefulObserverService;
            return generator;
        }
    }

    public static class BootstrapBuilder extends Builder {
        public void bootstrap() {
            while (true) {
                log.info("脚手架引导程序启动");
                if (inputBoolean("是否需要配置数据源，默认数据源：*****************************************")) {
                    url = inputString("请输入数据源地址", true);
                    username = inputString("请输入数据源用户名", true);
                    password = inputString("请输入数据源密码", true);
                    schema = inputString("请输入schema（回车跳过）", false);
                    System.out.println("数据库配置如下：");
                    System.out.println("url：" + url);
                    System.out.println("username：" + username);
                    System.out.println("schema(MySQL请忽略)：" + schema);
                }
                List<String> tables = Arrays.asList(inputString("请输入要生成的表名，多个逗号分割", true).split(","));
                tables.forEach(this::addTableName);
                List<String> prefixes = Arrays.asList(inputString("请输入要生成的表名前缀，多个逗号分割（回车跳过）", false).split(","));
                prefixes.forEach(this::addTablePrefix);
                if (inputBoolean("要生成的表字段命名是否遵守下划线分割规范，不遵守的话实体属性将会直接映射表字段命名，而不会下划线转驼峰，y|n")) {
                    setColumnsIrregularNaming(false);
                }
                String author = inputString("请输入作者名（回车跳过）", false);
                if (!StringUtils.isEmpty(author)) {
                    setAuthor(author);
                }
                String p = System.getProperty("user.dir");
                String path = inputString("请输入生成代码的Maven模块的相对路径，当前路径：" + p, true);
                String full = String.format("%s\\%s\\src\\main\\java", p, path);
                System.out.println("完整路径：" + full);
                setPath(full);
                String parent = inputString("请输入父包名", true);
                setParentPackage(parent);

                setUseObserverService(inputBoolean("是否使用基于观察者模式的 '无状态' Service实现，y|n"));
                if (!isUseObserverService) {
                    setUseStatefulObserverService(inputBoolean("是否使用基于观察者模式的 '有状态' Service实现，y|n"));
                }
                setAllowOverwriteAll(inputBoolean("如果文件存在是否全部覆盖，y|n"));
                if (!isAllowOverwriteAll) {
                    setAllowOverwriteMapper(inputBoolean("如果Mapper文件存在是否覆盖，y|n"));
                }
                if (!isAllowOverwriteAll) {
                    setAllowOverwriteEntity(inputBoolean("如果Entity文件存在是否覆盖，y|n"));
                }
                if (!isAllowOverwriteAll) {
                    setAllowOverwriteListeners(inputBoolean("如果监听器及其配置存在是否覆盖，y|n"));
                }
                if (isAllowOverwriteAll) {
                    if (isUseObserverService || isUseStatefulObserverService) {
                        System.out.println("\n注意！本次生成将会覆盖controller、service、serviceImpl、mapper以及相关处理监听器及其监听器配置");
                    } else {
                        System.out.println("\n注意！本次生成将会覆盖controller、service、serviceImpl、mapper");
                    }
                } else {
                    if (isAllowOverwriteMapper) {
                        System.out.println("\n注意！本次生成将会覆盖mapper");
                    }
                    if (isAllowOverwriteEntity) {
                        System.out.println("\n注意！本次生成将会覆盖entity");
                    }
                    if (isAllowOverwriteListeners) {
                        System.out.println("\n注意！本次生成将会覆盖处理监听器及其配置");
                    }
                }
                boolean flag = inputBoolean("配置完成，是否进行生成，y|n");
                if (flag) {
                    build().generate();
                    break;
                } else {
                    if (!inputBoolean("是否需要重新引导，y|n")) {
                        break;
                    }
                }
            }
            log.info("引导结束，如果需要请重新运行");
        }
    }

    private static String inputString(String reminder, boolean forceNeed) {
        Scanner scanner = new Scanner(System.in);
        try {
            String res = null;
            if (!forceNeed) {
                System.out.println(reminder);
                return scanner.nextLine();
            } else {
                while (true) {
                    System.out.println(reminder);
                    res = scanner.nextLine();
                    if (!StringUtils.isEmpty(res)) {
                        return res;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    private static void generateHandleListeners(TableInfo tableInfo, Builder builder, Class<?>... eventTypes) {
        for (Class<?> eventType : eventTypes) {
            HandleListenerGenerator.Builder generateBuilder = HandleListenerGenerator.builder(eventType);
            generateBuilder
                    .addParam("parentPackage", builder.parentPackage)
                    .addParam("package", builder.parentPackage + ".listener" + (builder.isUseStatefulObserverService ? ".handle" : ""))
                    .addParam("date", DateUtils.getDate())
                    .addParam("path", builder.path)
                    .addParam("datetime", DateUtils.getTime())
                    .addParam("author", builder.author)
                    .addParam("comment", "操作事件" + eventType.getSimpleName() + "的监听器")
                    .addParam("mapperName", tableInfo.getMapperName())
                    .addParam("serviceImplName", tableInfo.getServiceImplName())
                    .addParam("isUseStatefulObserverService", builder.isUseStatefulObserverService)
                    .addParam("isUseObserverService", builder.isUseObserverService)
                    .addParam("listenerName", tableInfo.getEntityName() + eventType.getSimpleName() + "Listener")
                    .addParam("isAllowOverwriteListeners", builder.isAllowOverwriteListeners)
                    .addParam("eventName", eventType.getSimpleName());
            generateBuilder
                    .build()
                    .generate(null);
        }
    }

    private static void generateServiceConfig(TableInfo tableInfo, Builder builder, Class<?>... eventTypes) {
        List<Map<String, Object>> listeners = new ArrayList<>();
        for (Class<?> eventType : eventTypes) {
            listeners.add(MapParamsBuilder
                    .newInstance()
                    .addParam("eventName", eventType.getSimpleName())
                    .addParam("name", tableInfo.getEntityName() + eventType.getSimpleName() + "Listener")
                    .getParams());
        }
        ServiceConfigGenerator.Builder generateBuilder = ServiceConfigGenerator.builder();
        generateBuilder
                .addParam("parentPackage", builder.parentPackage)
                .addParam("package", builder.parentPackage + ".config")
                .addParam("date", DateUtils.getDate())
                .addParam("path", builder.path)
                .addParam("datetime", DateUtils.getTime())
                .addParam("author", builder.author)
                .addParam("comment", "Service" + tableInfo.getServiceImplName() + "的相关监听器配置")
                .addParam("listenerPath", builder.isUseStatefulObserverService ? "listener.handle.*" : "listener.*")
                .addParam("mapperName", tableInfo.getMapperName())
                .addParam("serviceImplName", tableInfo.getServiceImplName())
                .addParam("isUseStatefulObserverService", builder.isUseStatefulObserverService)
                .addParam("isUseObserverService", builder.isUseObserverService)
                .addParam("isAllowOverwriteListeners", builder.isAllowOverwriteListeners)
                .addParam("listeners", listeners);
        generateBuilder
                .build()
                .generate(null);
    }

    public static Boolean inputBoolean(String reminder) {
        String res = inputString(reminder, true);
        if ("n".equalsIgnoreCase(res) || "no".equalsIgnoreCase(res)) {
            return false;
        } else if ("y".equalsIgnoreCase(res) || "yes".equalsIgnoreCase(res)) {
            return true;
        }
        System.out.println("你输入的内容：" + res + "不符合格式，默认为no");
        return false;
    }

    public void generate() {
        log.info("开始生成");
        long start = System.currentTimeMillis();
        if (isUseObserverService) {
            autoGenerator.template(new TemplateConfig.Builder()
                    .service("/template/IObserverService.java")
                    .serviceImpl("/template/ObserverService.java")
                    .controller("/template/Controller.java")
                    .build());
        } else if (isUseStatefulObserverService) {
            autoGenerator.template(new TemplateConfig.Builder()
                    .service("/template/IStatefulObserverService.java")
                    .serviceImpl("/template/StatefulObserverService.java")
                    .entity("/template/StatefulEntity.java")
                    .controller("/template/Controller.java")
                    .build());
        }
        autoGenerator.execute();
        if (isUseObserverService || isUseStatefulObserverService) {
            for (TableInfo info : autoGenerator.getAllTableInfoList()) {
                generateHandleListeners(info, builder, AddOneHandleEvent.class, AddListHandleEvent.class, UpdateByEntityHandleEvent.class, UpdateByMapHandleEvent.class, DelByEntityHandleEvent.class, DelByIdHandleEvent.class, DelByMapHandleEvent.class, DelLogicByEntityHandleEvent.class, DelLogicByIdHandleEvent.class, DelLogicByMapHandleEvent.class);
                generateServiceConfig(info, builder, AddOneHandleEvent.class, AddListHandleEvent.class, UpdateByEntityHandleEvent.class, UpdateByMapHandleEvent.class, DelByEntityHandleEvent.class, DelByIdHandleEvent.class, DelByMapHandleEvent.class, DelLogicByEntityHandleEvent.class, DelLogicByIdHandleEvent.class, DelLogicByMapHandleEvent.class);
            }
        }
        log.info("生成完成，总计用时{}ms，请查看目录：{}，包路径：{}，请检查生成的代码是否有误", System.currentTimeMillis() - start, path + "\\" + parent.replace(".", "\\"), parent);
    }

}
