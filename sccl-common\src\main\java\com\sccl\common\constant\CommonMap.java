package com.sccl.common.constant;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * 通用数据库映射Map数据
 *
 * <AUTHOR>
 */
public class CommonMap
{
    /** 状态编码转换(mysql) */
    public static Map<String, String> javaTypeMap = new HashMap<>(24);
    /** 状态编码转换(oracle) */
    public static Map<String, String> javaTypeMapByOracle = new HashMap<>();
    /** 每个表的公用字段 */
    public static Set<String> commonField = new HashSet<>();

    public static Map<String,String> jdbcTypeMap = new HashMap<>(8);

    static
    {
        initJavaTypeMap();
        initJavaTypeMapByOracle();
        initCommonFaild();
        initJdbcType();
    }

    /**
     * 返回状态映射(mysql)
     */
    public static void initJavaTypeMap()
    {
        javaTypeMap.put("tinyint", "Integer");
        javaTypeMap.put("smallint", "Integer");
        javaTypeMap.put("mediumint", "Integer");
        javaTypeMap.put("int", "Integer");
        javaTypeMap.put("integer", "integer");
        javaTypeMap.put("bigint", "Long");
        javaTypeMap.put("float", "Float");
        javaTypeMap.put("double", "Double");
        javaTypeMap.put("decimal", "BigDecimal");
        javaTypeMap.put("bit", "Boolean");
        javaTypeMap.put("char", "String");
        javaTypeMap.put("varchar", "String");
        javaTypeMap.put("tinytext", "String");
        javaTypeMap.put("text", "String");
        javaTypeMap.put("mediumtext", "String");
        javaTypeMap.put("longtext", "String");
        javaTypeMap.put("time", "Date");
        javaTypeMap.put("date", "Date");
        javaTypeMap.put("datetime", "Date");
        javaTypeMap.put("timestamp", "Date");
        javaTypeMap.put("json","Map<String,String>");
    }


    /**
     * 返回状态映射(oracle)
     */
    public static void initJavaTypeMapByOracle()
    {

        javaTypeMapByOracle.put("VARCHAR", "String");
        javaTypeMapByOracle.put("VARCHAR2", "String");
        javaTypeMapByOracle.put("CHAR", "String");
        javaTypeMapByOracle.put("BLOB", "String");
        javaTypeMapByOracle.put("CLOB", "String");
        javaTypeMapByOracle.put("DATE", "Timestamp");
        javaTypeMapByOracle.put("TIMESTAMP", "Timestamp");
        javaTypeMapByOracle.put("FLOAT", "Float");
        javaTypeMapByOracle.put("DOUBLE", "Double");
        javaTypeMapByOracle.put("LONG", "String");
    }
    /**
     * 初始化每个表应该拥有的公共字段
     * @return void
     * <AUTHOR>
     * @Date 2019/3/4 10:55
     */
    public static void initCommonFaild(){
        commonField.add("id");
        commonField.add("creator_id");
        commonField.add("creator_name");
        commonField.add("create_time");
        commonField.add("update_by_id");
        commonField.add("update_by_name");
        commonField.add("update_time");
        commonField.add("del_flag");
        commonField.add("remark");
    }

    /**
     * oracle数据库 在mybatis中的jdbcType 类型
     * @return void
     * <AUTHOR>
     * @Date 2019/3/4 18:12
     */
    public static void initJdbcType(){
        jdbcTypeMap.put("VARCHAR", "VARCHAR");
        jdbcTypeMap.put("VARCHAR2", "VARCHAR");
        jdbcTypeMap.put("CHAR", "CHAR");
        jdbcTypeMap.put("BLOB", "BLOB");
        jdbcTypeMap.put("CLOB", "CLOB");
        jdbcTypeMap.put("DATE", "TIMESTAMP");
        jdbcTypeMap.put("TIMESTAMP", "TIMESTAMP");
        jdbcTypeMap.put("TIMESTAMP(6)", "TIMESTAMP");
        jdbcTypeMap.put("FLOAT", "FLOAT");
        jdbcTypeMap.put("DOUBLE", "DOUBLE");
        jdbcTypeMap.put("LONG", "NUMERIC");
        jdbcTypeMap.put("NUMBER", "NUMERIC");
    }
}
