package com.enrising.dcarbon.delegate;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 基于任务名称的委派者
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-10 14:51
 * @email <EMAIL>
 */
public class NamedDelegate implements Delegate {
    private final Map<String, TaskHandler> taskContainer = new ConcurrentHashMap<>();

    public NamedDelegate addHandler(String name, TaskHandler handler) {
        taskContainer.put(name, handler);
        return this;
    }

    @Override
    public TaskHandler delegate(Task task) {
        if (task instanceof NamedTask) {
            return taskContainer.get(((NamedTask) task).taskName());
        } else {
            throw new UnsupportedOperationException();
        }
    }


}
