package com.enrising.dcarbon.manage.event;

import com.enrising.dcarbon.manage.BaseEntity;
import com.enrising.dcarbon.manage.WebHandleEvent;

/**
 * 根据实体参数逻辑删除事件
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-10 16:20
 * @email <EMAIL>
 */
public class DelLogicByEntityHandleEvent extends WebHandleEvent<BaseEntity> {
    public DelLogicByEntityHandleEvent() {
        super();
    }

    public DelLogicByEntityHandleEvent(BaseEntity source) {
        super(source);
    }
}
