package com.enrising.dcarbon.collector;

import com.enrising.dcarbon.cron.CronUtil;
import com.enrising.dcarbon.date.DateUtils;
import com.enrising.dcarbon.lang.Refreshable;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

/**
 * 基于cron表达式的触发器
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-12 10:52
 * @email <EMAIL>
 */
@Slf4j
public abstract class CronCollectorTrigger extends AbstractCollectorTrigger implements Refreshable {
    private volatile String cron;
    private long nextTriggeringTime;
    volatile boolean isRunning = false;
    volatile boolean isWaiting = false;

    public CronCollectorTrigger(AbstractCollectorTemplate template, String cron) {
        super(template);
        this.cron = cron;
    }

    public void refresh(String cron) {
        this.cron = cron;
        refresh();
    }

    public void register() {
        refresh();
        CronTrigLoop
                .getInstance()
                .register(this);
    }

    @Override
    public void refresh() {
        nextTriggeringTime = CronUtil
                .next(cron, new Date())
                .getTime();
        nextTriggeringTime = nextTriggeringTime - nextTriggeringTime % 1000;
        log.info("cron触发器刷新成功，下次执行时间：{}", DateUtils.formatDateTime(new Date(nextTriggeringTime)));
    }

    boolean isReachTriggerTime() {
        long in = nextTriggeringTime - System.currentTimeMillis();
        return Math.abs(in) <= 1000;
    }

    @Override
    public final void beforeTrig() {
        isRunning = true;
    }

    @Override
    public final void afterTrig() {
        isRunning = false;
        isWaiting = false;
        refresh();
    }

    @Override
    public final void errorTrig(Throwable throwable) {
        if (throwable != null) {
            throwable.printStackTrace();
        }
        afterTrig();
    }
}
