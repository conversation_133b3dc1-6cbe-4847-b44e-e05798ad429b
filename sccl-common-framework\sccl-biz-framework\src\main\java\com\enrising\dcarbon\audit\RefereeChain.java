package com.enrising.dcarbon.audit;


import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 评判链，注意该链不是线程安全的，稽核请严格通过上下文进行
 *
 * <AUTHOR>
 * @Date 2022/11/10 18:11
 * @Email <EMAIL>
 */
public class Referee<PERSON><PERSON><PERSON> implements Cloneable {
    /**
     * 整个评判链的头
     */
    protected RefereeNode chain;

    protected int size;

    protected String name;

    /**
     * 评判链与线程绑定，任何一个节点都可以通过该值来获取当前评判链的上下文
     */
    public static final InheritableThreadLocal<RefereeChain> currentRefereeChain = new InheritableThreadLocal<>();

    protected AuditContext currentAuditContext;

    protected volatile boolean isFinished = true;

    private RefereeChain() {
    }

    /**
     * 返回一个评判链构造器
     *
     * @return com.sccl.modules.business.stationaudit.framework.referee.RefereeChain.Builder
     * <AUTHOR>
     * @date 2022/11/11 10:45
     */
    public static Builder builder() {
        return new Builder();
    }

    protected void startChainReferee(Auditable auditable) {
        isFinished = false;
        currentRefereeChain.set(this);
        if (chain != null) {
            chain.referee(null, auditable);
        }
        currentRefereeChain.remove();
        isFinished = true;
    }

    /**
     * 是否已完成评判
     *
     * @return boolean
     * <AUTHOR> Yongxiang
     * @date 2022/11/11 9:59
     */
    public boolean isFinished() {
        return isFinished;
    }

    public boolean isEmpty() {
        return chain == null;
    }

    public int size() {
        return size;
    }

    /**
     * 通过节点名查找到某个节点
     *
     * @param nodeName 节点名称
     * @return com.sccl.modules.business.stationaudit.framework.referee.RefereeNode
     * <AUTHOR> Yongxiang
     * @date 2022/11/11 10:53
     */
    public RefereeNode findNode(String nodeName) {
        if (isEmpty()) {
            return null;
        }
        RefereeNode tail = chain;
        do {
            if (nodeName.equals(tail.nodeName)) {
                return tail;
            }
            tail = tail.next;
        } while (tail != null);
        return null;
    }

    /**
     * 收集整条评判链的评判结果，评判结果的收集不一定要在稽核完成后进行
     *
     * @return java.util.List<com.sccl.modules.business.stationaudit.framework.referee.RefereeResult>
     * <AUTHOR> Yongxiang
     * @date 2022/11/11 9:59
     */
    public List<RefereeResult> collectResults() {
        List<RefereeResult> refereeResults = new ArrayList<>();
        if (isEmpty()) {
            return refereeResults;
        }
        RefereeNode tail = chain;
        do {
            refereeResults.add(tail.getRefereeResult());
            tail = tail.next;
        } while (tail != null);
        return refereeResults
                .stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }


    public static class Builder {
        private RefereeNode head;
        private RefereeNode tail;
        private int size;

        /**
         * 添加一个评判节点到评判链尾
         *
         * @param node 要添加的节点
         * @return com.sccl.modules.business.stationaudit.framework.referee.RefereeChain.Builder
         * <AUTHOR> Yongxiang
         * @date 2022/11/11 10:45
         */
        public Builder addRefereeNode(RefereeNode node) {
            if (head == null) {
                head = tail = node;
                size++;
                node.nodeName = size + "";
                return this;
            }
            size++;
            node.nodeName = size + "";
            tail.next(node);
            tail = node;
            return this;
        }

        public RefereeChain build() {
            RefereeChain chain = new RefereeChain();
            chain.chain = head;
            chain.size = this.size;
            return chain;
        }
    }

    void reset() {
        if (chain == null) {
            return;
        }
        RefereeNode tail = chain;
        do {
            tail.reset();
            tail = tail.next;
        } while (tail != null);
    }

    @Override
    public Object clone() throws CloneNotSupportedException {
        RefereeChain chain = (RefereeChain) super.clone();
        chain.chain = (RefereeNode) this.chain.clone();
        return chain;
    }
}
