package com.enrising.dcarbon.demo.collector;

import com.enrising.dcarbon.collector.AbstractCollectedDatasourceHandlerNode;
import com.enrising.dcarbon.collector.AbstractCollectorTemplate;
import com.enrising.dcarbon.collector.CollectedDatasource;

import java.util.List;

/**
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-12 14:09
 * @email <EMAIL>
 */
public class DemoHandler extends AbstractCollectedDatasourceHandlerNode {
    @Override
    public List<CollectedDatasource> doHandle(List<CollectedDatasource> datasourceList) {
        List<DemoDatasource> demoDatasourceList = AbstractCollectorTemplate.convert(datasourceList, DemoDatasource.class);
        for (DemoDatasource item : demoDatasourceList) {
            long id = item.getId();
            item.setId(4L);
            System.out.println("change id " + id + " to " + 4);
        }
        return null;
    }
}
