package com.enrising.dcarbon.demo.collector;

import com.enrising.dcarbon.collector.CollectedDatasource;
import com.enrising.dcarbon.collector.Collector;
import com.enrising.dcarbon.thread.SyncHelper;

import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-12 14:07
 * @email <EMAIL>
 */
public class DemoCollector implements Collector {
    @Override
    public List<CollectedDatasource> collect() {
        List<CollectedDatasource> datasourceList = new LinkedList<>();
        for (long i = 0; i < 10; i++) {
            DemoDatasource demoDatasource = new DemoDatasource(i, 1);
            datasourceList.add(demoDatasource);
            SyncHelper.sleepQuietly(1, TimeUnit.SECONDS);
        }
        return datasourceList;
    }
}
