package com.sccl.framework.common.exception.file;

import com.sccl.framework.common.exception.base.BaseException;

/**
 * TODO
 *
 * <AUTHOR>
 * @Date 2019/2/22 10:48
 */
public class AttachmentsException extends BaseException {
    private static final long serialVersionUID = 1L;

    public AttachmentsException(String code, Object[] args)
    {
        super("attachments", code, args, null);
    }

    public AttachmentsException(String code, String errorMessage)
    {
        super("attachments", code, null, errorMessage);
    }

    public AttachmentsException(String code,Object[] args, String errorMessage)
    {
        super("attachments", code, args, errorMessage);
    }
}
