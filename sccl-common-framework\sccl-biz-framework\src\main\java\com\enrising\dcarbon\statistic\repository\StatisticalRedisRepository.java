package com.enrising.dcarbon.statistic.repository;

import com.enrising.dcarbon.codec.JsonUtil;
import com.enrising.dcarbon.redis.RedisUtil;
import com.enrising.dcarbon.reflect.ObjectUtil;
import com.enrising.dcarbon.regex.RegexUtil;
import com.enrising.dcarbon.statistic.StatisticalIndex;
import com.enrising.dcarbon.statistic.StatisticalRedisValue;
import com.enrising.dcarbon.statistic.codec.StatisticalDeserializer;
import com.enrising.dcarbon.statistic.codec.StatisticalSerializer;
import com.enrising.dcarbon.string.StringUtils;
import com.fasterxml.jackson.databind.json.JsonMapper;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Redis存储库
 *
 * <AUTHOR> Yongxiang
 * @Date 2022/10/25 17:57
 */
public class StatisticalRedisRepository implements StatisticalRepository<StatisticalRedisValue> {
    @Override
    public boolean save(List<StatisticalIndex> statisticalIndices, StatisticalSerializer<StatisticalRedisValue> serializer) {
        return save(statisticalIndices, serializer, 24, TimeUnit.HOURS);
    }

    public boolean save(List<StatisticalIndex> statisticalIndices, StatisticalSerializer<StatisticalRedisValue> serializer, long expiringTime, TimeUnit unit) {
        try {
            List<StatisticalRedisValue> redisValues = statisticalIndices
                    .stream()
                    .map(serializer::serialize)
                    .collect(Collectors.toList());
            Map<String, String> valuesMap = new HashMap<>();
            Map<String, List<String>> bucketMap = new HashMap<>();
            redisValues.forEach(value -> {
                valuesMap.put(value.getKey(), JsonUtil.pojoToJsonString(value));
                String bucketName = value.getKeysBucketName();
                if (!bucketMap.containsKey(bucketName)) {
                    bucketMap.put(bucketName, new ArrayList<>());
                }
                bucketMap
                        .get(bucketName)
                        .add(value.getKey());
            });
            //创建bucket
            for (Map.Entry<String, List<String>> entry : bucketMap.entrySet()) {
                RedisUtil.stringSetPushAll(entry.getKey(), expiringTime, unit, entry.getValue());
            }
            //推送到父bucket
            RedisUtil.stringSetPushAll(StatisticalRedisValue.getParentBucketName(), bucketMap.keySet());
            long pValidTime = RedisUtil.getStringValidTime(StatisticalRedisValue.getParentBucketName());
            if (pValidTime == -1 || pValidTime < unit.toMillis(expiringTime)) {
                RedisUtil.setStringValidTime(StatisticalRedisValue.getParentBucketName(), expiringTime, unit);
            }
            RedisUtil.setList(valuesMap, expiringTime, unit);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    @Override
    public List<StatisticalIndex> query(StatisticalDeserializer<StatisticalRedisValue> deserializer, StatisticalRedisValue query) {
        try {
            List<String> filterKeys = queryKeys(query);
            List<StatisticalRedisValue> values = RedisUtil.getJsonList(filterKeys, StatisticalRedisValue.class);
            return values
                    .stream()
                    .map(deserializer::deserialize)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Collections.emptyList();
    }

    @Override
    public boolean update(StatisticalRedisValue param, StatisticalRedisValue query) {
        try {
            List<String> filterKeys = queryKeys(query);
            List<StatisticalRedisValue> values = RedisUtil.getJsonList(filterKeys, StatisticalRedisValue.class);
            values.forEach(item -> ObjectUtil.mergeObjectFields(param, item));
            RedisUtil.deleteStr(filterKeys.toArray(new String[]{}));
            Map<String, String> valuesMap = new HashMap<>();
            values.forEach(value -> valuesMap.put(value.getKey(), JsonUtil.pojoToJsonString(value)));
            RedisUtil.setList(valuesMap, 24, TimeUnit.HOURS);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }


    @Override
    public boolean delete(StatisticalRedisValue query) {
        try {
            List<String> filterKeys = queryKeys(query);
            RedisUtil.deleteStr(filterKeys.toArray(new String[]{}));
            return true;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    private boolean isQueryPattern(StatisticalRedisValue query) {
        return query != null && ((!StringUtils.isEmpty(query.getGroupAlias()) && query
                .getGroupAlias()
                .contains("*")) || (!StringUtils.isEmpty(query.getOwnerAs()) && query
                .getOwnerAs()
                .contains("*")));
    }

    private List<String> queryKeys(StatisticalRedisValue query) {
        List<String> keys = new ArrayList<>();
        if (isQueryPattern(query)) {
            List<String> bucketNames = new ArrayList<>(RedisUtil.getStringSet(StatisticalRedisValue.getParentBucketName()));
            if (!CollectionUtils.isEmpty(bucketNames)) {
                bucketNames.forEach(bucket -> {
                    keys.addAll(RedisUtil.getStringSet(bucket));
                });
            }
        } else {
            keys.addAll(RedisUtil.getStringSet(query.getKeysBucketName()));
        }
        return keys
                .stream()
                .filter(key -> RegexUtil.isMatch(key, query.getPattern()))
                .collect(Collectors.toList());
    }


}
