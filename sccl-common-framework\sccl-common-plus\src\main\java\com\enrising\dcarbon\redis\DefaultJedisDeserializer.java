package com.enrising.dcarbon.redis;

import com.enrising.dcarbon.lang.Deserializer;
import redis.clients.util.SafeEncoder;

/**
 * Jedis默认的反序列化器实现
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-07-13 15:16
 * @email <EMAIL>
 */
public class DefaultJedisDeserializer implements Deserializer<String, byte[]> {
    @Override
    public String deserialize(byte[] source) {
        return SafeEncoder.encode(source);
    }

    @Override
    public String deserialize(byte[] source, Class<String> type) {
        return SafeEncoder.encode(source);
    }
}
