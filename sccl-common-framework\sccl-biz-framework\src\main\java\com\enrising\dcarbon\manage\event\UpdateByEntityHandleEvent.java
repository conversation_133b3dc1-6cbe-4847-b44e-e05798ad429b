package com.enrising.dcarbon.manage.event;

import com.enrising.dcarbon.manage.BaseEntity;
import com.enrising.dcarbon.manage.WebHandleEvent;

/**
 * 根据实体参数更新事件
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-10 16:18
 * @email <EMAIL>
 */
public class UpdateByEntityHandleEvent extends WebHandleEvent<BaseEntity> {
    public UpdateByEntityHandleEvent() {
        super();
    }

    public UpdateByEntityHandleEvent(BaseEntity source) {
        super(source);
    }
}
