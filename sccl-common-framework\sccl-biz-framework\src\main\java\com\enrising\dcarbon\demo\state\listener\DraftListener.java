package com.enrising.dcarbon.demo.state.listener;

import com.enrising.dcarbon.demo.state.DemoBaseData;
import com.enrising.dcarbon.demo.state.events.DraftEvent;
import com.enrising.dcarbon.observer.EventListener;

/**
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-10 11:09
 * @email <EMAIL>
 */
public class DraftListener implements EventListener<DraftEvent> {
    @Override
    public void onEvent(DraftEvent event) {
        DemoBaseData data = (DemoBaseData) event
                .getSource()
                .getSource();
        System.out.println("监听到生成草稿事件，当前数据状态：" + data.getStatus() + "，该监听器优先级为" + level());
    }
}
