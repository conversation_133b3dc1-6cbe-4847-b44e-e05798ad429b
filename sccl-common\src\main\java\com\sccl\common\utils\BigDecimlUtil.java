package com.sccl.common.utils;


import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.Arrays;

/**
 * Created by wjs on 2022/10/21
 * 如有问题，欢迎提出更正 qq:151******3
 */
public class BigDecimlUtil {

    public static final String BIG_NUM_FMT_COMMA = "#,###,###,###,###,###,##0.00";//千位分隔符 方便查看金额具体大小
    public static final String BIG_NUM_FMT = "##################0.00";//不带千位分隔符
    public static final String BIG_NUM_HUNDRED = "100";//100常量
    public static final int BIG_NUM_SCALE = 2;//保留两位小数

    public BigDecimlUtil() {
    }

    /**
     * 高精度加法
     *
     * @param v1
     * @param v2
     * @return
     */
    public static BigDecimal add(String v1, String v2) {
        BigDecimal b1 = new BigDecimal(v1);
        BigDecimal b2 = new BigDecimal(v2);
        return b1.add(b2);
    }

    /**
     * 高精度减法
     *
     * @param v1
     * @param v2
     * @return
     */
    public static BigDecimal sub(String v1, String v2) {
        BigDecimal b1 = new BigDecimal(v1);
        BigDecimal b2 = new BigDecimal(v2);
        return b1.subtract(b2);
    }

    /**
     * 高精度乘法
     *
     * @param v1
     * @param v2
     * @return
     */
    public static BigDecimal mul(String v1, String v2) {
        BigDecimal b1 = new BigDecimal(v1);
        BigDecimal b2 = new BigDecimal(v2);
        return b1.multiply(b2);
    }

    /**
     * 高精度除法
     *
     * @param v1
     * @param v2
     * @param scale
     * @return
     */
    public static BigDecimal div(String v1, String v2, int scale) {
        if (scale < 0) {
            throw new IllegalArgumentException(
                    "The scale must be a positive integer or zero");
        }
        BigDecimal b1 = new BigDecimal(v1);
        BigDecimal b2 = new BigDecimal(v2);
        return b1.divide(b2, scale, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 保留小数位
     *
     * @param v
     * @param scale
     * @return
     */
    public static BigDecimal round(String v, int scale) {
        if (scale < 0) {
            throw new IllegalArgumentException(
                    "The scale must be a positive integer or zero");
        }
        BigDecimal b = new BigDecimal(v);
        BigDecimal one = new BigDecimal("1");
        return b.divide(one, scale, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 分转换成元
     *
     * @param v
     * @return
     */
    public static BigDecimal penny2dollar(String v) {
        BigDecimal s = div(v, "100", 2);//保留两位小数
        return s;
    }

    /**
     * 元转换成分
     *
     * @param v
     * @return
     */
    public static BigDecimal dollar2penny(String v) {
        return mul(v, "100");
    }

    /**
     * 格式化金额
     * 千位分隔符 方便查看金额具体大小 BIG_NUM_FMT = "#,###,###,###,###,###,##0.00"
     * 精确两位小数 .99 -> 0.99
     * 1111111.985 -> 1,111,111.99
     *
     * @param v
     * @return
     */
    public static String formatNumber(String v) {
        return formatNumber(v, BIG_NUM_FMT_COMMA);
    }

    /**
     * 格式化金额
     *
     * @param v
     * @param pattern BigNum类中的常量 BIG_NUM_FMT_COMMA,BIG_NUM_FMT
     * @return
     */
    public static String formatNumber(String v, String pattern) {
        return new DecimalFormat(pattern).format(new BigDecimal(v));
    }

    public static void main(String[] args) {
        System.out.println(BigDecimlUtil.add("1.99", "20.999"));
        System.out.println(BigDecimlUtil.sub("1.99", "20.999"));
        String s = "1111111.985";
        String a = BigDecimlUtil.formatNumber(s);
        System.out.println(s);
        System.out.println(a);
    }

    public static BigDecimal add(BigDecimal... list) {
        return Arrays.stream(list).reduce(
                BigDecimal.ZERO,
                (a, b) -> {
                    if (b == null) {
                        b = BigDecimal.ZERO;
                    }
                    return a.add(b);
                }
        );

    }

    public static boolean isDiffGreaterThanOrEqualTo(BigDecimal value1, BigDecimal value2, BigDecimal percentage) {
        if (value1 == null || value2 == null || (value2.compareTo(BigDecimal.ZERO) == 0)) {
            return false;
        }
        BigDecimal diffPercentage = value1.subtract(value2).abs()
                .divide(value2, 2, BigDecimal.ROUND_HALF_UP)
                .multiply(new BigDecimal("100"));

        return diffPercentage.compareTo(percentage) >= 0;
    }

    public static boolean checkValue(BigDecimal one, BigDecimal two, BigDecimal three) {
        if (one == null || two == null || three == null) {
            return false;  // 检查是否有 null 值
        }
        return one.compareTo(two) >= 0
                && one.compareTo(three) < 0;
    }

    /**
     * 接收两个string ,输出第一个相对于第二个数的百分比
     *
     * @param num1
     * @param num2
     * @param scale
     * @return
     */
    public static String calculatePercentageDeviation(String num1, String num2, int scale) {
        // 将输入的字符串转换为 BigDecimal
        BigDecimal bdNum1 = new BigDecimal(num1);
        BigDecimal bdNum2 = new BigDecimal(num2);

        // 计算百分比
        BigDecimal deviation = bdNum1.divide(bdNum2, scale + 2, RoundingMode.HALF_UP);
        deviation = deviation.multiply(BigDecimal.valueOf(100));

        // 设置小数位数
        deviation = deviation.setScale(scale, RoundingMode.HALF_UP);

        return deviation.toPlainString() + "%";
    }

    /**
     * 求和，可变的string 参数
     * @param args
     * @return
     */
    public static String calcSum(String... args) {
        BigDecimal sum = BigDecimal.ZERO;
        for (String arg : args) {
            sum = sum.add(new BigDecimal(arg));
        }
        return sum.toString();
    }

}
