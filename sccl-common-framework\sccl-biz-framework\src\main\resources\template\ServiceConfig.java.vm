package ${package};

import com.enrising.dcarbon.manage.event.*;
import ${parentPackage}.${listenerPath};
import ${parentPackage}.service.impl.${serviceImplName};
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * ${comment}
 *
 * <AUTHOR>
 * @date ${date}
 */
@Configuration
public class ${serviceImplName}ServiceConfig {
    @Bean
    public ${serviceImplName} config${serviceImplName}() {
        ${serviceImplName} serviceImp = new ${serviceImplName}();
        serviceImp
                .getWebEventManger()
                #foreach($listener in ${listeners})
                .addListener(${listener.eventName}.class, new ${listener.name}())
                #end;
        return serviceImp;
    }
}