package com.sccl.common.utils;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * Curl日志生成工具类
 * 用于生成HTTP请求对应的curl命令，方便在服务器上调试
 *
 * <AUTHOR>
 * @date 2025年6月26日
 */
public class CurlLogUtil {

    private static final Logger log = LoggerFactory.getLogger(CurlLogUtil.class);

    /**
     * 生成POST请求的curl命令
     *
     * @param url         请求URL
     * @param requestBody 请求体（JSON字符串）
     * @param headers     请求头
     * @return curl命令字符串
     */
    public static String generatePostCurl(String url, String requestBody, Map<String, String> headers) {
        StringBuilder curlCommand = new StringBuilder();
        curlCommand.append("curl -X POST");

        // 添加URL
        curlCommand.append(" '").append(url).append("'");

        // 添加请求头
        if (headers != null && !headers.isEmpty()) {
            for (Map.Entry<String, String> header : headers.entrySet()) {
                curlCommand.append(" -H '").append(header.getKey()).append(": ").append(header.getValue()).append("'");
            }
        }

        // 添加Content-Type（如果没有设置的话）
        if (headers == null || !headers.containsKey("Content-Type")) {
            curlCommand.append(" -H 'Content-Type: application/json'");
        }

        // 添加请求体
        if (StrUtil.isNotBlank(requestBody)) {
            // 转义单引号和反斜杠
            String escapedBody = requestBody.replace("\\", "\\\\").replace("'", "'\"'\"'");
            curlCommand.append(" -d '").append(escapedBody).append("'");
        }

        return curlCommand.toString();
    }

    /**
     * 生成GET请求的curl命令
     *
     * @param url     请求URL
     * @param headers 请求头
     * @return curl命令字符串
     */
    public static String generateGetCurl(String url, Map<String, String> headers) {
        StringBuilder curlCommand = new StringBuilder();
        curlCommand.append("curl -X GET");

        // 添加URL
        curlCommand.append(" '").append(url).append("'");

        // 添加请求头
        if (headers != null && !headers.isEmpty()) {
            for (Map.Entry<String, String> header : headers.entrySet()) {
                curlCommand.append(" -H '").append(header.getKey()).append(": ").append(header.getValue()).append("'");
            }
        }

        return curlCommand.toString();
    }

    /**
     * 从HttpRequest对象生成curl命令
     *
     * @param httpRequest Hutool的HttpRequest对象
     * @return curl命令字符串
     */
    public static String generateCurlFromHttpRequest(HttpRequest httpRequest) {
        if (httpRequest == null) {
            return "";
        }

        String method = httpRequest.getMethod().name();
        String url = httpRequest.getUrl();
        // 注意：hutool的HttpRequest可能没有直接获取body的方法，这里简化处理
        String body = "";
        // hutool的headers()返回的是Map<String, List<String>>，需要转换
        Map<String, String> headers = null;

        if ("POST".equalsIgnoreCase(method) || "PUT".equalsIgnoreCase(method) || "PATCH".equalsIgnoreCase(method)) {
            return generatePostCurl(url, body, headers);
        } else {
            return generateGetCurl(url, headers);
        }
    }

    /**
     * 记录curl日志
     *
     * @param description 描述信息
     * @param url         请求URL
     * @param requestBody 请求体
     * @param headers     请求头
     */
    public static void logPostCurl(String description, String url, String requestBody, Map<String, String> headers) {
        String curlCommand = generatePostCurl(url, requestBody, headers);
        log.info("{}的curl命令: {}", description, curlCommand);
    }

    /**
     * 记录curl日志（从HttpRequest对象）
     * 注意：由于hutool API限制，建议使用其他方法
     *
     * @param description 描述信息
     * @param httpRequest HttpRequest对象
     */
    public static void logCurlFromHttpRequest(String description, HttpRequest httpRequest) {
        String curlCommand = generateCurlFromHttpRequest(httpRequest);
        log.info("{}的curl命令: {}", description, curlCommand);
    }

    /**
     * 记录curl日志（简化版本，只需要URL和请求体）
     *
     * @param description 描述信息
     * @param url         请求URL
     * @param requestBody 请求体对象（会自动转换为JSON）
     */
    public static void logPostCurl(String description, String url, Object requestBody) {
        String requestJson = JSONUtil.toJsonStr(requestBody);
        String curlCommand = generatePostCurl(url, requestJson, null);
        log.info("{}的curl命令: {}", description, curlCommand);
    }

    /**
     * 记录curl日志（带超时设置）
     *
     * @param description    描述信息
     * @param url            请求URL
     * @param requestBody    请求体
     * @param headers        请求头
     * @param timeoutSeconds 超时时间（秒）
     */
    public static void logPostCurlWithTimeout(String description, String url, String requestBody,
                                              Map<String, String> headers, int timeoutSeconds) {
        String curlCommand = generatePostCurl(url, requestBody, headers);
        // 添加超时参数
        curlCommand += " --connect-timeout " + timeoutSeconds + " --max-time " + timeoutSeconds;
        log.info("{}的curl命令（含超时设置）: {}", description, curlCommand);
    }

    /**
     * 记录GET请求的curl日志
     *
     * @param description 描述信息
     * @param url         请求URL
     * @param headers     请求头
     */
    public static void logGetCurl(String description, String url, Map<String, String> headers) {
        String curlCommand = generateGetCurl(url, headers);
        log.info("{}的curl命令: {}", description, curlCommand);
    }

    /**
     * 记录GET请求的curl日志（简化版本）
     *
     * @param description 描述信息
     * @param url         请求URL
     */
    public static void logGetCurl(String description, String url) {
        logGetCurl(description, url, null);
    }
}
