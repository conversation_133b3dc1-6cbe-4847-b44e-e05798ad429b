package com.enrising.dcarbon.collector;

import java.util.List;

/**
 * 路由策略上下文
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-05 11:30
 * @email <EMAIL>
 */
public class CollectedDatasourceRouteStrategyContext {
    private final CollectedDatasourceRouteStrategy strategy;

    public CollectedDatasourceRouteStrategyContext(CollectedDatasourceRouteStrategy strategy) {
        this.strategy = strategy;
    }

    public void execute(List<CollectedDatasource> datasourceList) {
        strategy.beforeRoute(datasourceList);
        strategy.route(datasourceList);
        strategy.afterRoute(datasourceList);
    }

}
