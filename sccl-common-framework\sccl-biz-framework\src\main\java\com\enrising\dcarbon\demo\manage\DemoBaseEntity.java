package com.enrising.dcarbon.demo.manage;

import com.enrising.dcarbon.manage.BaseEntity;
import com.enrising.dcarbon.state.State;
import com.enrising.dcarbon.state.Stateful;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-11 9:21
 * @email <EMAIL>
 */
@Getter
@ToString
@Setter
public class DemoBaseEntity extends BaseEntity implements Stateful {
    private Integer status;

    @Override
    public State currentState() {
        return null;
    }
}
