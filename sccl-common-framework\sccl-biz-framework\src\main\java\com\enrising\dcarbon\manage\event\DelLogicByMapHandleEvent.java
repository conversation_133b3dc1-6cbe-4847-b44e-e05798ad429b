package com.enrising.dcarbon.manage.event;

import com.enrising.dcarbon.manage.WebHandleEvent;

import java.util.Map;

/**
 * 根据MAP参数逻辑删除事件
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-11 9:30
 * @email <EMAIL>
 */
public class DelLogicByMapHandleEvent extends WebHandleEvent<Map<String, Object>> {
    public DelLogicByMapHandleEvent() {
        super();
    }

    public DelLogicByMapHandleEvent(Map<String, Object> source) {
        super(source);
    }
}
