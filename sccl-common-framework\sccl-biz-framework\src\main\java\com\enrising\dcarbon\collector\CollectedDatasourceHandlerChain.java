package com.enrising.dcarbon.collector;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-06-05 16:12
 * @email <EMAIL>
 */
public class CollectedDatasourceHandlerChain {
    private final List<AbstractCollectedDatasourceHandlerNode> handlers = new ArrayList<>();

    private CollectedDatasourceHandlerChain(List<AbstractCollectedDatasourceHandlerNode> handlers) {
        this.handlers.addAll(handlers);
    }

    public static Builder builder() {
        return new Builder();
    }

    public List<CollectedDatasource> execute(List<CollectedDatasource> datasourceList) {
        for (AbstractCollectedDatasourceHandlerNode handler : handlers) {
            List<CollectedDatasource> handled = handler.handle(datasourceList);
            datasourceList = handled == null ? datasourceList : handled;
            if (!handler.spreadNext(datasourceList)) {
                break;
            }
        }
        return datasourceList;
    }

    public static class Builder {
        private final List<AbstractCollectedDatasourceHandlerNode> handlers = new ArrayList<>();

        public Builder addHandler(AbstractCollectedDatasourceHandlerNode handler) {
            handlers.add(handler);
            return this;
        }

        public CollectedDatasourceHandlerChain build() {
            return new CollectedDatasourceHandlerChain(handlers);
        }
    }
}
