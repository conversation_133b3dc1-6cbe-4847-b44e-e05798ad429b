package com.enrising.dcarbon.state;

import com.enrising.dcarbon.observer.EventManger;

/**
 * 使用观察者模式的抽象状态类，该类状态的改变将会直接发布状态改变事件，每个状态改变事件可以由多个监听器处理
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-09 16:37
 * @email <EMAIL>
 */
public abstract class AbstractObserverState implements State {
    private EventManger eventManger;
    private StateMachine fromStateMachine;

    /**
     * 观察者模式的抽象状态类必须提供一个事件管理器实例
     *
     * @param eventManger 事件管理器实例
     * <AUTHOR> ^ ▽ ^ *)
     * @date 2023/5/10 13:47
     */
    public AbstractObserverState(EventManger eventManger) {
        this.eventManger = eventManger;
    }

    public AbstractObserverState() {
    }

    public void setEventManger(EventManger eventManger) {
        this.eventManger = eventManger;
    }

    public EventManger getEventManger() {
        return eventManger;
    }

    public AbstractObserverState setFromStateMachine(StateMachine fromStateMachine) {
        this.fromStateMachine = fromStateMachine;
        return this;
    }

    /**
     * 子类应该说明该状态改变会发布何种事件，这里返回事件工厂，工厂里负责事件的构造
     *
     * @return com.enrising.dcarbon.state.StateEventFactory
     * <AUTHOR> ^ ▽ ^ *)
     * @date 2023/5/10 10:46
     */
    public abstract StateEventFactory stateEventFactory();

    /**
     * 状态改变事件是否冒泡，默认是
     *
     * @return boolean
     * <AUTHOR> ^ ▽ ^ *)
     * @date 2023/5/9 16:46
     */
    public boolean allowEventBubble() {
        return true;
    }

    @Override
    public void change() {
        if (eventManger == null) {
            throw new NullPointerException("事件管理器不得为空");
        }
        StateChangeEvent event = stateEventFactory().create(new ReadonlyState(this).setFromStateMachine(fromStateMachine));
        eventManger.publishEventSync(event, event.getClass(), allowEventBubble());
    }

    @Override
    public StateMachine currentStateMachine() {
        return fromStateMachine;
    }
}
