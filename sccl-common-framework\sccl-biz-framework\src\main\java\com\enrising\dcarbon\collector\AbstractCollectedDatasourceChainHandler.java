package com.enrising.dcarbon.collector;

import com.enrising.dcarbon.servlet.ServletUtil;
import com.jingge.autojob.util.message.MessageManager;

import java.util.List;

/**
 * 抽象收集数据处理器链
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-05 11:03
 * @email <EMAIL>
 */
public abstract class AbstractCollectedDatasourceChainHandler {
    protected AbstractCollectedDatasourceChainHandler chain;

    protected AbstractCollectedDatasourceChainHandler() {
    }

    private void next(AbstractCollectedDatasourceChainHandler handler) {
        this.chain = handler;
    }

    public synchronized AbstractCollectedDatasourceChainHandler add(AbstractCollectedDatasourceChainHandler handler) {
        AbstractCollectedDatasourceChainHandler tail = null;
        for (tail = this; tail.chain != null; ) {
            tail = tail.chain;
        }
        tail.chain = handler;
        tail = handler;
        return tail;
    }

    public static Builder builder() {
        return new Builder();
    }

    /**
     * 对收集到的数据进行处理，该方法提供了返回值，如果仅仅对收集到的数据某些字段做了处理可以直接返回null，如果基于该收集到的数据重新生成了一批数据请返回，为了后续的处理节点能够使用最新的数据请你在调用{@link #spreadIfExistNext(List)}方法向下传播时使用最新的数据作为参数
     *
     * @param datasourceList 收集到的数据源
     * @return java.util.List<com.sccl.collector.CollectedDatasource>
     * <AUTHOR> ^ ▽ ^ *)
     * @date 2023/5/5 11:50
     */
    public abstract List<CollectedDatasource> doHandle(List<CollectedDatasource> datasourceList);

    public void afterHandle(List<CollectedDatasource> datasourceList) {

    }

    /**
     * 是否继续向后续节点传播
     *
     * @return boolean
     * <AUTHOR> ^ ▽ ^ *)
     * @date 2023/5/16 15:22
     */
    public boolean isSpread(List<CollectedDatasource> datasourceList) {
        return true;
    }

    /**
     * 向与当前线程绑定的响应写入消息，可以使用{@link MessageManager}构建消息
     *
     * @param msg 要写入的消息
     * @return void
     * <AUTHOR> ^ ▽ ^ *)
     * @date 2023/5/16 15:25
     */
    public void writeResponse(String msg) {
        ServletUtil.renderString(msg);
    }

    /**
     * 如果链存在下一个节点且{@link #isSpread(List)}返回true，继续向下传播
     *
     * @param datasourceList 本节点处理完后的数据
     * @return void
     * <AUTHOR> ^ ▽ ^ *)
     * @date 2023/5/5 12:30
     */
    public void spreadIfExistNext(List<CollectedDatasource> datasourceList) {
        try {
            afterHandle(datasourceList);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (chain != null && isSpread(datasourceList)) {
            chain.doHandle(datasourceList);
        }
    }

    public static class Builder {
        private AbstractCollectedDatasourceChainHandler head;
        private AbstractCollectedDatasourceChainHandler tail;

        public synchronized Builder addHandler(AbstractCollectedDatasourceChainHandler handler) {
            if (handler == null) {
                return this;
            }
            if (head == null) {
                head = tail = handler;
                return this;
            }
            tail.next(handler);
            tail = handler;
            return this;
        }

        public AbstractCollectedDatasourceChainHandler build() {
            return head;
        }
    }
}
