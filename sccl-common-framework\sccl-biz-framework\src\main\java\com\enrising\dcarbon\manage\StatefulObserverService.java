package com.enrising.dcarbon.manage;

import com.enrising.dcarbon.servlet.ServletUtil;
import com.enrising.dcarbon.state.AbstractObserverState;
import com.enrising.dcarbon.state.ObserverStateMachine;

/**
 * 有状态的基于观察者模式的Service实现，使用状态机管理有状态的基础数据
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-11 14:22
 * @email <EMAIL>
 */
public class StatefulObserverService<T extends StatefulBaseEntity> extends ObserverService<T> {
    /**
     * 状态事件管理器，将会通过操作事件进行发布，监听器可以利用其构建状态机处理相关状态
     */
    protected final ResponsiveEventManager stateEventManager = new ResponsiveEventManager();

    public ObserverStateMachine newStateMachine() {
        return new ObserverStateMachine(stateEventManager);
    }

    public ObserverStateMachine newStateMachine(AbstractObserverState initialState) {
        return new ObserverStateMachine(initialState, stateEventManager);
    }

    public ResponsiveEventManager getStateEventManager() {
        return stateEventManager;
    }

    @Override
    protected void publishHandleEvent(WebHandleEvent<?> event) {
        ServletUtil.bind();
        event.setStateEventManager(stateEventManager);
        event.setWebEventManager(responsiveEventManager);
        responsiveEventManager.publishEventSync(event, event.getClass(), true);
    }
}
