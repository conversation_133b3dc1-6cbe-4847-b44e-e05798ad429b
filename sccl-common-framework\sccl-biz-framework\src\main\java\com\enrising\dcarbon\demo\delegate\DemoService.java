package com.enrising.dcarbon.demo.delegate;

import com.enrising.dcarbon.delegate.NamedDelegate;
import com.jingge.autojob.util.bean.ObjectUtil;

/**
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-10 15:10
 * @email <EMAIL>
 */
public class DemoService {
    private static NamedDelegate DELEGATE = new NamedDelegate();

    static {

    }

    public void doing(DemoBaseData data, HandleType type) {
        if (ObjectUtil.isNull(DELEGATE)) {
            DELEGATE = new NamedDelegate();
        }
        DELEGATE.delegate(type.getTask());
    }
}
