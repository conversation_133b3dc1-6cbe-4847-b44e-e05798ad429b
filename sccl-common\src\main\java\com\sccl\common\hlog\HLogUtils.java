package com.sccl.common.hlog;

import com.sccl.common.utils.JsonUtil;
import com.sccl.common.utils.ServletUtils;
import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public final class HLogUtils {
    public enum LogType {
        /**
         * 登录日志
         */
        BUSILOG_TYPE_LOGIN("busisec_login", "登录日志"),
        /**
         * 操作日志
         */
        BUSILOG_TYPE_OPERATION("busisec_operation", "操作日志"),
        /**
         * INFO级别日志
         */
        CUSTOMLOG_TYPE_INFO("custsec_info", "INFO级别日志"),
        /**
         * DEBUG级别日志
         */
        CUSTOMLOG_TYPE_DEBUG("custsec_debug", "DEBUG级别日志"),
        /**
         * WARN级别日志
         */
        CUSTOMLOG_TYPE_WARN("custsec_warn", "WARN级别日志"),
        /**
         * ERROR级别日志
         */
        CUSTOMLOG_TYPE_ERROR("custsec_error", "ERROR级别日志");
        private String flag;
        private String description;

        public String getFlag() {
            return flag;
        }

        public void setFlag(String flag) {
            this.flag = flag;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        LogType(String flag, String description) {
            this.flag = flag;
            this.description = description;
        }
    }

    /*
     * 系统编码
     * 系统编码主要承担唯一识别代码用途，一但确定后编码将不再变化。编码共 16 位数字，具体编码规则如下：
     * 第 1 位：J-集团、S-省内（承建部门）
     * 第 2-3 位：表示该系统区域，区域编码见市州编码表
     * 第 4 位：1-合作、0-自营、X其它
     * 第 5 位：Q-前端系统（面向营销）、B-后端系统（维护支撑）、A-管控系统（财务、能力）
     * 第 6 位：部署位置，N-内网、W-外网、X-混合
     * 第 7-16 位：由系统自动生产序号
     */
    public static final String SYS_CODE = "S000AN2021012048";


    private static String genSerialNbr() {
        String timestamp = String.valueOf(System.currentTimeMillis());
        //取后10位保证1秒内不重复
        String subfix = timestamp.substring(timestamp.length() - 10);
        Date date = new Date();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        return simpleDateFormat.format(date) + subfix;
    }

    private static String genLogTime() {
        Date date = new Date();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        return simpleDateFormat.format(date);
    }


    /**
     * 写登录日志
     *
     * @param remoteIp    终端IP Ipv4/Ipv6
     * @param account     用户账号，手机号
     * @param loginTime   登录时间 yyyy-MM-dd HH:mm:ss.SSS格式
     * @param loginStatus 登录状态 0/1
     * @return void
     * <AUTHOR> Yongxiang
     * @date 2022/1/5 9:28
     */
    private static void writeLoginLog(String remoteIp, String account, String loginTime, String loginStatus) {
        Map<String, Object> logData = new HashMap<>();
        String logAppId = "QX661449_scnh_NHF_sys_PA";
        String server = "sysPA";
        logData.put("logAppId", logAppId); //日志全局唯一id
        logData.put("server", server); //服务组别名
        logData.put("sysCode", SYS_CODE);
        logData.put("serialNbr", genSerialNbr());
        logData.put("remoteIp", remoteIp);
        logData.put("account", account);
        logData.put("userCode", account);
        logData.put("loginTime", loginTime);
        logData.put("loginStatus", loginStatus);
        logData.put("authType", "11");
        LogHelper.writeBusisecLog(LogType.BUSILOG_TYPE_LOGIN.flag, logData);
        //解决非HLog环境日志输出
        log.info(JsonUtil.getMapJson(logData));
    }

    /**
     * 写登录日志
     *
     * @param account     用户账号，手机号
     * @param loginStatus 登录状态 0/1
     * @return void
     * <AUTHOR> Yongxiang
     * @date 2022/1/5 9:50
     */
    public static void writeLoginLog(String account, String loginStatus) {
        writeLoginLog(ServletUtils.getRequestIp(), account, genLogTime(), loginStatus);
    }

    /**
     * 写操作日志
     *
     * @param terminalInfo 终端类型  移动端为设备的 IMEI或电脑MAC 地址
     * @param admin        管理员标识 1是 0否
     * @param opeType      业务操作 add：新增；up：更新；del:删除；sel:查询
     * @param transId      工单号 ITSM单号 资源ID
     * @param specId       资源specId
     * @param hrcode       人员编码
     * @param userName     用户姓名
     * @param remoteIp     操作IP地址
     * @param requestIp    请求URI
     * @param statusCd     状态 0 开始操作 1操作中 2操作完成
     * @param content      操作内容
     * @param localnetId   本地网ID
     * @return void
     * <AUTHOR> Yongxiang
     * @date 2022/1/5 10:37
     */
    public static void writeOperateLog(String terminalInfo, String admin, String opeType, String transId, String specId, String hrcode, String userName, String remoteIp, String requestIp, String statusCd, String content, String localnetId) {
        Map<String, Object> logData = new HashMap<String, Object>();
        logData.put("serialNbr", genSerialNbr());
        logData.put("terminalInfo", terminalInfo);
        logData.put("admin", admin);
        logData.put("opeType", opeType);
        logData.put("transId", transId);
        logData.put("resId", transId);
        logData.put("specId", specId);
        logData.put("opeTime", genLogTime());
        logData.put("hrcode", hrcode);
        logData.put("userName", userName);
        logData.put("remoteIp", remoteIp);
        logData.put("requestIp", requestIp);
        logData.put("statusCd", statusCd);
        logData.put("content", content);
        logData.put("localnetId", localnetId);
        LogHelper.writeBusisecLog(LogType.BUSILOG_TYPE_OPERATION.flag, logData);
        //解决非HLog环境日志输出
        log.info(JsonUtil.getMapJson(logData));
    }

    /**
     * 写操作日志
     *
     * @param HLogUser   操作用户
     * @param opeType    业务操作类型 add：新增；up：更新；del:删除；sel:查询
     * @param resId      工单号，资源ID
     * @param specId     资源specId
     * @param content    操作内容
     * @param statusCd   状态 0 开始操作 1操作中 2操作完成
     * @param localnetId 本地网ID
     * @return void
     * <AUTHOR> Yongxiang
     * @date 2022/1/5 10:31
     */
    public static void writeOperateLog(HLogUser HLogUser, String opeType, String resId, String specId, String content, String statusCd, String localnetId) {
        if (!Arrays.asList(new String[]{"add", "up", "del", "sel"}).contains(opeType)) {
            log.error("业务操作类型指定错误");
            return;
        }
        if (HLogUser == null) {
            log.error("写操作日志失败，无指定用户");
            return;
        }
        writeOperateLog("", HLogUser.isAdmin() ? "1" : "0", opeType, resId, specId, HLogUser.getHrLoginId(), HLogUser.getUserName(), ServletUtils.getRequestIp(), ServletUtils.getRequestUrl(), statusCd, content, localnetId);
    }


    /**
     * INFO日志
     *
     * @param content 日志内容
     * @return void
     * <AUTHOR> Yongxiang
     * @date 2022/1/5 10:35
     */
    public static void writeResLogInfo(HLogUser HLogUser, String content, String localnetId) {
        Map<String, Object> logData = new HashMap<String, Object>();
        logData.put("content", content);
        writeResLog(HLogUser, LogType.CUSTOMLOG_TYPE_INFO, logData, localnetId);
    }

    public static void writeResLogDebug(HLogUser HLogUser, String content, String localnetId) {
        Map<String, Object> logData = new HashMap<String, Object>();
        logData.put("content", content);
        writeResLog(HLogUser, LogType.CUSTOMLOG_TYPE_DEBUG, logData, localnetId);
    }


    public static void writeResLogError(HLogUser HLogUser, String content, String localnetId) {
        Map<String, Object> logData = new HashMap<>();
        logData.put("content", content);
        writeResLog(HLogUser, LogType.CUSTOMLOG_TYPE_ERROR, logData, localnetId);
    }


    public static void writeResLogWarn(HLogUser HLogUser, String content, String localnetId) {
        Map<String, Object> logData = new HashMap<String, Object>();
        logData.put("content", content);
        writeResLog(HLogUser, LogType.CUSTOMLOG_TYPE_WARN, logData, localnetId);
    }


    /**
     * 写系统日志
     *
     * @param HLogUser   用户
     * @param logType    登录级别
     * @param logData    日志内容
     * @param localnetId 本地网ID
     * @return void
     * <AUTHOR> Yongxiang
     * @date 2022/1/5 10:38
     */
    public static void writeResLog(HLogUser HLogUser, LogType logType, Map<String, Object> logData, String localnetId) {
        String hrcode = HLogUser.getHrLoginId();
        logData.put("serialNbr", genSerialNbr());
        logData.put("logTime", genLogTime());
        logData.putIfAbsent("hrcode", hrcode);
        logData.putIfAbsent("localnetId", localnetId);

        LogHelper.writeLog(logType.flag, logData);

        //解决非HLog环境日志输出
        if (LogType.CUSTOMLOG_TYPE_INFO.flag.equals(logType.flag)) {
            log.info(JsonUtil.getMapJson(logData));
        } else if (LogType.CUSTOMLOG_TYPE_DEBUG.flag.equals(logType.flag)) {
            log.debug(JsonUtil.getMapJson(logData));
        } else if (LogType.CUSTOMLOG_TYPE_WARN.flag.equals(logType.flag)) {
            log.warn(JsonUtil.getMapJson(logData));
        } else if (LogType.CUSTOMLOG_TYPE_ERROR.flag.equals(logType.flag)) {
            log.error(JsonUtil.getMapJson(logData));
        }

    }
}
