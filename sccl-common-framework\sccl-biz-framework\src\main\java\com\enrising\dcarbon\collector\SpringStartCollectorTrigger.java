package com.enrising.dcarbon.collector;

import com.enrising.dcarbon.processor.SpringStartProcessor;
import org.springframework.stereotype.Component;

/**
 * Spring启动触发收集触发器，在Spring上下文启动完成后触发一次，注意子类必须加上{@link Component}注解或者通过其他方式加入Spring上下文
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-12 11:04
 * @email <EMAIL>
 */
public abstract class SpringStartCollectorTrigger extends AbstractCollectorTrigger implements SpringStartProcessor {

    public SpringStartCollectorTrigger(AbstractCollectorTemplate template) {
        super(template);
    }

    @Override
    public void onStart() {
        trig();
    }
}
