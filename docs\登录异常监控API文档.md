# 登录异常监控API文档

## 概述

本文档描述了用户异常登录行为监控与限制功能的API接口。该功能主要用于监控和检测用户的异常登录行为，包括异常时间登录和异常IP登录。

## 功能特性

1. **异常时间登录检测**：监控非工作时间（默认23:00-06:00）的登录行为
2. **异常IP登录检测**：监控短时间内从不同IP地址段的登录行为
3. **登录日志查询**：提供完整的登录日志查询和分析功能
4. **安全报告生成**：生成登录行为的统计分析报告

## API接口

### 1. 登录日志查询

#### 1.1 基础查询（不分页）

```
GET /system/loginLog/listAll?username={username}&status={status}&ipAddress={ipAddress}
```

**参数说明**：

- `username`：用户名（可选，模糊查询）
- `status`：登录状态（可选，0-失败，1-成功，2-成功有警告）
- `ipAddress`：IP地址（可选，模糊查询）
- `remark`：备注信息（可选，模糊查询）

**响应示例**：

```json
{
  "code": 0,
  "msg": "操作成功",
  "data": [
    {
      "id": 1,
      "userId": 123,
      "username": "testuser",
      "loginTime": "2024-01-15 14:30:00.000",
      "ipAddress": "*************",
      "userAgent": "Mozilla/5.0...",
      "status": 2,
      "remark": "登录成功；我们注意到您的账户在 2024-01-15 02:30:15 有一次非常规时间的登录尝试"
    }
  ]
}
```

#### 1.2 分页查询

```
POST /system/loginLog/list
Content-Type: application/x-www-form-urlencoded
```

**请求参数**：

- `pageNum`：页码（通过请求参数传递，默认1）
- `pageSize`：每页大小（通过请求参数传递，默认10）
- `username`：用户名（可选，模糊查询）
- `userId`：用户ID（可选）
- `status`：登录状态（可选，0-失败，1-成功，2-成功有警告）
- `ipAddress`：IP地址（可选，模糊查询）
- `remark`：备注信息（可选，模糊查询）
- `startTime`：开始时间（可选，格式：yyyy-MM-dd HH:mm:ss）
- `endTime`：结束时间（可选，格式：yyyy-MM-dd HH:mm:ss）

**请求示例**：

```bash
curl -X POST "http://localhost:8080/system/loginLog/list" \
  -d "pageNum=1&pageSize=10&username=testuser&status=2&startTime=2024-01-01 00:00:00&endTime=2024-01-31 23:59:59"
```

**响应示例**：

```json
{
  "code": 0,
  "total": 100,
  "rows": [
    {
      "id": 1,
      "userId": 123,
      "username": "testuser",
      "loginTime": "2024-01-15 14:30:00.000",
      "ipAddress": "*************",
      "userAgent": "Mozilla/5.0...",
      "status": 2,
      "remark": "登录成功；我们注意到您的账户在 2024-01-15 02:30:15 有一次非常规时间的登录尝试"
    }
  ]
}
```

#### 1.5 查询用户最近登录记录

```
GET /system/loginLog/recent/{username}?limit=10
```

### 2. 异常登录检测

#### 2.1 检测异常时间登录

```
GET /system/loginLog/checkAbnormalTime/{username}
```

**响应示例（异常时间）**：

```json
{
  "code": 301,
  "msg": "我们注意到您的账户在 2024-01-15 02:30:15 有一次非常规时间的登录尝试",
  "data": null
}
```

#### 2.2 检测异常IP登录

```
GET /system/loginLog/checkAbnormalIp/{username}?currentIp=*************
```

**响应示例（异常IP）**：

```json
{
  "code": 301,
  "msg": "检测到异常IP登录：在 2024-01-15 14:35:20 从不同地理位置（IP:*************）尝试登录，与最近登录IP（*************）差异较大",
  "data": null
}
```

### 3. 统计分析

#### 3.1 获取登录统计分析

```
GET /system/loginLog/analysis?username={username}&startTime={startTime}&endTime={endTime}
```

**响应示例**：

```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {
    "totalLogins": 150,
    "timeDistribution": {
      "00:00-06:00": 5,
      "06:00-12:00": 60,
      "12:00-18:00": 70,
      "18:00-24:00": 15
    },
    "ipDistribution": {
      "*************": 80,
      "*************": 45,
      "*********": 25
    },
    "statusDistribution": {
      "成功": 135,
      "成功(有警告)": 5,
      "失败": 10
    },
    "abnormalPatterns": {
      "abnormalTimeLogins": 5,
      "multiIpUsers": {
        "user1": 3,
        "user2": 2
      },
      "highFailureUsers": {
        "testuser": 8
      }
    }
  }
}
```

#### 3.2 获取异常登录检测报告

```
GET /system/loginLog/abnormalReport?username={username}&startTime={startTime}&endTime={endTime}
```

## 配置说明

在 `application.yml` 中可配置异常检测参数：

```yaml
login:
  security:
    # 是否启用异常时间检测
    enable-abnormal-time-check: true
    # 异常时间开始时间（24小时制）
    abnormal-start-hour: 23
    # 异常时间结束时间（24小时制）
    abnormal-end-hour: 6
    # 是否启用异常IP检测
    enable-abnormal-ip-check: true
    # IP异常检测时间间隔（分钟）
    ip-check-interval-minutes: 5
    # 是否启用失败次数检测
    enable-failure-count-check: true
    # 失败次数阈值
    failure-threshold: 5
    # 失败次数检测时间窗口（分钟）
    failure-time-window-minutes: 30
    # 是否启用登录安全日志
    enable-security-log: true
```

## 登录流程集成

在登录成功后，系统会自动进行异常检测：

### 登录状态说明

- **状态0**：登录失败
- **状态1**：登录成功（正常）
- **状态2**：登录成功但有警告（异常登录）

### 登录响应示例

**正常登录**：

```json
{
  "code": 0,
  "msg": "操作成功",
  "token": "eyJhbGciOiJIUzI1NiJ9..."
}
```

**异常登录（有警告）**：

```json
{
  "code": 0,
  "msg": "操作成功",
  "token": "eyJhbGciOiJIUzI1NiJ9...",
  "warning": "我们注意到您的账户在 2024-01-15 02:30:15 有一次非常规时间的登录尝试"
}
```

### 日志记录说明

- 正常登录：记录状态为1，备注为"登录成功"
- 异常登录：记录状态为2，备注为"登录成功；[异常提示信息]"
- 多个异常同时出现时，提示信息用分号分隔

## 数据库表结构

```sql
CREATE TABLE `sys_login_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志主键ID',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
  `username` varchar(50) DEFAULT NULL COMMENT '用户名',
  `login_time` datetime(3) DEFAULT NULL COMMENT '登录时间',
  `ip_address` varchar(128) DEFAULT NULL COMMENT '登录IP地址',
  `user_agent` varchar(512) DEFAULT NULL COMMENT '用户代理信息',
  `status` tinyint(1) DEFAULT NULL COMMENT '登录状态（0失败1成功2成功有警告）',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注信息',
  PRIMARY KEY (`id`),
  KEY `idx_username` (`username`),
  KEY `idx_login_time` (`login_time`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户登录日志表';
```

## 安全注意事项

1. 异常检测不会阻止登录，只提供警告信息
2. 所有异常登录行为都会记录到数据库中
3. IP地址检测基于前两段地址差异，可根据实际需求调整
4. 异常时间段可根据业务需求进行配置调整
5. 建议定期清理历史登录日志数据 
