package com.enrising.dcarbon.manage;

import com.enrising.dcarbon.bean.SpringUtil;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.support.TransactionTemplate;

/**
 * 编程式事务支持
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-12 10:00
 * @email <EMAIL>
 */
public class TransactionTemplateSupport {
    private static PlatformTransactionManager transactionManager;
    private static TransactionTemplate transactionTemplate;

    public static TransactionTemplate getTransactionTemplate() {
        if (transactionTemplate == null) {
            synchronized (TransactionTemplateSupport.class) {
                if (transactionTemplate == null) {
                    if (transactionManager != null) {
                        transactionTemplate = new TransactionTemplate(transactionManager);
                    } else {
                        transactionManager = SpringUtil.getBean(PlatformTransactionManager.class);
                        if (transactionManager != null) {
                            transactionTemplate = new TransactionTemplate(transactionManager);
                        } else {
                            throw new NullPointerException("获取事务模板失败，请检查你的Spring环境中是否有PlatformTransactionManager管理器支持");
                        }
                    }
                }
            }
        }
        return transactionTemplate;
    }

}
