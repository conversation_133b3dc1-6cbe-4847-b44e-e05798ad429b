package com.enrising.dcarbon.manage;

import java.util.List;
import java.util.Map;

/**
 * 基础数据管理的抽象Service
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-10 15:56
 * @email <EMAIL>
 */
public interface IService<T extends BaseEntity> {
    int addOne(T data);

    int addList(List<T> dataList);

    int update(T param);

    int update(Map<String, Object> params, String key);

    int deleteLogicByIds(List<String> ids);

    int deleteLogic(T param);

    int deleteLoginByMap(Map<String, Object> params);

    int deleteByIds(List<String> ids);

    int delete(T param);

    int deleteByMap(Map<String, Object> params);
}
