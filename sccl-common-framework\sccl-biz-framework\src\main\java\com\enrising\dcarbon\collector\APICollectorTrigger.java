package com.enrising.dcarbon.collector;

import java.util.List;

/**
 * 直接调用API的触发器，该类触发器都是手动调用触发算法模板，不会自动启动
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-12 15:03
 * @email <EMAIL>
 */
public class APICollectorTrigger extends AbstractCollectorTrigger {
    public APICollectorTrigger(AbstractCollectorTemplate template) {
        super(template);
    }

    @Override
    public final void executeActiveTemplate() {
        super.executeActiveTemplate();
    }

    @Override
    public final void executePassiveTemplate(List<CollectedDatasource> datasourceList) {
        super.executePassiveTemplate(datasourceList);
    }

    @Override
    public final void execute() {
        executeActiveTemplate();
    }
}
