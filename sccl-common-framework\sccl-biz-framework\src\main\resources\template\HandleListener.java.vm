package ${package};

import com.enrising.dcarbon.manage.ResponsiveEventListener;
import com.enrising.dcarbon.manage.event.${eventName};
import ${parentPackage}.service.impl.${serviceImplName};
import ${parentPackage}.mapper.${mapperName};


/**
 * ${comment}
 *
 * <AUTHOR>
 * @date ${date}
 */
public class ${listenerName} extends ResponsiveEventListener<${eventName}> {
    @Override
    public void onEvent(${eventName} event) {
        //处理事件的相关逻辑写在这里
        ${mapperName} mapper = ${serviceImplName}.getMapper();


    }

    /**
    * 返回监听器名称
    *
    * @return java.lang.String
    * <AUTHOR>
    * @date ${datetime}
    */
    @Override
    public String name() {
        return null;
    }

    /**
     * 监听器级别，高级别的监听器会优先执行
     *
     * @return int
     * <AUTHOR>
     * @date ${datetime}
     */
    @Override
    public int level() {
        return 0;
    }

}