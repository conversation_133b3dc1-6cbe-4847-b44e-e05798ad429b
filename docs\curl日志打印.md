# Curl日志功能使用说明

## 概述

为了方便在服务器上调试HTTP请求，我们添加了curl日志生成功能。当系统发送HTTP请求时，会自动生成对应的curl命令并记录到日志中，开发人员可以直接复制这些curl命令在服务器上执行，进行问题排查和调试。

## 功能特点

1. **自动生成curl命令**：支持POST、GET等HTTP方法
2. **包含完整信息**：包括URL、请求头、请求体、超时设置等
3. **特殊字符转义**：自动处理单引号、反斜杠等特殊字符
4. **多种使用方式**：支持直接传参、从HttpRequest对象生成等
5. **日志级别控制**：使用INFO级别记录，便于生产环境控制

## 使用方法

### 1. 基本POST请求日志

```java
import com.sccl.common.utils.CurlLogUtil;

// 简单方式：直接传入URL和请求体对象
CurlLogUtil.logPostCurl("水电费数据上报", url, requestObject);

// 完整方式：包含请求头
Map<String, String> headers = new HashMap<>();
headers.put("Authorization", "Bearer token");
CurlLogUtil.logPostCurl("API调用", url, requestBodyJson, headers);
```

### 2. 带超时设置的POST请求日志

```java
// 生成包含超时参数的curl命令
CurlLogUtil.logPostCurlWithTimeout("数据同步", url, requestBody, headers, 15);
```

### 3. GET请求日志

```java
// GET请求日志
CurlLogUtil.logGetCurl("查询数据", url, headers);

// 简化版本（无请求头）
CurlLogUtil.logGetCurl("查询数据", url);
```

### 4. 从HttpRequest对象生成

```java
HttpRequest request = HttpUtil.createPost(url).body(requestBody);
CurlLogUtil.logCurlFromHttpRequest("HTTP请求", request);
```

## 已集成的模块

### 1. 水电费数据上报模块

**文件位置**：`sccl-module-business/src/main/java/com/sccl/modules/mssaccount/mssinterface/service/WaterElectricityServiceImpl.java`

**集成位置**：`batchReportWaterElectricityData`方法

**日志示例**：
```
INFO  - 水电费数据上报的curl命令（含超时设置）: curl -X POST 'http://api.example.com/report' -H 'Content-Type: application/json' -d '{"callId":"510000","timeStamp":*************,"data":[...]}' --connect-timeout 5 --max-time 5
```

### 2. 电表数据同步模块

**文件位置**：`sccl-interface/src/main/java/com/sccl/modules/syncAmmeter/service/SyncAmmeterServiceImpl.java`

**集成位置**：
- `syncData`方法：电表数据同步
- `updateAmmeter`方法：更新电表信息
- `addAmmeter`方法：新增电表信息

**日志示例**：
```
INFO  - 电表数据同步的curl命令（含超时设置）: curl -X POST 'http://sync.example.com/api' -H 'Content-Type: application/json' -d '{"messageEn":"encrypted_data","processCode":"SYNC001"}' --connect-timeout 15 --max-time 15
```

## 在服务器上使用curl命令

1. **复制日志中的curl命令**：从应用日志中找到对应的curl命令
2. **在服务器上执行**：直接粘贴执行，或根据需要修改参数
3. **查看响应结果**：对比应用程序收到的响应，排查问题

### 示例操作

```bash
# 从日志复制的curl命令
curl -X POST 'http://api.example.com/report' \
  -H 'Content-Type: application/json' \
  -d '{"callId":"510000","timeStamp":*************}' \
  --connect-timeout 5 --max-time 5

# 如需查看详细信息，可添加 -v 参数
curl -v -X POST 'http://api.example.com/report' \
  -H 'Content-Type: application/json' \
  -d '{"callId":"510000","timeStamp":*************}' \
  --connect-timeout 5 --max-time 5

# 如需保存响应到文件
curl -X POST 'http://api.example.com/report' \
  -H 'Content-Type: application/json' \
  -d '{"callId":"510000","timeStamp":*************}' \
  --connect-timeout 5 --max-time 5 \
  -o response.json
```

## 注意事项

1. **敏感信息**：curl命令中可能包含敏感信息（如token、密码等），请注意日志安全
2. **日志级别**：curl日志使用INFO级别，生产环境可通过调整日志级别控制是否输出
3. **特殊字符**：工具会自动转义特殊字符，但复杂的JSON结构建议先验证格式
4. **网络环境**：在服务器执行curl时注意网络环境和防火墙设置

## 扩展使用

如需在其他模块中使用curl日志功能，只需：

1. 导入工具类：`import com.sccl.common.utils.CurlLogUtil;`
2. 在HTTP请求前后添加日志调用
3. 根据实际需求选择合适的方法

## 工具类API参考

| 方法名 | 参数 | 说明 |
|--------|------|------|
| `generatePostCurl` | url, requestBody, headers | 生成POST curl命令字符串 |
| `generateGetCurl` | url, headers | 生成GET curl命令字符串 |
| `logPostCurl` | description, url, requestBody, headers | 记录POST请求curl日志 |
| `logPostCurl` | description, url, requestObject | 记录POST请求curl日志（简化版） |
| `logPostCurlWithTimeout` | description, url, requestBody, headers, timeout | 记录带超时的POST请求curl日志 |
| `logGetCurl` | description, url, headers | 记录GET请求curl日志 |
| `logCurlFromHttpRequest` | description, httpRequest | 从HttpRequest对象生成curl日志 |
