package com.enrising.dcarbon.manage;

import com.enrising.dcarbon.observer.Event;
import com.enrising.dcarbon.observer.EventListener;
import com.enrising.dcarbon.observer.EventManger;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 专门处理可响应事件监听器的事件管理器
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-10 16:54
 * @email <EMAIL>
 */
@Slf4j
public class ResponsiveEventManager extends EventManger {
    @Override
    public EventManger addListener(Class<?> eventType, EventListener<?> listener) {
        if (listener instanceof ResponsiveEventListener) {
            return super.addListener(eventType, listener);
        } else {
            throw new UnsupportedOperationException();
        }
    }

    @Override
    public EventManger addListenerIfAbsent(Class<?> eventType, EventListener<?> listener) {
        if (listener instanceof ResponsiveEventListener) {
            return super.addListenerIfAbsent(eventType, listener);
        } else {
            throw new UnsupportedOperationException();
        }
    }

    /**
     * 重写发布事件逻辑，如果监听器不允许继续传播事件的话将会直接结束
     *
     * @param event       发布的事件
     * @param eventType   事件类型
     * @param allowBubble 是否允许冒泡
     * @return void
     * <AUTHOR> ^ ▽ ^ *)
     * @date 2023/5/10 17:05
     */
    @Override
    public void publishEventSync(Event<?> event, Class<?> eventType, boolean allowBubble) {
        if (event == null) {
            throw new NullPointerException("无法发布");
        }
        List<EventListener<Event<?>>> listeners = listenerContainer.get(eventType);
        if (listeners != null) {
            for (EventListener<Event<?>> listener : listeners
                    .stream()
                    .sorted((o1, o2) -> Integer.compare(o2.level(), o1.level()))
                    .collect(Collectors.toList())) {
                try {
                    ResponsiveEventListener<Event<?>> responsiveEventListener = (ResponsiveEventListener<Event<?>>) listener;
                    responsiveEventListener.onEvent(event);
                    if (!responsiveEventListener.isSpread()) {
                        responsiveEventListener.setSpread(true);
                        return;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("执行监听器：{}时发生异常：{}：{}", listener.name(), e
                            .getClass()
                            .getSimpleName(), e.getMessage());
                }
            }
        }
        if (allowBubble) {
            Class<?> superClass = eventType.getSuperclass();
            if (superClass != null && Event.class.isAssignableFrom(superClass)) {
                publishEventSync(event, superClass, true);
            }
        }
    }

    @Override
    public void publishEvent(Event<?> event, Class<?> eventType, boolean allowBubble) {
        throw new UnsupportedOperationException();
    }
}
