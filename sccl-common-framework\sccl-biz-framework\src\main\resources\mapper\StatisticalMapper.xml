<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.enrising.dcarbon.statistic.mapper.StatisticalMapper">

    <resultMap type="com.enrising.dcarbon.statistic.StatisticalEntity" id="StatisticalResult">
        <id property="id" column="id"/>
        <result property="groupId" column="group_id"/>
        <result property="groupAlias" column="group_alias"/>
        <result property="title" column="title"/>
        <result property="content" column="content"/>
        <result property="contentType" column="content_type"/>
        <result property="statisticalTime" column="statistical_time"/>
        <result property="ownerAs" column="owner_as"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <sql id="selectVo">
        select id, group_id, group_alias, title, content, content_type, statistical_time, owner_as, create_time,
        update_time, del_flag from power_statistical
    </sql>

    <sql id="other-condition">
        <if test="id != null">and id = #{id}</if>
        <if test="groupId != null">and group_id = #{groupId}</if>
        <if test="groupAlias != null">and group_alias = #{groupAlias}</if>
        <if test="title != null">and title = #{title}</if>
        <if test="content != null">and content = #{content}</if>
        <if test="contentType != null">and content_type = #{contentType}</if>
        <if test="statisticalTime != null">and statistical_time = #{statisticalTime}</if>
        <if test="ownerAs != null">and owner_as = #{ownerAs}</if>
        <if test="createTime != null">and create_time = #{createTime}</if>
        <if test="updateTime != null">and update_time = #{updateTime}</if>
        <if test="delFlag != null">and del_flag = #{delFlag}</if>
    </sql>

    <sql id="like-condition">
        <if test="id != null">and id like concat('%', #{id}, '%')</if>
        <if test="groupId != null">and group_id like concat('%', #{groupId}, '%')</if>
        <if test="groupAlias != null">and group_alias like concat('%', #{groupAlias}, '%')</if>
        <if test="title != null">and title like concat('%', #{title}, '%')</if>
        <if test="content != null">and content like concat('%', #{content}, '%')</if>
        <if test="contentType != null">and content_type like concat('%', #{contentType}, '%')</if>
        <if test="statisticalTime != null">and statistical_time like concat('%', #{statisticalTime}, '%')</if>
        <if test="ownerAs != null">and owner_as like concat('%', #{ownerAs}, '%')</if>
        <if test="createTime != null">and create_time like concat('%', #{createTime}, '%')</if>
        <if test="updateTime != null">and update_time like concat('%', #{updateTime}, '%')</if>
        <if test="delFlag != null">and del_flag like concat('%', #{delFlag}, '%')</if>
    </sql>

    <insert id="insertList">
        insert into power_statisical
        <trim prefix="(" suffix=")" suffixOverrides=",">
            id,
            group_id,
            group_alias,
            title,
            content,
            content_type,
            statistical_time,
            owner_as,
            create_time,
            update_time,
            del_flag,
        </trim>
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.id},
                #{item.groupId},
                #{item.groupAlias},
                #{item.title},
                #{item.content},
                #{item.contentType},
                #{item.statisticalTime},
                #{item.ownerAs},
                #{item.createTime},
                #{item.updateTime},
                '0',
            </trim>
        </foreach>
    </insert>

    <update id="updateAll">
        update power_statistical
        <trim prefix="SET" suffixOverrides=",">
            <if test="entity.groupId != null  ">group_id = #{entity.groupId},</if>
            <if test="entity.groupAlias != null  and entity.groupAlias != ''  ">group_alias = #{entity.groupAlias},</if>
            <if test="entity.title != null  and entity.title != ''  ">title = #{entity.title},</if>
            <if test="entity.content != null  and entity.content != ''  ">content = #{entity.content},</if>
            <if test="entity.contentType != null  and entity.contentType != ''  ">content_type =
                #{entity.contentType},
            </if>
            <if test="entity.statisticalTime != null  ">statistical_time = #{entity.statisticalTime},</if>
            <if test="entity.ownerAs != null  and entity.ownerAs != ''  ">owner_as = #{entity.ownerAs},</if>
            <if test="entity.createTime != null  ">create_time = #{entity.createTime},</if>
            <if test="entity.update_time != null  ">update_time = #{entity.updateTime},</if>
            <if test="entity.delFlag != null  ">del_flag = #{delFlag},</if>
        </trim>
        where id in
        (
        <foreach collection="ids" index="index" item="item" separator=",">
            #{item}
        </foreach>
        )
        and del_flag = 0
    </update>

    <delete id="deleteByIds">
        UPDATE power_statistical SET del_flag='1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectList" resultType="com.enrising.dcarbon.statistic.StatisticalEntity">
        <include refid="selectVo"/>
        <where>
            del_flag = '0'
            <include refid="other-condition"/>
        </where>
    </select>
    <select id="selectGroupId" resultType="java.lang.Long">
        select group_id from power_statistical where group_alias = #{groupName} and del_flag = 0 group by group_id;
    </select>
</mapper>