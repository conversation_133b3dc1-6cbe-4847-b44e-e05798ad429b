package com.enrising.dcarbon.collector;

import com.enrising.dcarbon.thread.NamedThreadFactory;
import com.enrising.dcarbon.thread.ScheduleTaskUtil;
import com.enrising.dcarbon.thread.SyncHelper;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 周期触发器轮询器
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-12 11:33
 * @email <EMAIL>
 */
@Slf4j
public class CycleTrigLoop {
    private final Map<Class<? extends CyclicallyCollectorTrigger>, CyclicallyCollectorTrigger> triggers = new ConcurrentHashMap<>();
    private volatile boolean isStop = false;
    private volatile boolean isRunning = false;
    private final ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(2, 5, 60, TimeUnit.SECONDS, new LinkedBlockingDeque<>(), new NamedThreadFactory("cycleTrigLoopThread"));

    private CycleTrigLoop() {
    }

    public static CycleTrigLoop getInstance() {
        return InstanceHolder.CYCLE_TRIG_LOOP;
    }

    public void register(CyclicallyCollectorTrigger trigger) {
        if (triggers.containsKey(trigger.getClass())) {
            return;
        }
        if (!isRunning) {
            start();
        }
        triggers.put(trigger.getClass(), trigger);
    }

    public CyclicallyCollectorTrigger unloading(Class<? extends CyclicallyCollectorTrigger> triggerType) {
        return triggers.remove(triggerType);
    }

    public synchronized void start() {
        if (isRunning) {
            return;
        }
        Thread thread = new Thread(() -> {
            try {
                isRunning = true;
                log.info("cycle trig collector loop start");
                while (!isStop) {
                    SyncHelper.sleepQuietly(1, TimeUnit.SECONDS);
                    for (CyclicallyCollectorTrigger trigger : triggers.values()) {
                        if (trigger.isReachTriggerTime()) {
                            if (trigger.isRunning || trigger.isWaiting) {
                                continue;
                            }
                            try {
                                trigger.isWaiting = true;
                                threadPoolExecutor.submit(trigger::trig);
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }
                    }
                }
            } catch (Exception ignored) {
            }
            isStop = false;
            isRunning = false;
            log.info("cycle trig collector loop end");
        });
        thread.setDaemon(true);
        thread.setName("cycleTrigLoop");
        thread.start();
    }

    public void stop() {
        isStop = true;
    }

    private static class InstanceHolder {
        private static final CycleTrigLoop CYCLE_TRIG_LOOP = new CycleTrigLoop();
    }
}
