package com.enrising.dcarbon.demo.state;

/**
 * 测试的接口
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-10 10:16
 * @email <EMAIL>
 */
public class DemoController {
    private static final DemoService SERVICE = new DemoService();

    public void add(DemoBaseData data) {
        if (data == null || data.getId() == null) {
            System.out.println("空参数，无法新增");
        }
        SERVICE.add(data);
    }

    public void createAgenda(long id) {
        SERVICE.createAgenda(id);
    }

    public void check(long id) {
        SERVICE.check(id);
    }

    public void confirm(long id) {
        SERVICE.confirm(id);
    }
}
