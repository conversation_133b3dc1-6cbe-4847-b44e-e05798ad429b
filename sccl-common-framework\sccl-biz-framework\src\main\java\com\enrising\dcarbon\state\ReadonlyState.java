package com.enrising.dcarbon.state;

/**
 * 只读状态，该类阻止了调用{@link #change()}方法
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-10 11:29
 * @email <EMAIL>
 */
public class ReadonlyState implements State {
    private final Object source;

    private final Class<? extends State> stateType;

    private StateMachine fromStateMachine;

    public ReadonlyState(State state) {
        this.source = state.getSource();
        this.stateType = state.getClass();
    }

    @Override
    public final void change() {
        throw new UnsupportedOperationException("不支持事件改变");
    }

    @Override
    public Object getSource() {
        return source;
    }

    ReadonlyState setFromStateMachine(StateMachine fromStateMachine) {
        this.fromStateMachine = fromStateMachine;
        return this;
    }

    @Override
    public StateMachine currentStateMachine() {
        return fromStateMachine;
    }



    public Class<? extends State> getStateType() {
        return stateType;
    }
}
