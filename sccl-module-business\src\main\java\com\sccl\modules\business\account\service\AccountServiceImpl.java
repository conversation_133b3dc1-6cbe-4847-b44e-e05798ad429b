package com.sccl.modules.business.account.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.sccl.common.lang.StringUtils;
import com.sccl.common.support.Convert;
import com.sccl.common.utils.DateUtils;
import com.sccl.common.utils.ShiroUtils;
import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.framework.utils.ExcelUtil;
import com.sccl.framework.web.domain.IdNameVO;
import com.sccl.modules.business.account.domain.*;
import com.sccl.modules.business.account.mapper.AccountExcelDetailExpenseMapper;
import com.sccl.modules.business.account.mapper.AccountExcelDetailPowerMapper;
import com.sccl.modules.business.account.mapper.AccountExcelInfoMapper;
import com.sccl.modules.business.account.mapper.AccountMapper;
import com.sccl.modules.business.account.unit.DataVerification;
import com.sccl.modules.business.accountbillitempre.domain.Accountbillitempre;
import com.sccl.modules.business.accountbillitempre.mapper.AccountbillitempreMapper;
import com.sccl.modules.business.accountbillpre.domain.AccountbillpreResult;
import com.sccl.modules.business.accountbillpre.mapper.AccountbillpreMapper;
import com.sccl.modules.business.ammeterorprotocol.domain.Ammeterorprotocol;
import com.sccl.modules.business.ammeterorprotocol.domain.ElectricClassification;
import com.sccl.modules.business.ammeterorprotocol.mapper.ElectricClassificationMapper;
import com.sccl.modules.business.ammeterorprotocol.service.IAmmeterorprotocolService;
import com.sccl.modules.business.cache.utils.RedisUtil;
import com.sccl.modules.business.quota.mapper.QuotaMapper;
import com.sccl.modules.business.statinAudit.util.StationAuditUtil;
import com.sccl.modules.business.stationinfo.domain.CheckStationInfoDto;
import com.sccl.modules.business.stationinfo.service.StationInfoServiceImpl;
import com.sccl.modules.business.stationreportwhitelist.domain.PowerAmmeterorprotocol;
import com.sccl.modules.mssaccount.mssaccountbill.domain.MssAccountbill;
import com.sccl.modules.mssaccount.mssaccountbill.mapper.MssAccountbillMapper;
import com.sccl.modules.system.attachments.domain.Attachments;
import com.sccl.modules.system.attachments.domain.UpLoadFile;
import com.sccl.modules.system.attachments.service.IAttachmentsService;
import com.sccl.modules.system.organization.domain.Organization;
import com.sccl.modules.system.organization.mapper.OrganizationMapper;
import com.sccl.modules.system.user.domain.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.transaction.Transactional;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static com.sccl.modules.business.account.unit.DataVerification.ifnull;
import static com.sccl.modules.business.statinAudit.util.StationAuditUtil.checkTime;


/**
 * 台账 服务层实现
 *
 * <AUTHOR>
 * @date 2019-04-25
 */
@Service
@Slf4j
public class AccountServiceImpl extends BaseServiceImpl<Account> implements IAccountService {
    @Autowired
    public AccountMapper accountMapper;
    @Autowired
    public QuotaMapper quotaMapper;
    @Autowired
    MssAccountbillMapper mssAccountbillMapper;
    @Autowired
    AccountbillpreMapper accountbillpremapper;
    @Autowired
    AccountbillitempreMapper accountbillitempreMapper;
    @Autowired
    OrganizationMapper organizationMapper;
    @Autowired
    ElectricClassificationMapper electricClassificationMapper;
    @Autowired
    private StationInfoServiceImpl stationInfoService;
    @Autowired
    private IAttachmentsService attachmentsService;
    @Autowired
    private AccountExcelInfoMapper accountExcelInfoMapper;
    @Autowired
    private AccountExcelDetailPowerMapper accountExcelDetailPowerMapper;
    @Autowired
    private AccountExcelDetailExpenseMapper accountExcelDetailExpenseMapper;
    @Autowired
    private IAmmeterorprotocolService ammeterorprotocolService;
    @Value("${sccl.deployTo}")
    private String deployTo;

    /**
     * @Description: 计算残月定额传入某月定额，总共天数，有效天数
     * @author: dongk
     * @date: 2019/5/5
     * @param:
     * @return:
     */
    private static BigDecimal electrQuota(BigDecimal quota, Integer days, Integer day) {
        quota = quota.divide(new BigDecimal(days), 10, BigDecimal.ROUND_HALF_UP);
        quota = quota.multiply(new BigDecimal(day));
        return quota.setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    /****
     * 传入期号 ，返回具体日期增加一个月。
     * @param date 日期
     * @return 2017-05-13
     * @throws ParseException
     */
    public static String subMonth(String date) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
        Date dt = sdf.parse(date);
        Calendar rightNow = Calendar.getInstance();
        rightNow.setTime(dt);
        rightNow.add(Calendar.MONTH, 1);
        Date dt1 = rightNow.getTime();
        String reStr = sdf.format(dt1);
        return reStr;
    }

    /**
     * @Description: 获取两段时间之间的月份
     * @author: dongk
     * @date: 2019/9/11
     * @param:
     * @return:
     */
    private static List<String> getMonthBetween(Date minDate, Date maxDate) throws ParseException {
        ArrayList<String> result = new ArrayList<String>();
        //格式化为年月
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
        SimpleDateFormat sdf1 = new SimpleDateFormat("MM");

        Calendar min = Calendar.getInstance();
        Calendar max = Calendar.getInstance();

        min.setTime(minDate);
        min.set(min.get(Calendar.YEAR), min.get(Calendar.MONTH), 1);

        max.setTime(maxDate);
        max.set(max.get(Calendar.YEAR), max.get(Calendar.MONTH), 2);

        Calendar curr = min;
        while (curr.before(max)) {
            result.add(sdf1.format(curr.getTime()));
            curr.add(Calendar.MONTH, 1);
        }

        return result;
    }

    @Override
    public List<AccountBaseResult>
    selectSelfList(AccountCondition condition) {
        List<AccountBaseResult> baseResultList = new ArrayList<>();

        log.info("台账录入 baseresultList开始执行");
        if ("sc".equalsIgnoreCase(deployTo)) {
            condition.setDeployTo("sc");
            baseResultList = accountMapper.selectSelfListSC(condition);
        } else if ("ln".equalsIgnoreCase(deployTo)) {
            condition.setDeployTo("ln");
            baseResultList = accountMapper.selectSelfListLN(condition);
        }
        log.info("台账录入 baseresultList执行结束");

        if (CollectionUtils.isEmpty(baseResultList)) {
            return new ArrayList<>();
        }

        List<BaseResultGroup> groupList = baseResultList.stream().map(
                item -> {
                    BaseResultGroup group = new BaseResultGroup();
                    group.setAmmeterid(item.getAmmeterid());
                    group.setAccno(item.getAccountno());
                    return group;
                }
        ).collect(Collectors.toList());
        List<BaseResultGroup> groups = accountMapper.selectifNextBitch(groupList);
        Map<Long, Integer> groupMap = groups.stream().collect(Collectors.toMap(
                item -> item.getAmmeterid(),
                item -> item.getSum(),
                (item1, item2) -> item1
        ));

        for (AccountBaseResult baseResult : baseResultList) {
            //AccountCondition ifNextCondition = new AccountCondition();
            //ifNextCondition.setAmmeterid(baseResult.getAmmeterid());
            //try {
            //    ifNextCondition.setAccountno(baseResult.getAccountno());
            //} catch (Exception e) {
            //}
            //
            //Integer ifNext = accountMapper.selectifNext(ifNextCondition);
            Integer ifNext = groupMap.get(baseResult.getAmmeterid());
            if (ifNext != null && ifNext > 0) {
                baseResult.setIfNext(true);
            } else {
                baseResult.setIfNext(false);
            }

            if (!condition.getRemoveAllFlag()) {
                BigDecimal quota = null;
                try {
                    quota = this.getElectrQuota(baseResult.getAmmeterid(), baseResult.getStartdate(), baseResult.getEnddate());
                } catch (Exception e) {
                    quota = new BigDecimal(0);
                }
                baseResult.setQuotareadings(quota);
            }

            //如果是由不支付电费台账转入，则判断是否已上传支付说明
            if(baseResult.getFromNoPay() != null && baseResult.getFromNoPay() == 1) {
                int cnt = attachmentsService.checkFileExist(baseResult.getPcid(), "支付说明");
                if (cnt > 0) {
                    baseResult.setHasTopayFj("1");//已上传支付说明
                } else {
                    baseResult.setHasTopayFj("0");//未上传支付说明
                }
            }
        }

        return baseResultList;
    }

    @Override
    public List<AccountBaseResult> selectSelfMapList(AccountCondition condition) {
        condition.setDeployTo("sc".equalsIgnoreCase(deployTo) ? "sc" : "ln");

        List<AccountBaseResult> baseResultList = accountMapper.selectSelfMapList(condition);
        for (AccountBaseResult baseResult : baseResultList) {
            AccountCondition ifNextCondition = new AccountCondition();
            ifNextCondition.setAmmeterid(baseResult.getAmmeterid());
            try {
                ifNextCondition.setAccountno(baseResult.getAccountno());
            } catch (Exception e) {

            }

            Integer ifNext = accountMapper.selectMapifNext(ifNextCondition);
            if (ifNext != null && ifNext > 0) {
                baseResult.setIfNext(true);
            } else {
                baseResult.setIfNext(false);
            }


            BigDecimal quota = null;
            try {
                quota = this.getElectrQuota(baseResult.getAmmeterid(), baseResult.getStartdate(), baseResult.getEnddate());
            } catch (Exception e) {
                quota = new BigDecimal(0);
            }
            baseResult.setQuotareadings(quota);
        }

        return baseResultList;
    }

    /**
     * @Description: 初始化台账
     * @author: dongk
     * @date: 2019/7/9
     * @param:
     * @return:
     */
    @Override
    public void initPoweerAccount(String accountno, Long companyId, Long countryId, Long userid, String version) {
        Account account = new Account();
        account.setAccountno(accountno);
        account.setCompany(companyId);
        account.setCountry(countryId);
        account.setInputerid(userid);
        accountMapper.initPoweerAccount(account);
        handleBG(accountno, companyId, countryId, userid);
    }

    /**
     * @Description: 处理包干电表生成的普通台账
     * @author: dongk
     * @date: 2019/7/20
     * @param:
     * @return:
     */
    private void handleBG(String accountno, Long companyId, Long countryId, Long userid) {
        Account account = new Account();
        account.setAccountno(accountno);
        account.setCompany(companyId);
        account.setCountry(countryId);
        account.setInputerid(userid);
        List<Map<String, Object>> resultList = accountMapper.selectBG(account);
        if (resultList != null && resultList.size() > 0) {
            for (Map<String, Object> map : resultList) {
                //包干开始日期
                Date lumpstartdate = (Date) map.get("lumpstartdate");
                //获取当前时间的期号

                String str = map.get("accountno").toString();
                //将包干开始日期转换为期号
                SimpleDateFormat formatter = new SimpleDateFormat("yyyyMM");
                String bg = formatter.format(lumpstartdate);
                Integer accounton = Integer.parseInt(str);
                Integer bgNum = Integer.parseInt(bg);
                Integer val = bgNum - accounton;
                Long pcid = (Long) map.get("pcid");
                if (val == 0) {
                    formatter = new SimpleDateFormat("yyyyMMdd");
                    Integer day = Integer.parseInt(formatter.format(lumpstartdate).substring(6, 8));
                    if (day == 1) {
                        accountMapper.deleteById(pcid);
                    } else if (day > 1) {
                        account = new Account();
                        account.setPcid((pcid));
                        String no = formatter.format(new Date(lumpstartdate.getTime() - 1 * 24 * 60 * 60 * 1000));
                        account.setEnddate(no);
                        accountMapper.updateEnddate(account);
                    }
                } else if (val < 0) {
                    accountMapper.deleteById(pcid);
                }

            }
        }
    }

    @Override
    public BigDecimal getElectrQuota(Long ammeterid, String startdate, String enddate) throws Exception {
        BigDecimal quotaData = new BigDecimal(0);
        //判断是否是空串
        if (StringUtils.isEmpty(startdate) || StringUtils.isEmpty(enddate)) {
            return null;
        }
        startdate = startdate.replaceAll("[[\\s-:punct:]]", "").substring(0, 8);
        enddate = enddate.replaceAll("[[\\s-:punct:]]", "").substring(0, 8);

        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
        Date start = formatter.parse(startdate);
        Date end = formatter.parse(enddate);
        //判断开始时间是否大于结束时间
        if (start.getTime() > end.getTime()) {
            return null;
        }

        Integer startyyyy = Integer.parseInt(startdate.substring(0, 4));
        Integer endyyyy = Integer.parseInt(enddate.substring(0, 4));

        //获取开始和结束月数
        Integer statrMM = Integer.parseInt(startdate.substring(4, 6));
        Integer endMM = Integer.parseInt(enddate.substring(4, 6));
        //获取开始天数
        Integer statrDay = Integer.parseInt(startdate.substring(6, 8));
        //获取结束天数
        Integer endDay = Integer.parseInt(enddate.substring(6, 8));
        //开始时间当月天数
        Integer startDays = DateUtils.getMonthHasDays(start);
        //结束时间当月天数
        Integer endDays = DateUtils.getMonthHasDays(end);
        if (startyyyy.equals(endyyyy)) {
            Integer num = endMM - statrMM;
            //如果开始时间跟结束时间在同一个月
            if (num == 0) {
                //通过查询获得定额
                BigDecimal quota = selectQuota(ammeterid, statrMM);
                Integer day = new Double(DateUtils.getDistanceOfTwoDate(start, end)).intValue() + 1;
                if (day == endDays) {
                    quotaData = quota;
                } else {

                    quotaData = electrQuota(quota, startDays, day);
                }
            }
            //结束时间是开始时间下一个月
            else if (num == 1) {
                //通过查询获得定额
                BigDecimal startQuota = selectQuota(ammeterid, statrMM);
                if (statrDay == 1) {
                    quotaData = quotaData.add(startQuota);
                } else if (statrDay > 1) {
                    quotaData = quotaData.add(electrQuota(startQuota, startDays, startDays - statrDay + 1));
                }
                BigDecimal endQuota = selectQuota(ammeterid, endMM);
                if (endDays == endDay) {
                    quotaData = quotaData.add(endQuota);
                } else if (endDays > endDay) {
                    quotaData = quotaData.add(electrQuota(endQuota, endDays, endDay - 1 + 1));
                }
            }
            //有隔月的情况
            else if (num > 1) {
                //通过查询获得定额
                BigDecimal startQuota = selectQuota(ammeterid, statrMM);
                if (statrDay == 1) {
                    quotaData = quotaData.add(startQuota);
                } else if (statrDay > 1) {
                    quotaData = quotaData.add(electrQuota(startQuota, startDays, startDays - statrDay + 1));
                }
                BigDecimal endQuota = selectQuota(ammeterid, endMM);
                if (endDays.equals(endDay)) {
                    quotaData = quotaData.add(endQuota);
                } else if (endDays > endDay) {
                    quotaData = quotaData.add(electrQuota(endQuota, endDays, endDay - 1 + 1));
                }
                int i = statrMM + 1;
                while (i < endMM) {
                    quotaData = quotaData.add(selectQuota(ammeterid, i));
                    i++;
                }
            }

        } else if (startyyyy < endyyyy) {
            List<String> list = getMonthBetween(start, end);
            if (list != null && list.size() > 0) {
                list.remove(0);
                list.remove(list.size() - 1);
                BigDecimal startQuota = selectQuota(ammeterid, statrMM);
                if (statrDay == 1) {
                    quotaData = quotaData.add(startQuota);
                } else if (statrDay > 1) {
                    quotaData = quotaData.add(electrQuota(startQuota, startDays, startDays - statrDay + 1));
                }
                BigDecimal endQuota = selectQuota(ammeterid, endMM);
                if (endDays.equals(endDay)) {
                    quotaData = quotaData.add(endQuota);
                } else if (endDays > endDay) {
                    quotaData = quotaData.add(electrQuota(endQuota, endDays, endDay - 1 + 1));
                }

                for (String mm : list) {
                    quotaData = quotaData.add(selectQuota(ammeterid, Integer.parseInt(mm)));
                }
            }

        }
        return quotaData;
    }

    @Transactional
    @Override
    public Map<String, Object> updateOwn(List<Account> accountList) {
        int num = 0;
        BigDecimal z = new BigDecimal(0);
        Map<String, Object> crossmap = new HashMap<>();
        String str = "";
        User user = ShiroUtils.getUser();
        List<Long> pcidList = new ArrayList<>();
        for (Account account : accountList) {
            if (user != null) {
                //设置最后修改人
                account.setLastediterid(user.getId());
                account.setLasteditdate(new Date());
                if (account.getAccountmoney() != null && account.getAccountmoney().compareTo(new BigDecimal(0)) != 0) {
                    account.setEffective(new BigDecimal(1));
                }
            }
            //account.setIsnew(new BigDecimal(0));

            boolean b = true;
            BigDecimal money = this.selectCompletedMoney(account.getPcid());
            if (money != null && money.compareTo(z) > 0) {
                if (account.getAccountmoney().compareTo(money) < 0) {
                    if ("sc".equals(deployTo)) {
                        str += "电表/协议编号为【" + account.getAmmetercode() + "】的台账已经报账完成：" + money + ",台账实缴费用不能低于已经报账金额";
                    } else if ("ln".equals(deployTo)) {
                        str += "项目名称为【" + account.getProjectname() + "】的台账已经报账完成：" + money + ",台账实缴费用不能低于已经报账金额";
                    }

                    b = false;
                }
            }
/*            if (b) {
                List<Map<String, Object>> licrossmap= (List<Map<String, Object>>) accountMapper.checkCrossdateaccount(account);
                if (licrossmap.size()>0) {
                    b = false;
                    str = "可能存在日期交叉风险，请核实台账查询数据";
                    for (Map<String, Object> stringObjectMap : licrossmap) {
                        stringObjectMap.forEach((key, value) -> {
                            System.out.println(key);
                            System.out.println(value);

                        });
                    }

                }

            }*/
            if (b) {
                num += accountMapper.updateOwnById(account);
                pcidList.add(account.getPcid());

                accountMapper.deleteByAmmeterid(account.getAmmeterid());
                saveBGJJ(account);
                // 保存之后，redis的暂存数据应删除
                RedisUtil.del(user.getLoginId()+"Account");
            }
        }
        //重新稽核台账
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                StationAuditUtil.doAuditAccountThreadByPcids(pcidList);
            }
        });
        Map<String, Object> map = new HashMap<>();
        map.put("num", num);
        map.put("str", str);
        return map;
    }

    @Override
    public Map<String, Object> staging(List<Account> accountList) {
        int num = 0;
        BigDecimal z = new BigDecimal(0);
        Map<String, Object> crossmap = new HashMap<>();
        String str = "";
        User user = ShiroUtils.getUser();
        for (Account account : accountList) {
            if (user != null) {
                //设置最后修改人
                account.setLastediterid(user.getId());
                account.setLasteditdate(new Date());
                if (account.getAccountmoney() != null && account.getAccountmoney().compareTo(new BigDecimal(0)) != 0) {
                    account.setEffective(new BigDecimal(1));
                }
            }
            //account.setIsnew(new BigDecimal(0));

            boolean b = true;
            BigDecimal money = this.selectCompletedMoney(account.getPcid());
            if (money != null && money.compareTo(z) > 0) {
                if (account.getAccountmoney().compareTo(money) < 0) {
                    if ("sc".equals(deployTo)) {
                        str += "电表/协议编号为【" + account.getAmmetercode() + "】的台账已经报账完成：" + money + ",台账实缴费用不能低于已经报账金额";
                    } else if ("ln".equals(deployTo)) {
                        str += "项目名称为【" + account.getProjectname() + "】的台账已经报账完成：" + money + ",台账实缴费用不能低于已经报账金额";
                    }

                    b = false;
                }
            }
/*            if (b) {
                List<Map<String, Object>> licrossmap= (List<Map<String, Object>>) accountMapper.checkCrossdateaccount(account);
                if (licrossmap.size()>0) {
                    b = false;
                    str = "可能存在日期交叉风险，请核实台账查询数据";
                    for (Map<String, Object> stringObjectMap : licrossmap) {
                        stringObjectMap.forEach((key, value) -> {
                            System.out.println(key);
                            System.out.println(value);

                        });
                    }

                }

            }*/
            if (b) {
//                num += accountMapper.updateOwnById(account);
                num += 1;
//                accountMapper.deleteByAmmeterid(account.getAmmeterid());
//                saveBGJJ(account);
                RedisUtil.set(user.getLoginId()+"Account",accountList);
            }
        }

        Map<String, Object> map = new HashMap<>();
        map.put("num", num);
        map.put("str", str);
        return map;
    }

    @Override
    public Map<String, Object> audit(List<Account> accountList) {



        for (Account account : accountList) {

        }


        return null;
    }

    //保存包干加减
    private void saveBGJJ(Account account) {
        if ("sc".equals(deployTo) && (account.getHighreadings() != null || account.getFlatreadings() != null || account.getLowreadings() != null)) {
            Integer count = accountMapper.getAccountHflByPcid(account);
            if (count > 0) {
                accountMapper.updateAccountHfl(account);
            } else {
                accountMapper.insertAccountHfl(account);
            }
        }
    }

    @Override
    public Map<String, Object> deleteAccountByIds(String[] ids) {
        int num = 0;
        boolean b = true;
        List<Long> idsList = new ArrayList<>();
        for (int i = 0; i < ids.length; i++) {
            idsList.add(Long.parseLong(ids[i]));
        }
        Map<String, Object> map = new HashMap<>();
        List<AccountBaseResult> resultList = accountMapper.selectByIds(idsList);
        String str = "";
        List<AccountBaseResult> list = new ArrayList<>();
        if (resultList != null && resultList.size() > 0) {
            for (AccountBaseResult result : resultList) {
                if (result.getEffective().compareTo(new BigDecimal(0)) > 0) {

                    if (result.getStatus() != 1 && result.getStatus() != 5) {
                        map.put("num", 0);
                        map.put("str", "数据异常，请联系管理员");
                        return map;
                    }

                    //收集退回的台账
                    if (result.getStatus() == 5) {
                        list.add(result);
                    }

                    BigDecimal money = this.selectCompletedMoney(result.getPcid());
                    if (money != null && money.compareTo(new BigDecimal(0)) > 0) {
                        if ("sc".equals(deployTo)) {
                            str += "电表/协议编号为【" + result.getAmmetercode() + "】的台账已经报账完成：" + money + ",不能删除";
                        } else if ("ln".equals(deployTo)) {
                            str += "项目名称为【" + result.getProjectname() + "】的台账已经报账完成：" + money + ",不能删除";
                        }
                        b = false;
                    } else {
                        Map<String, Object> params = new HashMap<>();
                        params.put("ammeterid", result.getAmmeterid());
                        accountMapper.deleteByParams(params);
                    }
                }
            }
            if (b) {
                //先删除明细，再删除台账
                if (list.size() > 0) {
                    for (AccountBaseResult result : resultList) {
                        Accountbillitempre item = accountbillitempreMapper.selectByPcid(result.getPcid());
                        if (item != null) {
                            accountbillitempreMapper.deleteById(item.getPabriid());
                        }
                    }
                }
                num += accountMapper.deleteByIds(ids);
            }
        }

        map.put("num", num);
        map.put("str", str);
        return map;
    }

    @Override
    public List<Map<String, String>> getDepartments(Long companyId) {
        Map<String, Object> params = new HashMap<>();
        List<Map<String, String>> list = null;
        String[] depids = null;
        User user = ShiroUtils.getUser();
        if (user != null) {
            params.put("userId", user.getId());
            params.put("companyId", companyId);
            if ("sc".equals(deployTo)) {
/*                depids=accountMapper.selectIds(String.valueOf(companyId)).split(",");
                params.put("depids", depids);*/
                /* if ((companyId.intValue() == 1000002)||(companyId.intValue() == 1000042)||(companyId.intValue() == 1000085)||(companyId.intValue() == 1000194)||(companyId.intValue() == 1000275)||(companyId.intValue() == 1000331)||(companyId.intValue() == 1000373)||(companyId.intValue() == 1000443)||(companyId.intValue() == 1000486)||(companyId.intValue() == 1000577)||(companyId.intValue() == 1000615)||(companyId.intValue() == 1000662)||(companyId.intValue() == 1000819)||(companyId.intValue() == 1000876)||(companyId.intValue() == 1000987)||(companyId.intValue() == 1001156)||(companyId.intValue() == 1001495))
                 */
                list = accountMapper.getDepartmentssccd(params);
           /*     else
                    list = accountMapper.getDepartments(params);*/
            } else
                list = accountMapper.getDepartments(params);
            return list;
        }
        return null;
    }

    @Override
    public List<AccountBaseResult> selectListByParams(AccountCondition condition) {
        List<AccountBaseResult> list = accountMapper.selectListByParams(condition);
        if (list != null && list.size() > 0) {
            for (AccountBaseResult account : list) {
                account.setCountry(cascadeCountry(Long.parseLong(account.getCountry())));
            }
        }
        return list;
    }

    @Override
    public List<AccountBaseResult> selectListByParamsfinan(AccountCondition condition) {

      /*  String enddate = condition.getStartAccountno();
        String startdate = condition.getEndAccountno();
        if (enddate != null && enddate.length() != 0) {
            String endyear = enddate.substring(0, 4);
            String endmonth = enddate.substring(4, 6);
            condition.setEndYear(endyear);
            condition.setEndMonth(endmonth);
        }
        if (startdate != null && startdate.length() != 0) {
            String startyear = startdate.substring(0, 4);
            String startmonth = startdate.substring(4, 6);
            condition.setStartYear(startyear);
            condition.setStartMonth(startmonth);
        }*/
        List<AccountBaseResult> list = accountMapper.selectListByParamsfinance(condition);
        if (list != null && list.size() > 0) {
            for (AccountBaseResult account : list) {
                account.setCountry(cascadeCountry(Long.parseLong(account.getCountry())));
            }
        }
        return list;
    }

    private String cascadeCountry(Long country) {
        String c = "";
        Organization org = organizationMapper.selectById(country);
        if (org != null && "2".equals(org.getOrgType())) {
            c = org.getOrgName();
            if (org.getParentCompanyNo() != null) {
                String s = cascadeCountry(Long.parseLong(org.getParentCompanyNo()));
                if (!StringUtils.isEmpty(s)) {
                    c = s + "-" + c;
                }
            }
        }

        return c;
    }

    @Override
    public AccountBaseResult selectByAmmeterId(AccountCondition condition) {
        condition.setDeployTo("sc".equalsIgnoreCase(deployTo) ? "sc" : "ln");
        return accountMapper.selectByAmmeterId(condition);
    }

    @Override
    public AccountBaseResult allAccountTotal(AccountCondition condition) {
        return accountMapper.allAccountTotal(condition);
    }

    @Override
    public AccountBaseResult accountTotal(AccountCondition condition) {
        //录入台账初始数据
        Long companyId = condition.getCompany();
        Long countryId = condition.getCountry();
        User user = ShiroUtils.getUser();
        if (user != null) {
            //部门组
            List<IdNameVO> departments = user.getDepartments();
            //分公司组
            List<IdNameVO> companies = user.getCompanies();
            //如果没有传入分公司和责任中心，则默认第一个
            if (companyId == null) {
                companyId = companies != null && companies.size() > 0 ? Long.parseLong(companies.get(0).getId()) : null;
            }
            if (countryId == null) {
                countryId = departments != null && departments.size() > 0 ? Long.parseLong(departments.get(0).getId()) : null;
            }
        }
        //搜索条件中加上当前登录人所属分公司
        //查询
        condition.setCompany(companyId);
        condition.setCountry(countryId);
        return accountMapper.accountTotal(condition);
    }

    @Override
    public AccountBaseResult accountMapTotal(AccountCondition condition) {
        //录入台账初始数据
        Long companyId = condition.getCompany();
        Long countryId = condition.getCountry();
        User user = ShiroUtils.getUser();
        if (user != null) {
            //部门组
            List<IdNameVO> departments = user.getDepartments();
            //分公司组
            List<IdNameVO> companies = user.getCompanies();
            //如果没有传入分公司和责任中心，则默认第一个
            if (companyId == null) {
                companyId = companies != null && companies.size() > 0 ? Long.parseLong(companies.get(0).getId()) : null;
            }
            if (countryId == null) {
                countryId = departments != null && departments.size() > 0 ? Long.parseLong(departments.get(0).getId()) : null;
            }
        }
        //搜索条件中加上当前登录人所属分公司
        //查询
        condition.setCompany(companyId);
        condition.setCountry(countryId);
        return accountMapper.accountMapTotal(condition);
    }

    @Override
    public List<Map<String, Object>> unitpricSql(AccountCondition condition) {
        return accountMapper.unitpricSql(condition);
    }

    @Override
    public BigDecimal selectCompletedMoney(Long pcid) {
        AccountbillpreResult result = accountbillpremapper.selectByPcid(pcid);
        if (result != null && result.getPabid() != null) {
            List<MssAccountbill> mssAccountbills = mssAccountbillMapper.selectListByIds(Convert.toStrArray(result.getPabid()));
            List<Long> ids = new ArrayList<>();
            if (mssAccountbills != null && mssAccountbills.size() > 0) {
                for (MssAccountbill mssAccountbill : mssAccountbills) {
                    if (mssAccountbill.getStatus() == 7) {
                        ids.add(mssAccountbill.getId());
                    }
                }
            }
            if (ids.size() > 0) {
                Map<String, Object> map = new HashMap<>();
                map.put("pcid", pcid);
                map.put("billId", ids);

                return accountMapper.selectCompletedMoney(map);
            } else {
                return new BigDecimal(0);
            }
        } else {
            return new BigDecimal(0);
        }
    }

    @Override
    public Map<String, Object> dataVerification(List<AccountBaseResult> list, String version) throws Exception {
        BigDecimal z = new BigDecimal(0);
        List<AccountBaseResult> preserve = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();
        //获取当前时间的期号和上个月期号
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMM");
        String no = formatter.format(new Date());
        Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        c.add(Calendar.MONTH, -1);
        Date m = c.getTime();
        String mon = formatter.format(m);

        if (list != null && list.size() > 0) {
            for (AccountBaseResult obj : list) {
                if ("小计".equals(obj.getProjectname()) || "合计".equals(obj.getProjectname())) {
                    continue;
                }
                if (!StringUtils.isEmpty(obj.getAccountno()) && !obj.getAccountno().equals(no) && !obj.getAccountno().equals(mon)) {
                    obj.setError("当前时间只能导入期号为【" + mon + "、" + no + "】的台账；");
                    continue;
                }

                AccountCondition condition = new AccountCondition();
                condition.setAccountno(obj.getAccountno());
                condition.setAmmeterid(obj.getAmmeterid());
                List<AccountBaseResult> data = accountMapper.selectAccountByParams(condition);
                condition.setAccountno(no);
                Boolean ifNext = accountMapper.selectcurrent(condition);

                if (ifNext != null && ifNext && obj.getAccountno().equals(mon)) {
                    obj.setError("本期已录入台账，上期台账不能导入；");
                    continue;
                }
                if (data == null || data.size() == 0) {
                    obj.setError("找不到对应的台账信息；");
                    continue;
                } else {
                    obj.setPrevhighreadings(ifnull(obj.getPrevhighreadings(), z).setScale(4, BigDecimal.ROUND_HALF_DOWN));
                    obj.setPrevflatreadings(ifnull(obj.getPrevflatreadings(), z).setScale(4, BigDecimal.ROUND_HALF_DOWN));
                    obj.setPrevlowreadings(ifnull(obj.getPrevlowreadings(), z).setScale(4, BigDecimal.ROUND_HALF_DOWN));
                    obj.setCurhighreadings(ifnull(obj.getCurhighreadings(), z).setScale(4, BigDecimal.ROUND_HALF_DOWN));
                    obj.setCurflatreadings(ifnull(obj.getCurflatreadings(), z).setScale(4, BigDecimal.ROUND_HALF_DOWN));
                    obj.setCurlowreadings(ifnull(obj.getCurlowreadings(), z).setScale(4, BigDecimal.ROUND_HALF_DOWN));
                    obj.setTransformerullage(ifnull(obj.getTransformerullage(), z).setScale(4, BigDecimal.ROUND_HALF_DOWN));
                    obj.setPrevtotalreadings(ifnull(obj.getPrevtotalreadings(), z).setScale(4, BigDecimal.ROUND_HALF_DOWN));
                    obj.setCurtotalreadings(ifnull(obj.getCurtotalreadings(), z).setScale(4, BigDecimal.ROUND_HALF_DOWN));
                    obj.setInputtaxticketmoney(ifnull(obj.getInputtaxticketmoney(), z).setScale(2, BigDecimal.ROUND_HALF_DOWN));
                    obj.setInputticketmoney(ifnull(obj.getInputticketmoney(), z).setScale(2, BigDecimal.ROUND_HALF_DOWN));
                    obj.setTicketmoney(ifnull(obj.getTicketmoney(), z).setScale(2, BigDecimal.ROUND_HALF_DOWN));
                    obj.setTaxticketmoney(ifnull(obj.getTaxticketmoney(), z).setScale(2, BigDecimal.ROUND_HALF_DOWN));
                    obj.setUllagemoney(ifnull(obj.getUllagemoney(), z).setScale(2, BigDecimal.ROUND_HALF_DOWN));
                    obj.setStartdate(obj.getStartdate() == null ? "" : obj.getStartdate());
                    AccountBaseResult query = data.get(0);
                    String remark = "";
                    if (query.getPrevhighreadings().compareTo(obj.getPrevhighreadings()) != 0) {
                        remark += "本期起始峰值 从" + query.getPrevhighreadings() + " 修改为 " + obj.getPrevhighreadings() + " ；";
                    }
                    if (query.getPrevflatreadings().compareTo(obj.getPrevflatreadings()) != 0) {
                        remark += "本期起始平值 从" + query.getPrevflatreadings() + " 修改为 " + obj.getPrevflatreadings() + " ；";
                    }
                    if (query.getPrevlowreadings().compareTo(obj.getPrevlowreadings()) != 0) {
                        remark += "本期起始谷值 从" + query.getPrevlowreadings() + " 修改为 " + obj.getPrevlowreadings() + " ；";
                    }
                    if (!query.getStartdate().equals(obj.getStartdate())) {
                        remark += "本期起始日期 从" + query.getStartdate() + " 修改为 " + obj.getStartdate() + "；";
                    }
                    if (query.getPrevtotalreadings().compareTo(obj.getPrevtotalreadings()) != 0) {
                        remark += "本期起度 从" + query.getPrevtotalreadings() + " 修改为 " + obj.getPrevtotalreadings() + " ；";
                    }

                    if (StringUtils.isEmpty(query.getRemark())) {
                        query.setRemark(remark);
                    } else {
                        String s = query.getRemark();
                        query.setRemark(s += remark);
                    }
                    query.setPrevhighreadings(ifnull(obj.getPrevhighreadings(), z));
                    query.setPrevflatreadings(ifnull(obj.getPrevflatreadings(), z));
                    query.setPrevlowreadings(ifnull(obj.getPrevlowreadings(), z));
                    query.setCurhighreadings(ifnull(obj.getCurhighreadings(), z));
                    query.setCurflatreadings(ifnull(obj.getCurflatreadings(), z));
                    query.setCurlowreadings(ifnull(obj.getCurlowreadings(), z));
                    query.setTransformerullage(ifnull(obj.getTransformerullage(), z));
                    query.setInputtaxticketmoney(ifnull(obj.getInputtaxticketmoney(), z));
                    query.setInputticketmoney(ifnull(obj.getInputticketmoney(), z));
                    query.setTicketmoney(ifnull(obj.getTicketmoney(), z));
                    query.setTaxticketmoney(ifnull(obj.getTaxticketmoney(), z));
                    query.setUllagemoney(ifnull(obj.getUllagemoney(), z));
                    query.setBz(obj.getBz());
                    query.setMulttimes(query.getMagnification());

                    if ("ln".equals(version)) {
                        query.setSupplybureauammetercode(obj.getAmmetercode());
                    }

                    StringBuffer rStr = new StringBuffer();
                    String srt = "";
                    BigDecimal taxrate = ifnull(obj.getTaxrate(), z);
                    query.setTaxrate(taxrate);
                    if (taxrate.compareTo(new BigDecimal(13)) != 0 && taxrate.compareTo(new BigDecimal(16)) != 0
                            && taxrate.compareTo(new BigDecimal(1)) != 0 && taxrate.compareTo(new BigDecimal(3)) != 0 && taxrate.compareTo(new BigDecimal(6)) != 0 && taxrate.compareTo(new BigDecimal(17)) != 0) {
                        rStr.append("税率填写不规范，请填写(1,3,6,13,16,17)中的一种");
                    }

                    //category 为1,2,3的电表/协议，站址产权归属与用电类型是否一致
                    if (query.getCategory() != null && (query.getCategory() == 1 || query.getCategory() == 2 || query.getCategory() == 3)) {
                        // 站址产权归属为“铁塔”时，用电类型只能是1411或1412；
                        if (query.getProperty() != null && query.getElectrotype() != null
                                && query.getProperty() == 2 && query.getElectrotype() != 1411 && query.getElectrotype() != 1412) {
                            rStr.append("电表/协议[" + query.getAmmetercode() + "]站址产权归属[铁塔]与用电类型不一致，需到基础数据维护信息;");
                        }
                        // 站址产权归属是非铁塔时，用电类型不能是1411或1412。
                        if (query.getProperty() != null && query.getElectrotype() != null
                                && query.getProperty() != 2 && (query.getElectrotype() == 1411 || query.getElectrotype() == 1412)) {
                            rStr.append("电表/协议[" + query.getAmmetercode() + "]站址产权归属[自有]与用电类型[铁塔公司基站]不一致，需到基础数据维护信息;");
                        }
                    }
                    //验证起始时间
                    srt = DataVerification.verifyStartDate(query, obj.getStartdate());
                    if (!StringUtils.isEmpty(srt)) {
                        rStr.append(srt);
                    } else {
                        query.setStartdate(obj.getStartdate());
                    }
                    //验证截止时间
                    srt = DataVerification.verifyEndDate(query, obj.getEnddate());
                    if (!StringUtils.isEmpty(srt)) {
                        rStr.append(srt);
                    } else {
                        query.setEnddate(obj.getEnddate());
                    }
                    //验证起度
                    srt = DataVerification.verifyPrevTotalReadings(query, obj.getPrevtotalreadings());
                    if (!StringUtils.isEmpty(srt)) {
                        rStr.append(srt);
                    } else {
                        query.setPrevtotalreadings(ifnull(obj.getPrevtotalreadings(), z));
                    }
                    //验证止度
                    srt = DataVerification.verifyCurTotalReadings(query, obj.getCurtotalreadings());
                    if (!StringUtils.isEmpty(srt)) {
                        rStr.append(srt);
                    } else {
                        query.setCurtotalreadings(ifnull(obj.getCurtotalreadings(), z));
                    }
                    //金额
                    query.setInputtaxticketmoney(DataVerification.verifyMoney(query.getCategory(), query.getAmmeteruse(), query.getInputtaxticketmoney()));
                    query.setInputticketmoney(DataVerification.verifyMoney(query.getCategory(), query.getAmmeteruse(), query.getInputticketmoney()));
                    query.setUllagemoney(DataVerification.verifyMoney(query.getCategory(), query.getAmmeteruse(), query.getUllagemoney()));
                    query.setTaxticketmoney(DataVerification.calculateActualMoney(query.getPercent(), query.getInputtaxticketmoney()));
                    query.setTicketmoney(DataVerification.calculateActualMoney(query.getPercent(), query.getInputticketmoney()));
                    //定额
                    BigDecimal quota = null;
                    try {
                        quota = this.getElectrQuota(query.getAmmeterid(), query.getStartdate(), query.getEnddate());
                    } catch (Exception e) {
                        quota = new BigDecimal(0);
                    }
                    query.setQuotareadings(quota);

                    //计算用电量
                    query.setCurusedreadings(DataVerification.calculateUsedReadings(query));
                    //计算总电量
                    query.setTotalusedreadings(DataVerification.calculateTotalReadings(query));
                    //计算税额
                    query.setTaxamount(DataVerification.countTaxamount(query));
                    //计算总价
                    query.setAccountmoney(DataVerification.calculateAccountMoney(query));
                    //计算单价
                    query.setUnitpirce(DataVerification.calculateUnitPriceByUsedMoney(query));
                    //计算浮动比
                    if (deployTo.equalsIgnoreCase("sc")) {
                        query.setQuotereadingsratio(DataVerification.calculateQuotereadingsratio(query));
                    }
                    if (deployTo.equalsIgnoreCase("ln")) {
                        query.setQuotereadingsratio(DataVerification.calculateQuotereadingsratioForLn(query));
                    }
                    if (query.getIschangeammeter() == 1 && query.getIsnew() == 1) {
                        if (query.getOldbillpower() != null && query.getOldbillpower().compareTo(BigDecimal.valueOf(0)) > 0) {
                            query.setTotalusedreadings(query.getTotalusedreadings().add(query.getOldbillpower()));
                            //从新计算单价
                            query.setUnitpirce(DataVerification.calculateUnitPriceByUsedMoney(query));
                        }
                        if (remark.indexOf("换表") == -1) {
                            remark += "换表，结清原电表读数【" + query.getOldbillpower().toString() + "】；";
                            query.setRemark(remark);
                        }
                    }

                    //最后的验证
                    rStr.append(DataVerification.requiredFieldValidator(query, version));
                    if (StringUtils.isEmpty(rStr.toString())) {
                        preserve.add(query);
                    }
                    rStr.append(DataVerification.unitpirceTips(query, version));
                    obj.setError(rStr.toString());
                    String str1 = "";
                    if (query.getMagnificationerr().equals(2)) {
                        str1 += "台账倍率【" + query.getMagnification() + "】与电表倍率【" + query.getAmmmulttimes() + "】不一致；";
                    }

                    if (query.getPercenterr().equals(2)) {
                        str1 += "台账分割比例【" + query.getPercent() + "】与电表分割比例【" + query.getAmmpercent() + "】不一致；";
                    }
                    obj.setCareful(str1);


                }

            }
            this.preserve(preserve);
        }
        map.put("list", list);
        map.put("number", preserve.size());
        return map;
    }

    @Override
    public void preserve(List<AccountBaseResult> list) {
        if (list != null && list.size() > 0) {
            User user = ShiroUtils.getUser();
            for (AccountBaseResult result : list) {

                if (user != null) {
                    //设置最后修改人
                    result.setLastediterid(user.getId());
                    result.setLasteditdate(new Date());
                    if (result.getAccountmoney() != null && result.getAccountmoney().compareTo(new BigDecimal(0)) != 0) {
                        result.setEffective(new BigDecimal(1));
                    }
                }
                result.setIsnew(0);
                accountMapper.updateExcel(result);
                accountMapper.deleteByAmmeterid(result.getAmmeterid());
            }
        }
    }

    @Override
    public Map<String, Object> selectIdsByParams(AccountCondition condition) {
        Map<String, Object> map = new HashMap<>();
        condition.setDeployTo("sc".equalsIgnoreCase(deployTo) ? "sc" : "ln");
        List<AccountBaseResult> baseResultList = accountMapper.selectDataByParams(condition);
        String str = "";
        String show = "";
        if (baseResultList != null && baseResultList.size() > 0) {
            for (AccountBaseResult result : baseResultList) {
                if ("sc".equalsIgnoreCase(deployTo)) {
                    if (result.getMagnificationerr().equals(2)) {
                        str += "电表/协议编号为【" + result.getAmmetercode() + "】的台账倍率【" + result.getMagnification() + "】与电表倍率【" + result.getAmmmulttimes() + "】不一致！ <br /> ";
                    }
                    if (result.getPercenterr().equals(2)) {
                        str += "电表/协议编号为【" + result.getAmmetercode() + "】的台账分割比例【" + result.getPercent() + "】与电表分割比例【" + result.getAmmpercent() + "】不一致！ <br /> ";
                    }
                    Integer electrotype = result.getElectrotype();
                    String stationaddresscode = result.getStationaddresscode();
                    boolean empty = StringUtils.isEmpty(stationaddresscode);
                    if (!empty && (electrotype == 1411 || electrotype == 1412)) { //铁塔

                        CheckStationInfoDto checkStationInfoDto = new CheckStationInfoDto();
                        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss:S");
                        String format = simpleDateFormat.format(new Date());
                        checkStationInfoDto.setServeenddate(format);
                        checkStationInfoDto.setStationaddr_code(stationaddresscode);
                        checkStationInfoDto.setFileid(result.getPcid());//台账id
                        checkStationInfoDto.setType("selfAccount");
                        Map<String, String> map1 = stationInfoService.checkStationInfo(checkStationInfoDto);
                        if (map1.get("iftimeout").equals("3")) {
                            if (map1.get("show") != null) {
                                str += "电表/协议编号为【" + result.getAmmetercode() + "】的台账所关联局站已到期 <br /> ";
                                show = "noshow";
                            }
                        }
                    }
                } else if ("ln".equalsIgnoreCase(deployTo)) {
                    if (result.getMagnificationerr().equals(2)) {
                        str += "项目名称为【" + result.getProjectname() + "】的台账倍率【" + result.getMagnification() + "】与电表倍率【" + result.getAmmmulttimes() + "】不一致！ <br /> ";
                    }

                    if (result.getPercenterr().equals(2)) {
                        str += "项目名称为【" + result.getProjectname() + "】的台账分割比例【" + result.getPercent() + "】与电表分割比例【" + result.getAmmpercent() + "】不一致！ <br /> ";
                    }
                }

            }
            List<Long> ids = accountMapper.selectIdsByParams(condition);
            if (ids == null) {
                ids = new ArrayList<>();
            }
            map.put("ids", ids);
            map.put("show", show);
            if (!StringUtils.isEmpty(str)) {
                map.put("str", str);
            }
        }


        return map;
    }

    @Override
    public Map<String, Object> selectIdsByParamsmap(AccountCondition condition) {
        Map<String, Object> map = new HashMap<>();
        condition.setDeployTo("sc".equalsIgnoreCase(deployTo) ? "sc" : "ln");
        List<AccountBaseResult> baseResultList = accountMapper.selectDataByParamsmap(condition);
        String str = "";
        if (baseResultList != null && baseResultList.size() > 0) {
            for (AccountBaseResult result : baseResultList) {
                if ("sc".equalsIgnoreCase(deployTo)) {
                    if (result.getMagnificationerr().equals(2)) {
                        str += "电表/协议编号为【" + result.getAmmetercode() + "】的台账倍率【" + result.getMagnification() + "】与电表倍率【" + result.getAmmmulttimes() + "】不一致！ <br /> ";
                    }

                    if (result.getPercenterr().equals(2)) {
                        str += "电表/协议编号为【" + result.getAmmetercode() + "】的台账分割比例【" + result.getPercent() + "】与电表分割比例【" + result.getAmmpercent() + "】不一致！ <br /> ";
                    }
                } else if ("ln".equalsIgnoreCase(deployTo)) {
                    if (result.getMagnificationerr().equals(2)) {
                        str += "项目名称为【" + result.getProjectname() + "】的台账倍率【" + result.getMagnification() + "】与电表倍率【" + result.getAmmmulttimes() + "】不一致！ <br /> ";
                    }

                    if (result.getPercenterr().equals(2)) {
                        str += "项目名称为【" + result.getProjectname() + "】的台账分割比例【" + result.getPercent() + "】与电表分割比例【" + result.getAmmpercent() + "】不一致！ <br /> ";
                    }
                }

            }
            List<Long> ids = accountMapper.selectIdsByParamsmap(condition);
            if (ids == null) {
                ids = new ArrayList<>();
            }
            map.put("ids", ids);
            if (!StringUtils.isEmpty(str)) {
                map.put("str", str);
            }
        }


        return map;
    }

    @Override
    public List<Map<String, Object>> selectCitySmallSize(AccountCondition condition) {
        List<Map<String, Object>> list = accountMapper.selectCitySmallSize(condition);
        if (list != null && list.size() > 0) {
            for (Map<String, Object> map : list) {
                Long orgid = (Long) map.get("orgid");
                map.put("orgname", cascadeCountry(orgid));
                AccountCondition oldcondition = new AccountCondition();
                oldcondition.setCountry(orgid);
                if (!StringUtils.isEmpty(condition.getStartAccountno())) {
                    String[] sAttay = condition.getStartAccountno().split("-");
                    oldcondition.setStartAccountno((Integer.parseInt(sAttay[0]) - 1) + "-" + sAttay[1]);
                }
                if (!StringUtils.isEmpty(condition.getEndAccountno())) {
                    String[] sAttay = condition.getEndAccountno().split("-");
                    oldcondition.setEndAccountno((Integer.parseInt(sAttay[0]) - 1) + "-" + sAttay[1]);
                }

                AccountBaseResult result = accountMapper.selectOldCitySmallSize(oldcondition);
                map.put("oldtotalusedreadings", result.getTotalusedreadings());
                map.put("oldaccountmoney", result.getAccountmoney());
                map.put("area", accountMapper.selectArea(orgid));
            }
        }
        return list;
    }

    @Override
    public XSSFWorkbook exportSHX(AccountCondition condition) {
        // 声明一个工作簿
        XSSFWorkbook wb = new XSSFWorkbook();
        // 生成一个表格
        XSSFSheet sheet = wb.createSheet("sheet1");

        // 生成一种样式
        XSSFCellStyle style = wb.createCellStyle();
        // 设置样式
        style.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        style.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        style.setBorderRight(HSSFCellStyle.BORDER_THIN);
        style.setBorderTop(HSSFCellStyle.BORDER_THIN);
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);

        // 生成一种字体
        XSSFFont font = wb.createFont();
        // 设置字体
        font.setFontName("微软雅黑");
        // 设置字体大小
        font.setFontHeightInPoints((short) 12);
        // 字体加粗
        font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
        // 在样式中引用这种字体
        style.setFont(font);
        // 生成并设置另一个样式
        XSSFCellStyle style2 = wb.createCellStyle();
        style2.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        style2.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        style2.setBorderRight(HSSFCellStyle.BORDER_THIN);
        style2.setBorderTop(HSSFCellStyle.BORDER_THIN);
        style2.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        style2.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);

        // 生成另一种字体2
        XSSFFont font2 = wb.createFont();
        // 设置字体
        font2.setFontName("微软雅黑");
        // 设置字体大小
        font2.setFontHeightInPoints((short) 12);
        // 字体加粗
        style2.setFont(font2);

        // 生成表格的第一行
        // 第一行表头
        XSSFRow row = sheet.createRow(0);
        XSSFCell cell = row.createCell(0);
        sheet.autoSizeColumn(0, true);
        cell.setCellValue("划小单元名称");
        cell.setCellStyle(style);
        cell = row.createCell(1);
        cell.setCellValue("总电量");
        cell.setCellStyle(style);
        cell = row.createCell(4);
        cell.setCellValue("总电费");
        cell.setCellStyle(style);
        cell = row.createCell(7);
        cell.setCellValue("平均电价");
        cell.setCellStyle(style);
        cell = row.createCell(8);
        cell.setCellValue("平均PUE");
        cell.setCellStyle(style);
        cell = row.createCell(9);
        cell.setCellValue("办公营业单位面积电量");
        cell.setCellStyle(style);
        cell = row.createCell(10);
        cell.setCellValue("呼叫中心每坐席电量");
        cell.setCellStyle(style);
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 0, 0));
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 1, 3));
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 4, 6));
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 7, 7));
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 8, 8));
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 9, 9));
        sheet.addMergedRegion(new CellRangeAddress(0, 1, 10, 10));
        // 第二行表头
        row = sheet.createRow(1);
        cell = row.createCell(1);
        cell.setCellValue("上年数量");
        cell.setCellStyle(style);
        cell = row.createCell(2);
        cell.setCellValue("本年数量");
        cell.setCellStyle(style);
        cell = row.createCell(3);
        cell.setCellValue("与上年同期比较增减(%)");
        cell.setCellStyle(style);
        cell = row.createCell(4);
        cell.setCellValue("上年数量");
        cell.setCellStyle(style);
        cell = row.createCell(5);
        cell.setCellValue("本年数量");
        cell.setCellStyle(style);
        cell = row.createCell(6);
        cell.setCellValue("与上年同期比较增减(%)");
        cell.setCellStyle(style);

        List<String> titleList = new ArrayList<>();
        titleList.add("orgname");
        titleList.add("oldtotalusedreadings");
        titleList.add("totalusedreadings");
        titleList.add("compareTotal");
        titleList.add("oldaccountmoney");
        titleList.add("accountmoney");
        titleList.add("compareMoney");
        titleList.add("averageprice");
        titleList.add("pue");
        titleList.add("areatotal");
        titleList.add("hujiao");

        List<Map<String, Object>> list = selectCitySmallSize(condition);
        if (list != null && list.size() > 0) {
            BigDecimal z = BigDecimal.valueOf(0);
            for (int i = 0; i < list.size(); i++) {
                row = sheet.createRow(i + 2);
                Map tempMap = list.get(i);
                BigDecimal accountmoney = new BigDecimal(tempMap.get("accountmoney").toString());
                BigDecimal totalusedreadings = new BigDecimal(tempMap.get("totalusedreadings").toString());
                BigDecimal oldtotalusedreadings = new BigDecimal(tempMap.get("oldtotalusedreadings").toString());
                BigDecimal oldaccountmoney = new BigDecimal(tempMap.get("oldaccountmoney").toString());
                BigDecimal area = new BigDecimal(tempMap.get("area").toString());
                for (int j = 0; j < titleList.size(); j++) {
                    String title = titleList.get(j);
                    if ("pue".equals(title) || "hujiao".equals(title)) {
                        continue;
                    } else if ("averageprice".equals(title)) {
                        cell = row.createCell(j);
                        BigDecimal total = accountmoney.divide(totalusedreadings, 2, BigDecimal.ROUND_HALF_DOWN);
                        cell.setCellValue(total.toString());
                        cell.setCellStyle(style2);
                    } else if ("compareTotal".equals(title)) {
                        BigDecimal total;
                        if (oldtotalusedreadings.compareTo(z) > 0) {
                            total = totalusedreadings.subtract(oldtotalusedreadings).divide(oldtotalusedreadings, 2, BigDecimal.ROUND_HALF_DOWN);
                        } else {
                            total = z;
                        }
                        cell = row.createCell(j);
                        cell.setCellValue(total.toString());
                        cell.setCellStyle(style2);
                    } else if ("compareMoney".equals(title)) {
                        BigDecimal total;
                        if (oldtotalusedreadings.compareTo(z) > 0) {
                            total = accountmoney.subtract(oldaccountmoney).divide(oldaccountmoney, 2, BigDecimal.ROUND_HALF_DOWN);
                        } else {
                            total = z;
                        }
                        cell = row.createCell(j);
                        cell.setCellValue(total.toString());
                        cell.setCellStyle(style2);
                    } else if ("areatotal".equals(title)) {
                        BigDecimal total;
                        if (area.compareTo(z) > 0) {
                            total = oldtotalusedreadings.divide(area, 2, BigDecimal.ROUND_HALF_DOWN);
                        } else {
                            total = area;
                        }
                        cell = row.createCell(j);
                        cell.setCellValue(total.toString());
                        cell.setCellStyle(style2);
                    } else {
                        cell = row.createCell(j);
                        cell.setCellValue(null == tempMap.get(titleList.get(j)) ? "" : tempMap.get(titleList.get(j)).toString());
                        cell.setCellStyle(style2);
                    }

                }
            }
        }

        return wb;
    }

    @Override
    public XSSFWorkbook exportzy(List<AccountBaseResult> list, String version) {
        BigDecimal z = new BigDecimal(0);
        String[] header = null;
        String[] headerSC = new String[]{"电表/协议id", "项目名称", "电表户号/协议编码", "    期号    ", "起始日期(必填)", "截止日期(必填)", "本期峰段起度(选填)", "本期平段起度(选填)", "本期谷段起度(选填)", "本期起度(有表类普通电表必填)", "本期峰段止度(选填)", "本期平段止度(选填)", "本期谷段止度(选填)", "本期止度(有表类普通电表必填)", "电损(度)(选填)", "专票含税金额(元)(专票普票必填其中一项)", "专票税率(%)", "专票税额", "普票含税金额(元)(专票普票必填其中一项)", "其他(元)(选填)", "实缴费用(元)含税", "    倍率    ", "用电量(度)", "总电量(度)", "电价(元)", "    备注(选填)    ", "分割比例(%)"};
        String[] headerLN = new String[]{"电表/协议id", "项目名称", "供电局电表编号", "    期号    ", "起始日期(必填)", "截止日期(必填)", "本期峰段起度(选填)", "本期平段起度(选填)", "本期谷段起度(选填)", "本期起度(有表类普通电表必填)", "本期峰段止度(选填)", "本期平段止度(选填)", "本期谷段止度(选填)", "本期止度(有表类普通电表必填)", "电损(度)(选填)", "专票含税金额(元)(专票普票必填其中一项)", "专票税率(%)", "专票税额", "普票含税金额(元)(专票普票必填其中一项)", "其他(元)(选填)", "实缴费用(元)含税", "    倍率    ", "用电量(度)", "总电量(度)", "电价(元)", "    备注(选填)    ", "分割比例(%)"};
        if ("sc".equals(version)) {
            header = headerSC;
        } else if ("ln".equals(version)) {
            header = headerLN;
        }

        // 声明一个工作簿
        XSSFWorkbook wb = new XSSFWorkbook();
        // 生成一个表格
        XSSFSheet sheet = wb.createSheet("sheet1");

        // 生成一种样式
        XSSFCellStyle style = wb.createCellStyle();
        // 设置样式
        style.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        style.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        style.setBorderRight(HSSFCellStyle.BORDER_THIN);
        style.setBorderTop(HSSFCellStyle.BORDER_THIN);
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);

        // 生成一种字体
        XSSFFont font = wb.createFont();
        // 设置字体
        font.setFontName("微软雅黑");
        // 设置字体大小
        font.setFontHeightInPoints((short) 12);
        // 字体加粗
        font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
        // 在样式中引用这种字体
        style.setFont(font);
        // 生成并设置另一个样式
        XSSFCellStyle style2 = wb.createCellStyle();
        style2.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        style2.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        style2.setBorderRight(HSSFCellStyle.BORDER_THIN);
        style2.setBorderTop(HSSFCellStyle.BORDER_THIN);
        style2.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        style2.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);

        // 生成另一种字体2
        XSSFFont font2 = wb.createFont();
        // 设置字体
        font2.setFontName("微软雅黑");
        // 设置字体大小
        font2.setFontHeightInPoints((short) 12);
        // 字体加粗
        style2.setFont(font2);

        // 生成表格的第一行
        // 第一行表头
        XSSFRow row = sheet.createRow(0);
        for (int i = 0; i < header.length; i++) {
            XSSFCell cell = row.createCell(i);
            cell.setCellValue(header[i]);
            cell.setCellStyle(style);
            sheet.autoSizeColumn(i, true);
        }


        sheet.setColumnWidth(0, 0);
        if (list != null && list.size() > 0) {
            for (int i = 0; i < list.size(); i++) {
                AccountBaseResult result = list.get(i);
                row = sheet.createRow(i + 1);
                XSSFCell cell = row.createCell(0);
                cell.setCellValue(result.getAmmeterid().toString());
                cell.setCellStyle(style2);
                cell = row.createCell(1);
                cell.setCellValue(result.getProjectname());
                cell.setCellStyle(style2);
                if ("sc".equals(version)) {
                    cell = row.createCell(2);
                    cell.setCellValue(result.getAmmetercode());
                    cell.setCellStyle(style2);
                } else if ("ln".equals(version)) {
                    cell = row.createCell(2);
                    cell.setCellValue(result.getSupplybureauammetercode());
                    cell.setCellStyle(style2);
                }
                cell = row.createCell(3);
                cell.setCellValue(result.getAccountno());
                cell.setCellStyle(style2);
                cell = row.createCell(4);
                cell.setCellValue(result.getStartdate());
                cell.setCellStyle(style2);
                cell = row.createCell(5);
                cell.setCellValue(result.getEnddate());
                cell.setCellStyle(style2);
                cell = row.createCell(6);
                cell.setCellValue(ifnull(result.getPrevhighreadings(), z).toString());
                cell.setCellStyle(style2);
                cell = row.createCell(7);
                cell.setCellValue(ifnull(result.getPrevflatreadings(), z).toString());
                cell.setCellStyle(style2);
                cell = row.createCell(8);
                cell.setCellValue(ifnull(result.getPrevlowreadings(), z).toString());
                cell.setCellStyle(style2);
                cell = row.createCell(9);
                cell.setCellValue(ifnull(result.getPrevtotalreadings(), z).toString());
                cell.setCellStyle(style2);
                cell = row.createCell(10);
                cell.setCellValue(ifnull(result.getCurhighreadings(), z).toString());
                cell.setCellStyle(style2);
                cell = row.createCell(11);
                cell.setCellValue(ifnull(result.getCurflatreadings(), z).toString());
                cell.setCellStyle(style2);
                cell = row.createCell(12);
                cell.setCellValue(ifnull(result.getCurlowreadings(), z).toString());
                cell.setCellStyle(style2);
                cell = row.createCell(13);
                cell.setCellValue(ifnull(result.getCurtotalreadings(), z).toString());
                cell.setCellStyle(style2);
                cell = row.createCell(14);
                cell.setCellValue(ifnull(result.getTransformerullage(), z).toString());
                cell.setCellStyle(style2);
                cell = row.createCell(15);
                cell.setCellValue(ifnull(result.getInputtaxticketmoney(), z).setScale(2, BigDecimal.ROUND_HALF_DOWN).toString());
                cell.setCellStyle(style2);
                cell = row.createCell(16);
                cell.setCellValue((result.getTaxrate() == null || result.getTaxrate().compareTo(z) == 0 ? BigDecimal.valueOf(13) : result.getTaxrate()).toString());
                cell.setCellStyle(style2);
                cell = row.createCell(17);
                cell.setCellValue(ifnull(result.getTaxamount(), z).setScale(2, BigDecimal.ROUND_HALF_DOWN).toString());
                cell.setCellStyle(style2);
                cell = row.createCell(18);
                cell.setCellValue(ifnull(result.getInputticketmoney(), z).setScale(2, BigDecimal.ROUND_HALF_DOWN).toString());
                cell.setCellStyle(style2);
                cell = row.createCell(19);
                cell.setCellValue(ifnull(result.getUllagemoney(), z).setScale(2, BigDecimal.ROUND_HALF_DOWN).toString());
                cell.setCellStyle(style2);
                cell = row.createCell(20);
                cell.setCellValue(ifnull(result.getAccountmoney(), z).setScale(2, BigDecimal.ROUND_HALF_DOWN).toString());
                cell.setCellStyle(style2);
                cell = row.createCell(21);
                cell.setCellValue(ifnull(result.getMagnification(), z).toString());
                cell.setCellStyle(style2);
                cell = row.createCell(22);
                cell.setCellValue(ifnull(result.getCurusedreadings(), z).toString());
                cell.setCellStyle(style2);
                cell = row.createCell(23);
                cell.setCellValue(ifnull(result.getTotalusedreadings(), z).toString());
                cell.setCellStyle(style2);
                cell = row.createCell(24);
                cell.setCellValue(ifnull(result.getUnitpirce(), z).setScale(2, BigDecimal.ROUND_HALF_DOWN).toString());
                cell.setCellStyle(style2);
                cell = row.createCell(25);
                cell.setCellValue(result.getRemark());
                cell.setCellStyle(style2);
                cell = row.createCell(26);
                cell.setCellValue(ifnull(result.getPercent(), z).toString());
                cell.setCellStyle(style2);
            }
        }
        return wb;
    }

    @Override
    public void exportcx(HttpServletResponse response, List<AccountBaseResult> list, String moreMes) {
        if (list != null && list.size() > 0) {
            setdata(list);
            if ("ln".equalsIgnoreCase(deployTo)) {
                List<AccountExcelLN> lnList = new ArrayList<>();
                for (AccountBaseResult result : list) {
                    AccountExcelLN ln = new AccountExcelLN();
                    ln.setProjectname(result.getProjectname());
                    ln.setSupplybureauammetercode(result.getSupplybureauammetercode());
                    ln.setStationName(result.getStationName());
                    ln.setResstationcode(result.getResstationcode());
                    ln.setSubstation(result.getSubstation());
                    ln.setAccountno(result.getAccountno());
                    ln.setCompany(result.getCompany());
                    ln.setCountry(result.getCountry());
                    ln.setMagnification(result.getMagnification() == null ? "" : result.getMagnification().toString());
                    ln.setQuotareadings(result.getQuotareadings() == null ? "" : result.getQuotareadings().toString());
                    ln.setQuotereadingsratio(result.getQuotereadingsratio() == null ? "" : result.getQuotereadingsratio().toString());
                    ln.setStartdate(result.getStartdate());
                    ln.setEnddate(result.getEnddate());
                    ln.setPrevtotalreadings(result.getPrevtotalreadings() == null ? "" : result.getPrevtotalreadings().toString());
                    ln.setCurtotalreadings(result.getCurtotalreadings() == null ? "" : result.getCurtotalreadings().toString());

                    ln.setPrevhighreadings(result.getPrevhighreadings() == null ? "" : result.getPrevhighreadings().toString());
                    ln.setPrevflatreadings(result.getPrevflatreadings() == null ? "" : result.getPrevflatreadings().toString());
                    ln.setPrevlowreadings(result.getPrevlowreadings() == null ? "" : result.getPrevlowreadings().toString());
                    ln.setCurhighreadings(result.getCurhighreadings() == null ? "" : result.getCurhighreadings().toString());
                    ln.setCurflatreadings(result.getCurflatreadings() == null ? "" : result.getCurflatreadings().toString());
                    ln.setCurlowreadings(result.getCurlowreadings() == null ? "" : result.getCurlowreadings().toString());

                    ln.setCurusedreadings(result.getCurusedreadings() == null ? "" : result.getCurusedreadings().toString());
                    ln.setTransformerullage(result.getTransformerullage() == null ? "" : result.getTransformerullage().toString());
                    ln.setUnitpirce(result.getUnitpirce() == null ? "" : result.getUnitpirce().toString());
                    ln.setInputticketmoney(result.getInputticketmoney() == null ? "" : result.getInputticketmoney().toString());
                    ln.setInputtaxticketmoney(result.getInputtaxticketmoney() == null ? "" : result.getInputtaxticketmoney().toString());
                    ln.setTicketmoney(result.getTicketmoney() == null ? "" : result.getTicketmoney().toString());
                    ln.setTaxticketmoney(result.getTaxticketmoney() == null ? "" : result.getTaxticketmoney().toString());
                    ln.setTaxrate(result.getTaxrate() == null ? "" : result.getTaxrate().toString());
                    ln.setTaxamount(result.getTaxamount() == null ? "" : result.getTaxamount().toString());
                    ln.setUllagemoney(result.getUllagemoney() == null ? "" : result.getUllagemoney().toString());
                    ln.setAccountmoney(result.getAccountmoney() == null ? "" : result.getAccountmoney().toString());
                    ln.setTotalBHS(result.getTotalBHS() == null ? "" : result.getTotalBHS().toString());
                    ln.setRemark(result.getRemark());
                    ln.setCategoryname(result.getCategoryname());
                    ln.setElectrotypename(result.getElectrotypename());
                    ln.setAmmeterusename(result.getAmmeterusename());
                    ln.setType(result.getType());
                    ln.setStatus(result.getStatusName());
                    ln.setPercent(result.getPercent() == null ? "" : result.getPercent().toString());
                    ln.setTotalusedreadings(result.getTotalusedreadings() == null ? "" : result.getTotalusedreadings().toString());
                    ln.setNote(result.getNote());
                    ln.setInputname(result.getInputname());
                    ln.setDirectsupplyflag(result.getDirectsupplyflagname());

                    lnList.add(ln);
                }
                ExcelUtil<AccountExcelLN> excelUtil = new ExcelUtil<AccountExcelLN>(AccountExcelLN.class);
                excelUtil.exportExcelToBrowser(response, lnList, "台账查询导出");
            } else if (("sc").equalsIgnoreCase(deployTo)) {
                if ("more".equals(moreMes)) {
                    String paytypes[] = {"", "预交", "后交", ""};
                    String propertys[] = {"", "自留", "铁塔", "代持", "外租", "其他", "", "", ""};
                    List<AccountExcelSCmore> scList = new ArrayList<>();
                    for (AccountBaseResult result : list) {
                        AccountExcelSCmore sc = new AccountExcelSCmore();

                        sc.setPackagetype(result.getPackagetype());
                        sc.setFee(result.getFee());
                        sc.setAddress(result.getAddress());
                        sc.setElectrovalencenature(result.getElectrovalencenature());
                        try {
                            if (result.getProperty() != null)
                                sc.setProperty(propertys[result.getProperty()]);
                            else
                                sc.setProperty("");
                        } catch (Exception e) {
                            sc.setProperty("" + result.getProperty());
                        }
                        try {
                            if (result.getPaytype() != null)
                                sc.setPaytype(paytypes[result.getPaytype()]);
                            else
                                sc.setPaytype("");
                        } catch (Exception e) {
                            sc.setPaytype("" + result.getPaytype());
                        }

                        sc.setProjectname(result.getProjectname());
                        sc.setAmmetercode(result.getAmmetercode());
                        sc.setStationName(result.getStationName());
                        sc.setResstationcode(result.getResstationcode());
                        sc.setSubstation(result.getSubstation());
                        sc.setAccountno(result.getAccountno());
                        sc.setCompany(result.getCompany());
                        sc.setCountry(result.getCountry());
                        sc.setMagnification(result.getMagnification() == null ? "" : result.getMagnification().toString());
                        sc.setQuotareadings(result.getQuotareadings() == null ? "" : result.getQuotareadings().toString());
                        sc.setQuotereadingsratio(result.getQuotereadingsratio() == null ? "" : result.getQuotereadingsratio().toString());
                        sc.setStartdate(result.getStartdate());
                        sc.setEnddate(result.getEnddate());
                        sc.setPrevtotalreadings(result.getPrevtotalreadings() == null ? "" : result.getPrevtotalreadings().toString());
                        sc.setCurtotalreadings(result.getCurtotalreadings() == null ? "" : result.getCurtotalreadings().toString());

                        sc.setPrevhighreadings(result.getPrevhighreadings() == null ? "" : result.getPrevhighreadings().toString());
                        sc.setPrevflatreadings(result.getPrevflatreadings() == null ? "" : result.getPrevflatreadings().toString());
                        sc.setPrevlowreadings(result.getPrevlowreadings() == null ? "" : result.getPrevlowreadings().toString());
                        sc.setCurhighreadings(result.getCurhighreadings() == null ? "" : result.getCurhighreadings().toString());
                        sc.setCurflatreadings(result.getCurflatreadings() == null ? "" : result.getCurflatreadings().toString());
                        sc.setCurlowreadings(result.getCurlowreadings() == null ? "" : result.getCurlowreadings().toString());
                        sc.setHighreadings(result.getHighreadings() == null ? "" : result.getHighreadings().toString());
                        sc.setFlatreadings(result.getFlatreadings() == null ? "" : result.getFlatreadings().toString());
                        sc.setLowreadings(result.getLowreadings() == null ? "" : result.getLowreadings().toString());


                        sc.setCurusedreadings(result.getCurusedreadings() == null ? "" : result.getCurusedreadings().toString());
                        sc.setTransformerullage(result.getTransformerullage() == null ? "" : result.getTransformerullage().toString());
                        sc.setUnitpirce(result.getUnitpirce() == null ? "" : result.getUnitpirce().toString());
                        sc.setInputticketmoney(result.getInputticketmoney() == null ? "" : result.getInputticketmoney().toString());
                        sc.setInputtaxticketmoney(result.getInputtaxticketmoney() == null ? "" : result.getInputtaxticketmoney().toString());
                        sc.setTickettaxamount(result.getTickettaxamount() == null ? "" : result.getTickettaxamount().toString());
                        sc.setTicketmoney(result.getTicketmoney() == null ? "" : result.getTicketmoney().toString());
                        sc.setTaxticketmoney(result.getTaxticketmoney() == null ? "" : result.getTaxticketmoney().toString());
                        sc.setTaxrate(result.getTaxrate() == null ? "" : result.getTaxrate().toString());
                        sc.setTaxamount(result.getTaxamount() == null ? "" : result.getTaxamount().toString());
                        sc.setUllagemoney(result.getUllagemoney() == null ? "" : result.getUllagemoney().toString());
                        sc.setAccountmoney(result.getAccountmoney() == null ? "" : result.getAccountmoney().toString());
                        sc.setTotalBHS(result.getTotalBHS() == null ? "" : result.getTotalBHS().toString());
                        sc.setRemark(result.getRemark());
                        sc.setCategoryname(result.getCategoryname());
                        sc.setElectrotypename(result.getElectrotypename());
                        sc.setAmmeterusename(result.getAmmeterusename());
                        sc.setType(result.getType());
                        sc.setStatus(result.getStatusName());
                        sc.setPercent(result.getPercent() == null ? "" : result.getPercent().toString());
                        sc.setNmccode(result.getNmccode());
                        sc.setNml2100(result.getNml2100());
                        sc.setNml1800(result.getNml1800());
                        sc.setNmcl800m(result.getNmcl800m());
                        sc.setContractothpart(result.getContractothpart());
                        sc.setBillId(result.getBillId());
                        sc.setFillinname(result.getFillinname());
                        sc.setBudgetsetname(result.getBudgetsetname());
                        sc.setTotalusedreadings(result.getTotalusedreadings() == null ? "" : result.getTotalusedreadings().toString());
                        sc.setNote(result.getNote());
                        sc.setInputname(result.getInputname());
                        sc.setDirectsupplyflag(result.getDirectsupplyflagname());

                        scList.add(sc);
                    }
                    ExcelUtil<AccountExcelSCmore> excelUtil = new ExcelUtil<>(AccountExcelSCmore.class);
                    excelUtil.exportExcelToBrowser(response, scList, "台账查询导出");
                } else {
                    List<AccountExcelSC> scList = new ArrayList<>();
                    for (AccountBaseResult result : list) {
                        AccountExcelSC sc = new AccountExcelSC();

                        sc.setProjectname(result.getProjectname());
                        sc.setAmmetercode(result.getAmmetercode());
                        sc.setStationName(result.getStationName());
                        sc.setResstationcode(result.getResstationcode());
                        sc.setSubstation(result.getSubstation());
                        sc.setAccountno(result.getAccountno());
                        sc.setCompany(result.getCompany());
                        sc.setCountry(result.getCountry());
                        sc.setMagnification(result.getMagnification() == null ? "" : result.getMagnification().toString());
                        sc.setQuotareadings(result.getQuotareadings() == null ? "" : result.getQuotareadings().toString());
                        sc.setQuotereadingsratio(result.getQuotereadingsratio() == null ? "" : result.getQuotereadingsratio().toString());
                        sc.setStartdate(result.getStartdate());
                        sc.setEnddate(result.getEnddate());
                        sc.setPrevtotalreadings(result.getPrevtotalreadings() == null ? "" : result.getPrevtotalreadings().toString());
                        sc.setCurtotalreadings(result.getCurtotalreadings() == null ? "" : result.getCurtotalreadings().toString());

                        sc.setPrevhighreadings(result.getPrevhighreadings() == null ? "" : result.getPrevhighreadings().toString());
                        sc.setPrevflatreadings(result.getPrevflatreadings() == null ? "" : result.getPrevflatreadings().toString());
                        sc.setPrevlowreadings(result.getPrevlowreadings() == null ? "" : result.getPrevlowreadings().toString());
                        sc.setCurhighreadings(result.getCurhighreadings() == null ? "" : result.getCurhighreadings().toString());
                        sc.setCurflatreadings(result.getCurflatreadings() == null ? "" : result.getCurflatreadings().toString());
                        sc.setCurlowreadings(result.getCurlowreadings() == null ? "" : result.getCurlowreadings().toString());
                        sc.setHighreadings(result.getHighreadings() == null ? "" : result.getHighreadings().toString());
                        sc.setFlatreadings(result.getFlatreadings() == null ? "" : result.getFlatreadings().toString());
                        sc.setLowreadings(result.getLowreadings() == null ? "" : result.getLowreadings().toString());

                        sc.setCurusedreadings(result.getCurusedreadings() == null ? "" : result.getCurusedreadings().toString());
                        sc.setTransformerullage(result.getTransformerullage() == null ? "" : result.getTransformerullage().toString());
                        sc.setUnitpirce(result.getUnitpirce() == null ? "" : result.getUnitpirce().toString());
                        sc.setInputticketmoney(result.getInputticketmoney() == null ? "" : result.getInputticketmoney().toString());
                        sc.setInputtaxticketmoney(result.getInputtaxticketmoney() == null ? "" : result.getInputtaxticketmoney().toString());
                        sc.setTickettaxamount(result.getTickettaxamount() == null ? "" : result.getTickettaxamount().toString());
                        sc.setTicketmoney(result.getTicketmoney() == null ? "" : result.getTicketmoney().toString());
                        sc.setTaxticketmoney(result.getTaxticketmoney() == null ? "" : result.getTaxticketmoney().toString());
                        sc.setTaxrate(result.getTaxrate() == null ? "" : result.getTaxrate().toString());
                        sc.setTaxamount(result.getTaxamount() == null ? "" : result.getTaxamount().toString());
                        sc.setUllagemoney(result.getUllagemoney() == null ? "" : result.getUllagemoney().toString());
                        sc.setAccountmoney(result.getAccountmoney() == null ? "" : result.getAccountmoney().toString());
                        sc.setTotalBHS(result.getTotalBHS() == null ? "" : result.getTotalBHS().toString());
                        sc.setRemark(result.getRemark());
                        sc.setCategoryname(result.getCategoryname());
                        sc.setElectrotypename(result.getElectrotypename());
                        sc.setAmmeterusename(result.getAmmeterusename());
                        sc.setType(result.getType());
                        sc.setStatus(result.getStatusName());
                        sc.setPercent(result.getPercent() == null ? "" : result.getPercent().toString());
                        sc.setNmccode(result.getNmccode());
                        sc.setNml2100(result.getNml2100());
                        sc.setNml1800(result.getNml1800());
                        sc.setNmcl800m(result.getNmcl800m());
                        sc.setContractothpart(result.getContractothpart());
                        sc.setBillId(result.getBillId());
                        sc.setFillinname(result.getFillinname());
                        sc.setBudgetsetname(result.getBudgetsetname());
                        sc.setTotalusedreadings(result.getTotalusedreadings() == null ? "" : result.getTotalusedreadings().toString());
                        sc.setNote(result.getNote());
                        sc.setInputname(result.getInputname());
                        sc.setDirectsupplyflag(result.getDirectsupplyflagname());

                        scList.add(sc);
                    }
                    ExcelUtil<AccountExcelSC> excelUtil = new ExcelUtil<>(AccountExcelSC.class);
                    excelUtil.exportExcelToBrowser(response, scList, "台账查询导出");
                }
            }
        }
    }

    private void setdata(List<AccountBaseResult> list) {
        for (AccountBaseResult result : list) {
            Integer status = result.getStatus();
            if (status != null) {
                String statusName = "";
                if (1 == status) {
                    statusName = "正常";
                } else if (2 == status) {
                    statusName = "报账中";
                } else if (3 == status) {
                    statusName = "报账完成";
                } else if (4 == status) {
                    statusName = "已生成归集单";
                } else if (5 == status) {
                    statusName = "已退回";
                }
                result.setStatusName(statusName);
            }

            if (result.getProperty() != null) {
                String type = "";
                if (result.getProperty() == 1) {
                    type += "自有";
                } else if (result.getProperty() == 2) {
                    type += "铁塔";
                }
                if (result.getAccounttype() != null) {
                    if (result.getAccounttype() == 2) {
                        if (result.getAccountestype() == 1) {
                            type += "预估";
                        } else if (result.getAccountestype() == 2) {
                            type += "挂账";
                        } else if (result.getAccountestype() == 3) {
                            type += "预付";
                        }
                    }
                }
                result.setType(type);
            }

            Integer directsupplyflag = result.getDirectsupplyflag();
            if (directsupplyflag != null) {
                String directsupplyflagName = "";
                if (directsupplyflag.equals(1)) {
                    directsupplyflagName = "直供电";
                } else if (directsupplyflag.equals(2)) {
                    directsupplyflagName = "转供电";
                }
                result.setDirectsupplyflagname(directsupplyflagName);
            }

            Integer category = result.getCategory();
            if (category != null) {
                String categoryName = "";
                if (1 == category) {
                    categoryName = "电表";
                } else if (2 == category) {
                    categoryName = "支出无表协议";
                } else if (3 == category) {
                    categoryName = "支出有表协议";
                } else if (4 == category) {
                    categoryName = "收入无表协议";
                } else if (5 == category) {
                    categoryName = "收入有表协议";
                }

                result.setCategoryname(categoryName);
            }

            String ammeterUse = result.getAmmeteruse();
            if (ammeterUse != null) {
                String ammeterUseName = "";
                if ("1".equals(ammeterUse)) {
                    ammeterUseName = "实际报账";
                } else if ("2".equals(ammeterUse)) {
                    ammeterUseName = "预提冲销";
                } else if ("3".equals(ammeterUse)) {
                    ammeterUseName = "回收电费";
                }
                result.setAmmeterusename(ammeterUseName);
            }

            BigDecimal totalBHS = null;
            BigDecimal taxticketmoney = result.getTaxticketmoney() == null ? BigDecimal.valueOf(0) : result.getTaxticketmoney();
            BigDecimal ticketmoney = result.getTicketmoney() == null ? BigDecimal.valueOf(0) : result.getTicketmoney();
            BigDecimal ullagemoney = result.getUllagemoney() == null ? BigDecimal.valueOf(0) : result.getUllagemoney();
            BigDecimal taxamount = result.getTaxamount() == null ? BigDecimal.valueOf(0) : result.getTaxamount();

            totalBHS = ticketmoney.add(taxticketmoney.subtract(taxamount)).add(ullagemoney);
            result.setTotalBHS(totalBHS.setScale(2, BigDecimal.ROUND_HALF_DOWN));
        }
    }

    @Override
    public List<Map<String, Object>> subdividUnitsCount(AccountCondition condition) {
        String year = condition.getStartAccountno().substring(0, 4);
        condition.setYear(year);
        List<Map<String, Object>> result = accountMapper.subdividUnitsCount(condition);

        BigDecimal zero = new BigDecimal(0);
        if (result != null && result.size() > 0) {
            List<Map<String, Object>> list = new ArrayList<>();
            ElectricClassification classification = new ElectricClassification();
            List<ElectricClassification> lists = electricClassificationMapper.selectByParent(classification);
            for (ElectricClassification value : lists) {
                if (value.getChild() == true) {
                    classification.setParentId(value.getId());
                    for (ElectricClassification value1 : electricClassificationMapper.selectByParent(classification)) {
                        if (value1.getChild() == true) {
                            classification.setParentId(value1.getId());
                            for (ElectricClassification value2 : electricClassificationMapper.selectByParent(classification)) {
                                if (value2.getChild() == true) {
                                    classification.setParentId(value2.getId());
                                    for (ElectricClassification value3 : electricClassificationMapper.selectByParent(classification)) {
                                        list.add(getType(value3.getId(), value.getTypeName(), value1.getTypeName(), value2.getTypeName(), value3.getTypeName()));
                                    }
                                } else {
                                    list.add(getType(value2.getId(), value.getTypeName(), value1.getTypeName(), value2.getTypeName(), null));
                                }
                            }
                        } else {
                            list.add(getType(value1.getId(), value.getTypeName(), value1.getTypeName(), null, null));
                        }
                    }
                } else {
                    list.add(getType(value.getId(), value.getTypeName(), null, null, null));
                }
            }
            for (Map<String, Object> rMap : result) {
                BigDecimal totalusedreadings = (BigDecimal) rMap.get("totalusedreadings");
                BigDecimal totalusedreadingsY = (BigDecimal) rMap.get("totalusedreadings");
                BigDecimal accountmoney = (BigDecimal) rMap.get("totalusedreadings");
                BigDecimal accountmoneyY = (BigDecimal) rMap.get("totalusedreadings");
                BigDecimal area = (BigDecimal) rMap.get("area");
                Object electrotype = rMap.get("electrotype");
                BigDecimal compareelectricity = null;
                BigDecimal comparemoney = null;
                BigDecimal avgPrice = null;
                BigDecimal areaelectricity = null;
                if (electrotype != null) {
                    String electrot = rMap.get("electrotype").toString();
                    for (Map<String, Object> m : list) {
                        String e = m.get("electrotype").toString();
                        if (electrot.equals(e)) {
                            rMap.put("type1", m.get("type1"));
                            rMap.put("type2", m.get("type2"));
                            rMap.put("type3", m.get("type3"));
                            rMap.put("type4", m.get("type4"));
                        }
                    }

                    if (area != null && totalusedreadings != null && area.compareTo(zero) > 0) {
                        if ("2".equals(electrot) || "31".equals(electrot)) {
                            areaelectricity = totalusedreadings.divide(area);
                        }
                    }
                }

                if (totalusedreadings != null && totalusedreadingsY != null && totalusedreadingsY.compareTo(zero) > 0) {
                    compareelectricity = totalusedreadings.subtract(totalusedreadingsY).divide(totalusedreadingsY);
                }

                if (accountmoney != null && accountmoneyY != null && accountmoneyY.compareTo(zero) > 0) {
                    comparemoney = accountmoney.subtract(accountmoneyY).divide(accountmoneyY);
                }

                if (accountmoneyY != null && totalusedreadingsY != null && totalusedreadingsY.compareTo(zero) > 0) {
                    avgPrice = accountmoneyY.divide(totalusedreadingsY);
                }
                rMap.put("compareelectricity", compareelectricity == null ? null : compareelectricity.setScale(2, BigDecimal.ROUND_HALF_DOWN));
                rMap.put("comparemoney", comparemoney == null ? null : comparemoney.setScale(2, BigDecimal.ROUND_HALF_DOWN));
                rMap.put("avgPrice", avgPrice == null ? null : avgPrice.setScale(2, BigDecimal.ROUND_HALF_DOWN));
                rMap.put("areaelectricity", areaelectricity == null ? null : areaelectricity.setScale(2, BigDecimal.ROUND_HALF_DOWN));
            }
        }

        return result;
    }

    @Override
    public XSSFWorkbook exportHX(List<Map<String, Object>> list) {
        // 声明String数组，并初始化元素（表头名称）
        //第一行表头字段，合并单元格时字段跨几列就将该字段重复几次
        List<Map<String, Object>> headerList = new ArrayList<>();
        String[] excelHeader0 = new String[]{"分类", "分类", "分类", "分类", "分类", "统计指标", "统计指标", "统计指标", "统计指标", "统计指标", "统计指标", "管控指标", "管控指标", "管控指标", "管控指标", "管控指标"};
        String[] excelHeader1 = new String[]{"一级分类", "二级分类", "三级分类", "四级分类", "物理点名称", "电量", "电量", "电量", "电费", "电费", "电费", "平均电价", "平均PUE", "电费占收比", "办公营业单位面积电量", "呼叫中心每坐席电量"};
        String[] excelHeader2 = new String[]{"", "", "", "", "", "本年承包数", "本年发生数", "与承包数比较增减(%)", "本年承包数", "本年发生数", "与承包数比较增减(%)", "", "", "", "", ""};
        //对应excel中的行和列，（"开始行,结束行,开始列,结束列"）
        String[] headNum0 = new String[]{"0,0,0,4", "0,0,5,10,", "0,0,11,15"};
        String[] headNum1 = new String[]{"1,2,0,0", "1,2,1,1,", "1,2,2,2", "1,2,3,3", "1,2,4,4", "1,1,5,7", "1,1,8,10", "1,2,11,11", "1,2,12,12", "1,2,13,13", "1,2,14,14", "1,2,15,15"};
        Map<String, Object> map0 = new HashMap<>();
        Map<String, Object> map1 = new HashMap<>();
        Map<String, Object> map2 = new HashMap<>();
        map0.put("excelHeader", excelHeader0);
        map0.put("headNum", headNum0);
        map1.put("excelHeader", excelHeader1);
        map1.put("headNum", headNum1);
        map2.put("excelHeader", excelHeader2);
        headerList.add(map0);
        headerList.add(map1);
        headerList.add(map2);
        // 声明一个工作簿
        XSSFWorkbook wb = new XSSFWorkbook();
        XSSFSheet sheet = wb.createSheet("TAQIDataReport");

        wb = this.setHearder(headerList, sheet, wb);
        wb = this.setBody(list, sheet, wb, 3);
        return wb;
    }

    private XSSFWorkbook setHearder(List<Map<String, Object>> headerList, XSSFSheet sheet, XSSFWorkbook wb) {

        // 生成一种样式
        XSSFCellStyle style = wb.createCellStyle();
        // 设置样式
        style.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        style.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        style.setBorderRight(HSSFCellStyle.BORDER_THIN);
        style.setBorderTop(HSSFCellStyle.BORDER_THIN);
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);

        // 生成一种字体
        XSSFFont font = wb.createFont();
        // 设置字体
        font.setFontName("微软雅黑");
        // 设置字体大小
        font.setFontHeightInPoints((short) 12);
        // 字体加粗
        font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
        // 在样式中引用这种字体
        style.setFont(font);

        int count = 0;
        for (Map<String, Object> map : headerList) {
            XSSFRow row = sheet.createRow(count);
            String[] excelHeader = (String[]) map.get("excelHeader");
            for (int i = 0; i < excelHeader.length; i++) {
                // 根据字段长度自动调整列的宽度
                sheet.autoSizeColumn(i, true);
                XSSFCell cell = row.createCell(i);
                cell.setCellValue(excelHeader[i]);
                cell.setCellStyle(style);
            }
            // 动态合并单元格
            if (map.containsKey("headNum")) {
                String[] headNum = (String[]) map.get("headNum");
                for (int i = 0; i < headNum.length; i++) {
                    if (headNum[i] != null) {
                        sheet.autoSizeColumn(i, true);
                        String[] temp = headNum[i].split(",");
                        Integer startrow = Integer.parseInt(temp[0]);
                        Integer overrow = Integer.parseInt(temp[1]);
                        Integer startcol = Integer.parseInt(temp[2]);
                        Integer overcol = Integer.parseInt(temp[3]);
                        sheet.addMergedRegion(new CellRangeAddress(startrow, overrow, startcol, overcol));
                    }
                }
            }
            count++;
        }
        return wb;
    }

    private XSSFWorkbook setBody(List<Map<String, Object>> array, XSSFSheet sheet, XSSFWorkbook wb, int rowNum) {
// 生成并设置另一个样式
        XSSFCellStyle style2 = wb.createCellStyle();
        style2.setBorderBottom(HSSFCellStyle.BORDER_THIN);
        style2.setBorderLeft(HSSFCellStyle.BORDER_THIN);
        style2.setBorderRight(HSSFCellStyle.BORDER_THIN);
        style2.setBorderTop(HSSFCellStyle.BORDER_THIN);
        style2.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        style2.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);

        // 生成另一种字体2
        XSSFFont font2 = wb.createFont();
        // 设置字体
        font2.setFontName("微软雅黑");
        // 设置字体大小
        font2.setFontHeightInPoints((short) 12);
        // 字体加粗
        style2.setFont(font2);

        XSSFRow row = sheet.createRow(rowNum);

        int type1End = rowNum;
        int type1Start = rowNum;
        int type2End = rowNum;
        int type2Start = rowNum;
        int type3End = rowNum;
        int type3Start = rowNum;
        List<String> keyList = getKeyList();
        //合并行
        // 第四行数据开始
        for (int i = 0; i < array.size(); i++) {
            Map tempMap = (Map) array.get(i);
            Map tempMap1 = (Map) array.get(0 == i ? 0 : i - 1);
            row = sheet.createRow(i + rowNum);
            //TAQIDataReport report = list.get(i);
            int j = 0;
            for (String key : keyList) {
                XSSFCell cell = row.createCell(j);
//				if("test1".equals(key) || "test2".equals(key) || "test3".equals(key)){
//					cell.setCellValue("");
//				}else if("type1".equals(key) || "type2".equals(key) || "type3".equals(key) || "type4".equals(key)){
                cell.setCellValue(null == tempMap.get(key) ? null : this.rvZeroAndDot(tempMap.get(key).toString()));
//				}else{
//					cell.setCellValue(null == tempMap.get(key)?"0":this.rvZeroAndDot(tempMap.get(key).toString()));
//				}
                cell.setCellStyle(style2);
                j++;
            }
            sheet.autoSizeColumn(i, true);
            //从第二行数据开始，判断分类是否合并
            if (i > 0) {
                //分公司合并
                if (tempMap.get("type1").equals(tempMap1.get("type1"))) {
                    type1End++;//合并结束列数加1
                } else if (type1Start != type1End) {
                    sheet.addMergedRegion(new CellRangeAddress(type1Start, type1End, 0, 0));
                    type1Start = type1End + 1;//开始为上次结束列数+1
                    type1End++;
                } else {
                    type1Start++;
                    type1End++;
                }
//				//分公司合并
                if (null != tempMap.get("type2") && tempMap.get("type2").equals(tempMap1.get("type2"))) {
                    type2End++;//合并结束列数加1
                } else if (type2Start != type2End) {
                    sheet.addMergedRegion(new CellRangeAddress(type2Start, type2End, 1, 1));
                    type2Start = type2End + 1;//开始为上次结束列数+1
                    type2End++;
                }

                if (null != tempMap.get("type3") && tempMap.get("type3").equals(tempMap1.get("type3"))) {
                    type3End++;//合并结束列数加1
                } else if (type3Start != type3End) {
                    sheet.addMergedRegion(new CellRangeAddress(type3Start, type3End, 2, 2));
                    type3Start = type3End + 1;//开始为上次结束列数+1
                    type3End++;
                } else {
                    type3Start++;
                    type3End++;
                }
            }
        }
        return wb;
    }

    private List<String> getKeyList() {

        List<String> keyList = new ArrayList<>();
        keyList.add("type1");
        keyList.add("type2");
        keyList.add("type3");
        keyList.add("type4");
        keyList.add("stationname");
        keyList.add("totalusedreadingsY");
        keyList.add("totalusedreadings");
        keyList.add("compareelectricity");
        keyList.add("accountmoneyY");
        keyList.add("accountmoney");
        keyList.add("comparemoney");
        keyList.add("avgPrice");
        keyList.add("test1");
        keyList.add("test2");
        keyList.add("areaelectricity");
        keyList.add("test3");
        return keyList;
    }

    public String rvZeroAndDot(String s) {
        if (s.isEmpty()) {
            return null;
        }
        if (s.indexOf(".") > 0) {
            //去掉多余的0
            s = s.replaceAll("0+?$", "");
            //如最后一位是.则去掉
            s = s.replaceAll("[.]$", "");
        }
        return s;
    }

    private Map<String, Object> getType(Long id, String type1, String type2, String type3, String type4) {
        Map<String, Object> map = new HashMap<>();
        map.put("type1", type1);
        map.put("type2", type2);
        map.put("type3", type3);
        map.put("type4", type4);
        map.put("electrotype", id.toString());
        return map;
    }

    /**
     * @Description: 通过查询获得定额，
     * @author: dongk
     * @date: 2019/5/5
     * @param:
     * @return:
     */
    private BigDecimal selectQuota(Long ammeterid, Integer mm) {
        BigDecimal quota;
        Map<String, Object> map = new HashMap<>();
        map.put("manth", returnMonthColumn(mm));
        map.put("ammeterid", ammeterid);
        quota = quotaMapper.selectByMonth(map);
        if (quota == null) {
            quota = new BigDecimal(0);
        }
        return quota;
    }

    /**
     * @Description: 获取数据库中月份对应的列名
     * @author: dongk
     * @date: 2019/5/29
     * @param:
     * @return:
     */
    private String returnMonthColumn(Integer mm) {
        String month;

        switch (mm) {
            case 1:
                month = "jan_quota_value";
                break;
            case 2:
                month = "feb_quota_value";
                break;
            case 3:
                month = "mar_quota_value";
                break;
            case 4:
                month = "apr_quota_value";
                break;
            case 5:
                month = "may_quota_value";
                break;
            case 6:
                month = "jun_quota_value";
                break;
            case 7:
                month = "jul_quota_value";
                break;
            case 8:
                month = "aug_quota_value";
                break;
            case 9:
                month = "sep_quota_value";
                break;
            case 10:
                month = "oct_quota_value";
                break;
            case 11:
                month = "nov_quota_value";
                break;
            case 12:
                month = "dec_quota_value";
                break;
            default:
                month = "";
        }

        return month;
    }

    /**
     * 电量/电费同比/环比分析报表
     *
     * @param params
     * @return
     */
    @Override
    public List<Map<String, Object>> statisticalAnalysis(Map<String, Object> params) {
        return accountMapper.statisticalAnalysis(params);
    }

    @Override
    public Map<String, Object> valOldAcount(Account account) {
        return accountMapper.valOldAcount(account);
    }

    @Override
    public List<Map<String, Object>> selectAmmeterByCompany(AccountCondition accountEsResult) {
        Map<String, Object> params = new HashMap<>();
        params.put("accountno", accountEsResult.getAccountno());
        params.put("company", accountEsResult.getCompany());
        params.put("country", accountEsResult.getCountry());
        params.put("countrys", accountEsResult.getCountrys());
        params.put("projectname", accountEsResult.getProjectname());
        //params.put("ammeterName",accountEsResult.getamm());
        params.put("accountType", accountEsResult.getAccountType());

        params.put("supplybureauammetercode", accountEsResult.getSupplybureauammetercode());
        params.put("isentityammeter", 1);
/*        if ("ln".equals(deployTo)){
            params.put("isentityammeter",0);
            return accountMapper.selectAmmeterByCompanyln(params);
        }*/
        return accountMapper.selectAmmeterByCompany(params);
    }

    @Override
    public List<Map<String, Object>> selectAmmeterLstByCompany(AccountCondition accountEsResult) {
        Map<String, Object> params = new HashMap<>();
        List<Map<String, Object>> list1 = new ArrayList<Map<String, Object>>();
        Account account = new Account();
        Map<String, Object> map = new ConcurrentHashMap<>();

        params.put("accountno", accountEsResult.getAccountno());
        params.put("company", accountEsResult.getCompany());
        params.put("country", accountEsResult.getCountry());
        params.put("countrys", accountEsResult.getCountrys());
        params.put("projectname", accountEsResult.getProjectname());
        //params.put("ammeterName",accountEsResult.getamm());
        params.put("accountType", accountEsResult.getAccountType());

        params.put("supplybureauammetercode", accountEsResult.getSupplybureauammetercode());
        params.put("isentityammeter", 1);
/*        if ("ln".equals(deployTo)){
            params.put("isentityammeter",0);
            return accountMapper.selectAmmeterByCompanyln(params);
        }*/
        List<Map<String, Object>> list = accountMapper.selectAmmeterByCompany(params);
        Iterator<Map<String, Object>> it = list.iterator();
        while (it.hasNext()) {
            Map<String, Object> map1 = new ConcurrentHashMap<>();
            HashMap<String, Object> m = (HashMap<String, Object>) it.next();
            String nu = "";
            for (String k : m.keySet()) {
                if (k.equals("ammeterid")) {
                    map1.put(k, (Long) m.get(k));
                    account.setAmmeterid((Long) m.get(k));

                    map = accountMapper.selectLastacc(account);
                    if (map != null) {
                        map1.put("curtotalreadings", new BigDecimal(map.get("curtotalreadings").toString()));
                        map1.put("enddate", map.get("enddate").toString());
                        map1.put("pcid", map.get("pcid").toString());

                    }
                    System.out.println(k + " : " + m.get(k));
                } else {
                    if (m.get(k) != null)
                        map1.put(k, m.get(k));

                    else
                        map1.put(k, nu);
                }
            }
            list1.add(map1);
            map1 = null;
        }
        return list1;//accountMapper.selectAmmeterLstByCompany(params);
    }

    @Override
    public List<AccountBaseResult> getids(List<Long> ids) {
        return accountMapper.getids(ids);
    }

    @Override
    public List<AccountBaseResult> getEsIds(List<Long> accountIds) {
        return accountMapper.getEsids(accountIds);
    }

    @Override
    @org.springframework.transaction.annotation.Transactional(rollbackFor = Exception.class)
    public Map<String, Object> importExcelData(MultipartFile file) throws Exception {
        Map<String, Object> map = new HashMap<>();
        Attachments attachments = uploadExcelFile(file);
        if (ObjectUtil.isNotEmpty(attachments)) {

            InputStream fis = file.getInputStream();
            Workbook workbook = new XSSFWorkbook(fis);
            Sheet sheet = workbook.getSheetAt(0); // 获取第一个工作表
            AccountExcelInfo accountExcelInfo = new AccountExcelInfo();
            String companyName = getCellValue(sheet.getRow(7), 12, false, false);
            if (StrUtil.isBlank(companyName)) {
                companyName = getCellValue(sheet.getRow(7), 13, false, false);
            }
            accountExcelInfo.setCompanyName(companyName);
            accountExcelInfo.setStartDate(sheet.getRow(7).getCell(1).getStringCellValue());
            accountExcelInfo.setEndDate(sheet.getRow(9).getCell(1).getStringCellValue());
            accountExcelInfo.setAccountCount((int) sheet.getRow(9).getCell(26).getNumericCellValue());
            accountExcelInfo.setCurrentConsumption(sheet.getRow(12).getCell(5).getStringCellValue().replace("千瓦时", ""));
            accountExcelInfo.setCurrentExpense(sheet.getRow(12).getCell(14).getStringCellValue().replace("元", ""));
            String meterDate = getCellValue(sheet.getRow(12), 29, false, false);
            if (StrUtil.isBlank(meterDate)) {
                meterDate = getCellValue(sheet.getRow(12), 30, false, false);
            }
            accountExcelInfo.setMeterDate(meterDate);
            accountExcelInfo.setWriteOffNumber(sheet.getRow(6).getCell(26).getStringCellValue());
            accountExcelInfo.setWriteOffName(sheet.getRow(7).getCell(26).getStringCellValue());
            String address = getCellValue(sheet.getRow(9), 12, false, false);
            if (StrUtil.isBlank(address)) {
                address = getCellValue(sheet.getRow(9), 13, false, false);
            }
            accountExcelInfo.setAddress(address);
            String printDate = getCellValue(sheet.getRow(11), 22, false, false);
            if (StrUtil.isBlank(printDate)) {
                printDate = getCellValue(sheet.getRow(11), 23, false, false);
            }
            if (StrUtil.isNotBlank(printDate)) {
                accountExcelInfo.setPrintDate(printDate.substring(printDate.indexOf("：") + 1));
            }
            accountExcelInfo.setCurrentConsumptionDesc(sheet.getRow(18).getCell(25).getStringCellValue());
            accountExcelInfo.setAccountCountDesc(sheet.getRow(22).getCell(25).getStringCellValue());
            String avgPriceDesc = getCellValue(sheet.getRow(26), 25, false, false);
            if (!avgPriceDesc.contains("本期您的平均电价")) {
                avgPriceDesc = getCellValue(sheet.getRow(27), 25, false, false);
            }
            accountExcelInfo.setAvgPriceDesc(avgPriceDesc);

            accountExcelInfo.setCreateTime(new Date());
            accountExcelInfo.setAttachmentId(attachments.getId());
            User user = ShiroUtils.getUser();
            if (ObjectUtil.isNotEmpty(user)) {
                accountExcelInfo.setCreateBy(user.getId());
                //部门组
                List<IdNameVO> departments = user.getDepartments();
                //分公司组
                List<IdNameVO> companies = user.getCompanies();
                Long companyId = companies != null && companies.size() > 0 ? Long.parseLong(companies.get(0).getId()) : null;

                Long countryId = departments != null && departments.size() > 0 ? Long.parseLong(departments.get(0).getId()) : null;
                accountExcelInfo.setCompany(companyId);
                accountExcelInfo.setCountry(countryId);
            }
            AccountExcelInfo model = new AccountExcelInfo();
            model.setAddress(accountExcelInfo.getAddress());
            model.setStartDate(accountExcelInfo.getStartDate());
            model.setEndDate(accountExcelInfo.getEndDate());
            model.setCompanyName(accountExcelInfo.getCompanyName());
            model.setWriteOffName(accountExcelInfo.getWriteOffName());
            model.setWriteOffNumber(accountExcelInfo.getWriteOffNumber());
            int iCount = accountExcelInfoMapper.count(model);
            if (iCount > 0) {
                map.put("code", "201");
                return map;
            }
            accountExcelInfoMapper.insert(accountExcelInfo);
            boolean isConsumptionDetail = false;
            boolean isExpenseDetail = false;
            List<AccountExcelDetailPower> accountExcelDetailPowerList = new ArrayList<>();
            List<AccountExcelDetailExpense> accountExcelDetailExpenseList = new ArrayList<>();
            int sequence = 0;
            String supplybureauammetercode = "";
            String meterName = "";
            String meterId = "";
            int accountMoneyCol = 12;
            int powerMoneyCol = 15;
            int capacityMoneyCol = 18;
            int ruralMoneyCol = 19;
            int magnificationCol = 12;
            int transformerullageCol = 15;
            int lineLossCol = 16;
            for (Row rowData : sheet) {
                if (rowData.getCell(1).getCellType() == XSSFCell.CELL_TYPE_STRING && "序号".equals(rowData.getCell(1).getStringCellValue())) {
                    if (isConsumptionDetail && !isExpenseDetail) {
                        isExpenseDetail = true;
                        accountMoneyCol = getColNum(rowData, "电费", 12);
                        powerMoneyCol = getColNum(rowData, "其中功率因素", 15);
                        capacityMoneyCol = getColNum(rowData, "其中输配容", 18);
                        ruralMoneyCol = getColNum(rowData, "农网维护费用", 19);
                    } else if (!isConsumptionDetail) {
                        isConsumptionDetail = true;
                        magnificationCol = getColNum(rowData, "倍率", 12);
                        lineLossCol = getColNum(rowData, "线损", 15);
                        transformerullageCol = getColNum(rowData, "变损", 16);
                    }
                    continue;
                }
                if (isExpenseDetail) {
                    if (rowData.getLastCellNum() > 12 && rowData.getCell(1).getCellType() == XSSFCell.CELL_TYPE_NUMERIC) {
                        AccountExcelDetailExpense accountExcelDetailExpense = new AccountExcelDetailExpense();
                        accountExcelDetailExpense.setId(IdWorker.getId());
                        accountExcelDetailExpense.setInfoId(accountExcelInfo.getId());
                        accountExcelDetailExpense.setSequence(Integer.parseInt(getCellValue(rowData, 1, true, false)));
                        accountExcelDetailExpense.setSupplybureauammetercode(rowData.getCell(2).getStringCellValue());
                        accountExcelDetailExpense.setMeterName(rowData.getCell(4).getStringCellValue());
                        accountExcelDetailExpense.setUnit(rowData.getCell(6).getStringCellValue());
                        accountExcelDetailExpense.setAddress(rowData.getCell(8).getStringCellValue());
                        accountExcelDetailExpense.setAccountmoney(getCellValue(rowData, accountMoneyCol, false, true));
                        accountExcelDetailExpense.setPowermoney(getCellValue(rowData, powerMoneyCol, false, true));
                        accountExcelDetailExpense.setCapacitymoney(getCellValue(rowData, capacityMoneyCol, false, true));
                        accountExcelDetailExpense.setRuralmoney(getCellValue(rowData, ruralMoneyCol, false, true));
                        if (rowData.getLastCellNum() > 20) {
                            accountExcelDetailExpense.setMemo(getCellValue(rowData, 20, false, false));
                        }
                        accountExcelDetailExpense.setHandled("0");
                        accountExcelDetailExpenseList.add(accountExcelDetailExpense);
                    }
                } else if (isConsumptionDetail) {
                    if (StrUtil.isNotBlank(getCellValue(rowData, 1, true, false))) {
                        sequence = (int) rowData.getCell(1).getNumericCellValue();
                        supplybureauammetercode = rowData.getCell(2).getStringCellValue();
                        meterName = rowData.getCell(4).getStringCellValue();
                        meterId = rowData.getCell(6).getStringCellValue();
                    }
                    if (rowData.getLastCellNum() > 19 && StrUtil.isNotBlank(getCellValue(rowData, 19, false, false))) {
                        AccountExcelDetailPower accountExcelDetailPower = new AccountExcelDetailPower();
                        accountExcelDetailPower.setId(IdWorker.getId());
                        accountExcelDetailPower.setInfoId(accountExcelInfo.getId());
                        accountExcelDetailPower.setSequence(sequence);
                        accountExcelDetailPower.setSupplybureauammetercode(supplybureauammetercode);
                        accountExcelDetailPower.setMeterName(meterName);
                        if (StrUtil.isNotBlank(getCellValue(rowData, 8, false, false))) {
                            accountExcelDetailPower.setMeterId(meterId);
                        }
                        accountExcelDetailPower.setPrevtotalreadings(getCellValue(rowData, 8, false, true));
                        accountExcelDetailPower.setCurtotalreadings(getCellValue(rowData, 11, false, true));
                        accountExcelDetailPower.setMagnification(getCellValue(rowData, magnificationCol, false, true));
                        accountExcelDetailPower.setLineLoss(getCellValue(rowData, lineLossCol, false, true));
                        accountExcelDetailPower.setTransformerullage(getCellValue(rowData, transformerullageCol, false, true));
                        accountExcelDetailPower.setReimbursement(getCellValue(rowData, 18, false, true));
                        accountExcelDetailPower.setTotalusedreadings(getCellValue(rowData, 19, false, true));
                        if (rowData.getLastCellNum() > 20) {
                            accountExcelDetailPower.setMemo(getCellValue(rowData, 20, false, false));
                        }
                        accountExcelDetailPower.setHandled("0");
                        accountExcelDetailPowerList.add(accountExcelDetailPower);
                    }
                }
            }
            accountExcelInfo.setAttachments(attachments);
            map.put("accountExcelInfo", accountExcelInfo);
            if (CollectionUtils.isNotEmpty(accountExcelDetailPowerList)) {
                checkAccountExcelDetailPowerList(accountExcelDetailPowerList, accountExcelInfo.getStartDate().replaceAll("-", ""));
                accountExcelDetailPowerMapper.insertList(accountExcelDetailPowerList);
                map.put("accountExcelDetailPowerList", accountExcelDetailPowerList);
            }
            if (CollectionUtils.isNotEmpty(accountExcelDetailExpenseList)) {
                accountExcelDetailExpenseMapper.insertList(accountExcelDetailExpenseList);
                map.put("accountExcelDetailExpenseList", accountExcelDetailExpenseList);
            }
        }
        return map;
    }

    private int getColNum(Row rowData, String colTitle, int defaultColNum) {
        for (int i = rowData.getFirstCellNum(); i <= rowData.getLastCellNum(); i++) {
            if (rowData.getCell(i).getStringCellValue().contains(colTitle)) {
                return i;
            }
        }
        return defaultColNum;
    }

    @Override
    public Map<String, Object> viewAccountExcel(Long id){
        Map<String, Object> map = new HashMap<>();
        if (ObjectUtil.isNotEmpty(id)) {
            Map<String, Object> paramMap = new HashMap<String, Object>();
            paramMap.put("id", id);
            AccountExcelInfo accountExcelInfo = accountExcelInfoMapper.selectByPrimaryKey(paramMap);
            if (ObjectUtil.isNotEmpty(accountExcelInfo)) {
                accountExcelInfo.setAttachments(attachmentsService.get(accountExcelInfo.getAttachmentId()));
                AccountExcelDetailPower paramPower = new AccountExcelDetailPower();
                paramPower.setInfoId(id);
                List<AccountExcelDetailPower> accountExcelDetailPowerList = accountExcelDetailPowerMapper.selectList(paramPower);
                AccountExcelDetailExpense paramExpense = new AccountExcelDetailExpense();
                paramExpense.setInfoId(id);
                List<AccountExcelDetailExpense> accountExcelDetailExpenseList = accountExcelDetailExpenseMapper.selectList(paramExpense);
                map.put("accountExcelInfo", accountExcelInfo);
                if (CollectionUtils.isNotEmpty(accountExcelDetailPowerList)) {
                    map.put("accountExcelDetailPowerList", accountExcelDetailPowerList);
                }
                if (CollectionUtils.isNotEmpty(accountExcelDetailExpenseList)) {
                    map.put("accountExcelDetailExpenseList", accountExcelDetailExpenseList);
                }
            }
        }
        return map;
    }

    @Override
    public Map<String, Object> viewAccountExcelNext(Long id){
        Map<String, Object> map = new HashMap<>();
        if (ObjectUtil.isNotEmpty(id)) {
            Map<String, Object> paramMap = new HashMap<String, Object>();
            paramMap.put("id", id);
            AccountExcelInfo accountExcelInfo = accountExcelInfoMapper.selectByPrimaryKey(paramMap);
            if (ObjectUtil.isNotEmpty(accountExcelInfo)) {
                String accountno = accountExcelInfo.getMeterDate().replaceAll("-", "").substring(0, 6);
                accountExcelInfo.setAttachments(attachmentsService.get(accountExcelInfo.getAttachmentId()));
                AccountExcelDetailPower paramPower = new AccountExcelDetailPower();
                paramPower.setInfoId(id);
                List<HashMap<String, Object>> accountExcelDetailPowerList = accountExcelDetailPowerMapper.selectListByInfo(id,
                        accountExcelInfo.getStartDate().replaceAll("-", ""),
                        accountExcelInfo.getEndDate().replaceAll("-", ""), accountno);
                map.put("accountExcelInfo", accountExcelInfo);
                if (CollectionUtils.isNotEmpty(accountExcelDetailPowerList)) {
                    map.put("accountExcelDetailPowerList", accountExcelDetailPowerList);
                }
            }
        }
        return map;
    }

    private void checkAccountExcelDetailPowerList(List<AccountExcelDetailPower> accountExcelDetailPowerList, String startDate){
        for (AccountExcelDetailPower accountExcelDetailPower : accountExcelDetailPowerList) {
            Ammeterorprotocol ammeterorprotocol = accountExcelDetailPowerMapper.selectAmmeterorprotocolByInfo(accountExcelDetailPower.getSupplybureauammetercode(),
                    accountExcelDetailPower.getMeterId());
            if (ObjectUtil.isNotEmpty(ammeterorprotocol)) {
                accountExcelDetailPower.setAmmeterId(ammeterorprotocol.getId());
                String abnormalCause = checkAccountExcel(ammeterorprotocol, accountExcelDetailPower.getPrevtotalreadings(), startDate);
                accountExcelDetailPower.setAbnormalCause(abnormalCause);
            } else {
                accountExcelDetailPower.setAbnormalCause("未找到关联的电表。");
            }
        }
    }

    private String checkAccountExcel(Ammeterorprotocol ammeterorprotocol, String prevtotalreadings, String startDate) {
        String result = null;
        Long oldAmmeterId = null;
        if (ObjectUtil.isNotEmpty(ammeterorprotocol.getIschangeammeter()) && ammeterorprotocol.getIschangeammeter() == 1) {
            // 若是进行了换表操作 , 获取到旧电表的id
            oldAmmeterId = ammeterorprotocol.getOldAmmeterId();
        }
        Account account = accountMapper.selectLastAccountByAmmeter(ammeterorprotocol.getId(), oldAmmeterId);
        if (ObjectUtil.isNotEmpty(account)) {
            /** 上期截止时间*/
            String preEndDate = account.getEnddate();
            /** 上期止度*/
            BigDecimal curtotalreadings = account.getCurtotalreadings();

            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            LocalDate endTime = LocalDate.parse(preEndDate, formatter);
            LocalDate startTime = LocalDate.parse(startDate, formatter);
            long between = ChronoUnit.DAYS.between(endTime, startTime);
            //上期截止日期第二天是本期起始时间才算连续
            if (startTime.isBefore(endTime) || between != 1) {
                result = "台账周期不连续，上次台账（pcid：" + account.getPcid() + "）截止日期为" + preEndDate + "。";
            }
            if (curtotalreadings.compareTo(new BigDecimal(prevtotalreadings)) != 0) {
                result += "电表读数不连续，上次台账（pcid：" + account.getPcid() + "）电表截止读数为" + curtotalreadings + "。";
            }
        }
        return result;
    }

    @Override
    @org.springframework.transaction.annotation.Transactional(rollbackFor = Exception.class)
    public Map<String, Object> deleteAccountExcelByIds(String[] ids) {
        int num = 0;
        boolean b = true;
        List<Long> idsList = new ArrayList<>();
        for (int i = 0; i < ids.length; i++) {
            idsList.add(Long.parseLong(ids[i]));
        }
        Map<String, Object> map = new HashMap<>();
        String str = "";
        List<AccountExcelInfo> resultList = accountExcelInfoMapper.selectByIds(idsList);
        if (CollectionUtils.isNotEmpty(resultList)) {
            for (AccountExcelInfo result : resultList) {
                AccountExcelDetailPower model = new AccountExcelDetailPower();
                model.setInfoId(result.getId());
                model.setHandled("1");
                int count = accountExcelDetailPowerMapper.count(model);
                if (count > 0) {
                    str += "文件名为【" + result.getFileName() + "】的账单已经保存为台账，不能删除";
                } else {
                    deleteAccountExcelById(result.getId());
                    num += 1;
                }
            }
        }
        map.put("num", num);
        map.put("str", str);
        return map;
    }

    @org.springframework.transaction.annotation.Transactional(rollbackFor = Exception.class)
    @Override
    public Map<String, Object> addExcelAccount(List<Account> accountList) {
        int num = 0;
        BigDecimal z = new BigDecimal(0);
        Map<String, Object> crossmap = new HashMap<>();
        String str = "";
        User user = ShiroUtils.getUser();
        List<Long> pcidList = new ArrayList<>();
        for (Account account : accountList) {

            if (user != null) {
                //设置最后修改人
                account.setLastediterid(user.getId());
                account.setLasteditdate(new Date());
                if (account.getAccountmoney() != null && account.getAccountmoney().compareTo(new BigDecimal(0)) != 0) {
                    account.setEffective(new BigDecimal(1));
                }
            }
            //account.setIsnew(new BigDecimal(0));
            Long pcid = accountMapper.getExcelAccountId(account.getAccountno(), account.getAmmeterid());
            boolean b = true;
            if (ObjectUtil.isNotEmpty(pcid)) {
                BigDecimal money = this.selectCompletedMoney(account.getPcid());
                if (money != null && money.compareTo(z) > 0) {
                    if (account.getAccountmoney().compareTo(money) < 0) {
                        str += "电表/协议编号为【" + account.getAmmetercode() + "】的台账已经报账完成：" + money + ",台账实缴费用不能低于已经报账金额";
                        b = false;
                    }
                }
            }
/*            if (b) {
                List<Map<String, Object>> licrossmap= (List<Map<String, Object>>) accountMapper.checkCrossdateaccount(account);
                if (licrossmap.size()>0) {
                    b = false;
                    str = "可能存在日期交叉风险，请核实台账查询数据";
                    for (Map<String, Object> stringObjectMap : licrossmap) {
                        stringObjectMap.forEach((key, value) -> {
                            System.out.println(key);
                            System.out.println(value);

                        });
                    }l

                }

            }*/
            if (b) {
                account.setBz("国电excel账单导入");
                account.setTaxticketmoney(account.getAccountMoney());
                account.setInputtaxticketmoney(account.getAccountMoney());
                account.setCurusedreadings(account.getCurtotalreadings().subtract(account.getPrevtotalreadings()));
                if (ObjectUtil.isNotEmpty(pcid)) {
                    account.setPcid(pcid);
                    num += accountMapper.updateOwnById(account);
                } else {
                    account.setPcid(IdWorker.getId());
                    num += accountMapper.insert(account);
                }
                pcidList.add(account.getPcid());

                if (ObjectUtil.isNotEmpty(account.getExcelPowerId())) {
                    AccountExcelDetailPower model = new AccountExcelDetailPower();
                    model.setId(account.getExcelPowerId());
                    model.setHandled("1");
                    model.setPcid(account.getPcid());
                    accountExcelDetailPowerMapper.updateForModel(model);
                }
                accountMapper.deleteByAmmeterid(account.getAmmeterid());
                saveBGJJ(account);
                // 保存之后，redis的暂存数据应删除
                RedisUtil.del(user.getLoginId()+"Account");
            }
        }
        //重新稽核台账
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                StationAuditUtil.doAuditAccountThreadByPcids(pcidList);
            }
        });
        Map<String, Object> map = new HashMap<>();
        map.put("num", num);
        map.put("str", str);
        return map;
    }

    private void deleteAccountExcelById(Long id) {
        accountExcelDetailPowerMapper.deleteByInfoId(id);
        accountExcelDetailExpenseMapper.deleteByInfoId(id);
        Map<String, Object> deleteMap = new HashMap<>();
        deleteMap.put("id", id);
        accountExcelInfoMapper.deleteByPrimaryKeyDB(deleteMap);
    }

    private String getCellValue(Row row, int cellNum, boolean isInt, boolean isNumeric) {
        String cellValue = "";
        switch (row.getCell(cellNum).getCellType()) {
            case XSSFCell.CELL_TYPE_STRING:
                cellValue = row.getCell(cellNum).getStringCellValue();
                break;
            case XSSFCell.CELL_TYPE_NUMERIC:
                double numericCellValue = row.getCell(cellNum).getNumericCellValue();
                if (isInt) {
                    cellValue = String.valueOf((int) numericCellValue);
                } else {
                    cellValue = String.valueOf(numericCellValue);
                }
                break;
        }
        if (isNumeric) {
            String[] parts = cellValue.split("\\.");
            if (parts.length == 2 && parts[1].equals("0")) {
                return parts[0];
            }
        }
        return cellValue;
    }

    private Attachments uploadExcelFile(MultipartFile file) throws Exception {
        User currentUser = ShiroUtils.getUser();
        // 上传附件
        UpLoadFile upLoadFile = new UpLoadFile();
        upLoadFile.setBusiAliasCode("account_excel");
        upLoadFile.setCategoryCode("file");
        upLoadFile.setBusiAliasCode("国电账单（附件）");
        upLoadFile.setBusiId(0L); // 暂时设为0，后续可以根据业务需要调整
        upLoadFile.setCreatorId(currentUser.getId());
        upLoadFile.setCreatorName(currentUser.getUserName());
        upLoadFile.setShardKey("SCCL");
        upLoadFile.setYear(new Date().getYear() + 1900);
        upLoadFile.setFileName(file.getOriginalFilename());
        upLoadFile.setFileSize(file.getSize());
        return attachmentsService.upLoadFile(file, upLoadFile);
    }
}
