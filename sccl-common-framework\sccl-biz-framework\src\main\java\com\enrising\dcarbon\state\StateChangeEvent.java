package com.enrising.dcarbon.state;

import com.enrising.dcarbon.observer.Event;
import lombok.Getter;
import lombok.Setter;

/**
 * 状态改变事件，事件的源使用的是只读的状态对象，为了避免监听器对状态进行其他操作导致未知异常
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-09 16:44
 * @email <EMAIL>
 */
@Getter
@Setter
public class StateChangeEvent extends Event<ReadonlyState> {
    public StateChangeEvent(ReadonlyState source) {
        super(source);
    }
}
