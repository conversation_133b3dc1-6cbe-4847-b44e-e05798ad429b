package com.enrising.dcarbon.observer;

import java.util.Date;

/**
 * 父事件类
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-09 13:51
 * @email <EMAIL>
 */
public class Event<T> {
    protected T source;
    protected Date publishTime;

    public Event() {
        this.publishTime = new Date();
    }

    public Event(T source) {
        this.source = source;
        this.publishTime = new Date();
    }

    public T getSource() {
        return source;
    }

    public Date getPublishTime() {
        return publishTime;
    }
}
