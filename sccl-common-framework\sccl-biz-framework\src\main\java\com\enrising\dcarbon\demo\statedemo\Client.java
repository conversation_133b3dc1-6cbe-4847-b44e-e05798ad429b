package com.enrising.dcarbon.demo.statedemo;

import com.enrising.dcarbon.state.ObserverStateMachine;

/**
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-17 9:59
 * @email <EMAIL>
 */
public class Client {
    public static void main(String[] args) {
        ObserverStateMachine stateMachine = new ObserverStateMachine();

        DemoBaseEntity entity = new DemoBaseEntity();
        entity.setId(1L);
        entity.setStatus(0);

        stateMachine.initialize(entity);

        stateMachine.stateChange(new FinishedState(EventManagerInstance.EVENT_MANGER).setEntity(entity));
    }

}
