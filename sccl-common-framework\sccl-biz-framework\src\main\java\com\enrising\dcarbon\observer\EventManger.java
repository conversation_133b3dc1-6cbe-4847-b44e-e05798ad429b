package com.enrising.dcarbon.observer;

import com.enrising.dcarbon.collector.RouteEvent;
import com.enrising.dcarbon.thread.NamedThreadFactory;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * 事件管理器
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-09 13:55
 * @email <EMAIL>
 */
@Slf4j
public class EventManger {
    protected final Map<Class<? extends Event<?>>, List<EventListener<Event<?>>>> listenerContainer = new ConcurrentHashMap<>();
    protected final ThreadPoolExecutor eventHandleThreads;

    public EventManger(ThreadPoolExecutor eventHandleThreads) {
        this.eventHandleThreads = eventHandleThreads;
    }

    public EventManger() {
        this.eventHandleThreads = new ThreadPoolExecutor(3, 5, 60, TimeUnit.SECONDS, new LinkedBlockingDeque<>(), new NamedThreadFactory("eventHandle"));
    }

    /**
     * 添加一个监听器
     *
     * @param eventType 要检监听的事件类型，方法参数没有对类型进行限制，但如果类型不是{@link Event}及其子类则会抛出异常
     * @param listener  添加的监听器，方法参数没有对类型进行限制，但如果泛型类型不是{@link Event}及其子类则会抛出异常
     * @return com.enrising.dcarbon.observer.EventManger
     * <AUTHOR> ^ ▽ ^ *)
     * @date 2023/5/9 16:53
     */
    @SuppressWarnings("unchecked")
    public EventManger addListener(Class<?> eventType, EventListener<?> listener) {
        if (isListenerExist(eventType, listener.name())) {
            throw new IllegalArgumentException("监听器：" + listener.name() + "已存在");
        }
        if (!listenerContainer.containsKey(eventType)) {
            listenerContainer.putIfAbsent((Class<? extends Event<?>>) eventType, new LinkedList<>());
        }
        listenerContainer
                .get(eventType)
                .add((EventListener<Event<?>>) listener);
        return this;
    }

    @SuppressWarnings("unchecked")
    public EventManger addListenerIfAbsent(Class<?> eventType, EventListener<?> listener) {
        if (isListenerExist(eventType, listener.name())) {
            return this;
        }
        if (!listenerContainer.containsKey(eventType)) {
            listenerContainer.putIfAbsent((Class<? extends Event<?>>) eventType, new LinkedList<>());
        }
        listenerContainer
                .get(eventType)
                .add((EventListener<Event<?>>) listener);
        return this;
    }

    public boolean removeListenerByName(Class<?> eventType, String name) {
        if (!listenerContainer.containsKey(eventType)) {
            return false;
        }
        listenerContainer
                .get(eventType)
                .removeIf(listener -> name.equals(listener.name()));
        return true;
    }

    public boolean isListenerExist(Class<?> eventType, String name) {
        if (!listenerContainer.containsKey(eventType)) {
            return false;
        }
        for (EventListener<? extends Event<?>> listener : listenerContainer.get(eventType)) {
            if (name.equals(listener.name())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 同步地发布一个事件，事件的处理将会同步进行
     *
     * @param event       要发布的事件
     * @param eventType   事件类型，事件类型决定该发布的事件用何种监听器处理
     * @param allowBubble 是否允许冒泡，如果允许，事件会从逐级往上通知父类监听器
     * @return void
     * <AUTHOR> ^ ▽ ^ *)
     * @date 2023/5/9 16:49
     */
    public void publishEventSync(Event<?> event, Class<?> eventType, boolean allowBubble) {
        if (event == null) {
            throw new NullPointerException("无法发布");
        }
        List<EventListener<Event<?>>> listeners = listenerContainer.get(eventType);
        if (listeners != null) {
            for (EventListener<Event<?>> listener : listeners
                    .stream()
                    .sorted((o1, o2) -> Integer.compare(o2.level(), o1.level()))
                    .collect(Collectors.toList())) {
                try {
                    listener.onEvent(event);
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("执行监听器：{}时发生异常：{}：{}", listener.name(), e
                            .getClass()
                            .getSimpleName(), e.getMessage());
                }
            }
        }
        if (allowBubble) {
            Class<?> superClass = eventType.getSuperclass();
            if (superClass != null && Event.class.isAssignableFrom(superClass)) {
                publishEventSync(event, superClass, true);
            }
        }
    }

    /**
     * 异步地发布一个事件，事件的处理将会异步进行，主线程不会等待事件的处理
     *
     * @param event       要发布的事件
     * @param eventType   事件类型，事件类型决定该发布的事件用何种监听器处理
     * @param allowBubble 是否允许冒泡，如果允许，事件会从逐级往上通知父类监听器
     * @return void
     * <AUTHOR> ^ ▽ ^ *)
     * @date 2023/5/9 16:53
     */
    public void publishEvent(Event<?> event, Class<?> eventType, boolean allowBubble) {
        if (event == null) {
            throw new NullPointerException("无法发布");
        }
        List<EventListener<Event<?>>> listeners = listenerContainer.get(eventType);
        if (listeners != null) {
            for (EventListener<Event<?>> listener : listeners
                    .stream()
                    .sorted((o1, o2) -> Integer.compare(o2.level(), o1.level()))
                    .collect(Collectors.toList())) {
                eventHandleThreads.submit(() -> {
                    try {
                        listener.onEvent(event);
                    } catch (Exception e) {
                        e.printStackTrace();
                        log.error("执行监听器：{}时发生异常：{}:{}", listener.name(), e
                                .getClass()
                                .getSimpleName(), e.getMessage());
                    }
                });
            }
        }
        if (allowBubble) {
            Class<?> superClass = eventType.getSuperclass();
            if (superClass != null && Event.class.isAssignableFrom(superClass)) {
                publishEvent(event, superClass, true);
            }
        }
    }


}
