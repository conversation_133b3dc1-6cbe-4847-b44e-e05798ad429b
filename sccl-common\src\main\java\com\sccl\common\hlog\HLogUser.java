package com.sccl.common.hlog;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @Description 用于描述统一日志的客户端用户，拷贝于com.sccl.modules.system.user.domain.User
 * @Auther Huang <PERSON>
 * @Date 2022/01/05 10:45
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class HLogUser {
    private static final long serialVersionUID = 1L;
    /**
     * ID
     */
    private Long id;
    /**
     * 登录账号
     */
    private String loginId;
    /**
     * 密码
     */
    private String password;
    /**
     * 用户名称
     */
    private String userName;
    /**
     * 可选昵称
     */
    private String name;
    /**
     * 用户性别（0男 1女 2未知）
     */
    private String sex;
    /**
     * 用户身份证号
     */
    private String idCard;
    /**
     * 统一认证账号(根据系统自身情况选择)
     */
    private String hrLoginId;
    /**
     * 头像路径
     */
    private String avatarUri;
    /**
     * 帐号状态（0正常 1停用）
     */
    private String status;

    /**
     * 最后登陆IP
     */
    private String loginIp;
    /**
     * 最后登陆时间
     */
    private Date loginDate;
    /**
     * 手机号
     */
    private String phone;

    public boolean isAdmin() {
        return isAdmin(this.getId());
    }

    public static boolean isAdmin(Long userId) {
        return userId != null && 1L == userId;
    }
}
