package com.enrising.dcarbon.collect;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-06-21 11:25
 * @email <EMAIL>
 */
public class CollectionUtil {

    @FunctionalInterface
    public interface Callback<T> {
        void callback(T callback);
    }

    /**
     * 将大集合分成小的批次，并且进行回调
     *
     * @param collection 集合
     * @param limit      单批数目
     * @param callback   回调函数
     * @return void
     * <AUTHOR> ^ ▽ ^ *)
     * @date 2023/6/21 11:35
     */
    public static <T> void batchedCollection(Collection<T> collection, int limit, Callback<List<T>> callback) {
        if (callback == null) {
            throw new NullPointerException("回调不能为null");
        }
        if (collection == null || collection.size() == 0) {
            return;
        }
        List<T> cache = new ArrayList<>();
        for (T data : collection) {
            cache.add(data);
            if (cache.size() == limit) {
                try {
                    callback.callback(cache);
                } finally {
                    cache.clear();
                }
            }
        }
        if (cache.size() > 0) {
            try {
                callback.callback(cache);
            } finally {
                cache.clear();
            }
        }
    }
}
