package com.enrising.dcarbon.demo.collector;

import com.enrising.dcarbon.collector.ExecuteCollectorEvent;
import com.enrising.dcarbon.thread.SyncHelper;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-12 14:16
 * @email <EMAIL>
 */
public class Client {
    public static void main(String[] args) {
        //创建模板
        DemoCollectorTemplate template = new DemoCollectorTemplate();
        //创建触发器
        DemoCronTrigger trigger = new DemoCronTrigger(template, "55 39 14 * * ?");
        DemoCycleTrigger cycleTrigger = new DemoCycleTrigger(template, 5, TimeUnit.SECONDS);
        DemoObserverTrigger observerTrigger = new DemoObserverTrigger(template);
        //注册触发器，注册后触发器才能生效
        cycleTrigger.register();
        //发布事件
        //DemoObserverTrigger.publishExecuteCollectorEvent(new ExecuteCollectorEvent<>());
        SyncHelper.sleepQuietly(500, TimeUnit.SECONDS);
    }
}
