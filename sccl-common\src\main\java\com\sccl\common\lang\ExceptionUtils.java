
package com.sccl.common.lang;

import java.io.PrintWriter;
import java.io.StringWriter;

/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2018年8月18日 上午12:11:44
 * @ClassName: ExceptionUtils
 * 
 */
public class ExceptionUtils {

    /**
     * 在request中获取异常类
     * 
     * @param request
     * @return
     */
//    public static Throwable getThrowable(HttpServletRequest request) {
//        Throwable ex = null;
//        if (request.getAttribute("exception") != null) {
//            ex = (Throwable) request.getAttribute("exception");
//        } else if (request.getAttribute("javax.servlet.error.exception") != null) {
//            ex = (Throwable) request.getAttribute("javax.servlet.error.exception");
//        }
//        return ex;
//    }

    /**
     * 将ErrorStack转化为String.
     */
    public static String getStackTraceAsString(Throwable e) {
        if (e == null) {
            return "";
        }
        StringWriter stringWriter = new StringWriter();
        e.printStackTrace(new PrintWriter(stringWriter));
        return stringWriter.toString();
    }

    /**
     * 判断异常是否由某些底层的异常引起.
     */
    @SuppressWarnings("unchecked")
    public static boolean isCausedBy(Exception ex, Class<? extends Exception>... causeExceptionClasses) {
        Throwable cause = ex.getCause();
        while (cause != null) {
            for (Class<? extends Exception> causeClass : causeExceptionClasses) {
                if (causeClass.isInstance(cause)) {
                    return true;
                }
            }
            cause = cause.getCause();
        }
        return false;
    }

    /**
     * 将CheckedException转换为UncheckedException.
     */
    public static RuntimeException unchecked(Exception e) {
        if (e instanceof RuntimeException) {
            return (RuntimeException) e;
        } else {
            return new RuntimeException(e);
        }
    }

}