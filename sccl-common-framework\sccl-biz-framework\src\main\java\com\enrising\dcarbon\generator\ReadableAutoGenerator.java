package com.enrising.dcarbon.generator;

import com.baomidou.mybatisplus.generator.AutoGenerator;
import com.baomidou.mybatisplus.generator.config.DataSourceConfig;
import com.baomidou.mybatisplus.generator.config.builder.ConfigBuilder;
import com.baomidou.mybatisplus.generator.config.po.TableInfo;

import java.util.List;

/**
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-31 11:29
 * @email <EMAIL>
 */
public class ReadableAutoGenerator extends AutoGenerator {
    /**
     * 构造方法
     *
     * @param dataSourceConfig 数据库配置
     * @since 3.5.0
     */
    public ReadableAutoGenerator(DataSourceConfig dataSourceConfig) {
        super(dataSourceConfig);
    }

    public List<TableInfo> getAllTableInfoList() {
        if (null == config) {
            config = new ConfigBuilder(getPackageInfo(), getDataSource(), getStrategy(), getTemplate(), getGlobalConfig(), injection);
        }
        return super.getAllTableInfoList(this.config);
    }
}
