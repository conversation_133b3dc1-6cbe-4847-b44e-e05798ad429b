FROM yd-artifact.srdcloud.cn/sc-oshare-docker-mc/tomcat-alpine-hlog:8.5.51.12
RUN rm -rf /usr/local/tomcat/webapps/ROOT
# 删除Tomcat中可能存在的slf4j-log4j12依赖，避免与项目中的Logback冲突
RUN find /usr/local/tomcat/lib -name "*slf4j-log4j12*" -delete || true
ADD ./sccl-web/target/energy-cost.war /usr/local/tomcat/webapps/energy-cost.war
ADD hlog/client.truststore.jks /app/hlog/client.truststore.jks
ADD hlog/client.keystore.jks /app/hlog/client.keystore.jks
# 修改docker时区为东八区，规避应用运行时间与北京时间相差8小时问题
ENV TZ=Asia/Shanghai
CMD ["/usr/local/tomcat/bin/catalina.sh","run"]

#FROM yd-artifact.srdcloud.cn/sc-oshare-docker-mc/openjdk-alpine-hlog:1.12.4
#ADD hlog/client.truststore.jks /app/hlog/client.truststore.jks
#ADD hlog/client.keystore.jks /app/hlog/client.keystore.jks
#ADD ./sccl-web/target/energy-cost.jar /app/
#ADD ./startup.sh /app/
#
#RUN chmod a+x /app/startup.sh
#WORKDIR /app/
## 修改docker时区为东八区，规避应用程序和北京时间相差8小时问题
#ENV TZ=Asia/Shanghai
#EXPOSE 8080
#CMD ["/bin/sh", "-c", "/app/startup.sh"]
