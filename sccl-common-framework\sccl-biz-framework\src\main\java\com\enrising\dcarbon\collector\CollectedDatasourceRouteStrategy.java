package com.enrising.dcarbon.collector;

import java.util.List;

/**
 * 采集数据路由策略
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-05 11:25
 * @email <EMAIL>
 */
public interface CollectedDatasourceRouteStrategy {
    default void beforeRoute(List<CollectedDatasource> datasourceList) {

    }

    void route(List<CollectedDatasource> datasourceList);

    default void afterRoute(List<CollectedDatasource> datasourceList) {

    }
}
