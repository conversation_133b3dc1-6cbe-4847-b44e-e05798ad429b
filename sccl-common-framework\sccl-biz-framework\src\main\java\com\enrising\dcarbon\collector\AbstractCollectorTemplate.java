package com.enrising.dcarbon.collector;

import com.enrising.dcarbon.audit.AuditContext;
import com.enrising.dcarbon.audit.RefereeResult;
import com.enrising.dcarbon.servlet.ServletUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 抽象采集器模板
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-05 9:38
 * @email <EMAIL>
 */
@Slf4j
public abstract class AbstractCollectorTemplate {
    /**
     * 收集器，负责数据的收集，在使用{@link #executeActiveTemplate()}主动方式下，请务必提供收集器
     *
     * @return Collector
     * <AUTHOR> ^ ▽ ^ *)
     * @date 2023/5/5 12:16
     */
    protected abstract Collector collector();

    /**
     * 数据处理器链，如果不需要对数据进行加工处理，直接返回Null
     *
     * @return AbstractCollectedDatasourceHandler
     * <AUTHOR> ^ ▽ ^ *)
     * @date 2023/5/5 12:17
     */
    protected abstract CollectedDatasourceHandlerChain handler<PERSON>hain();

    /**
     * 基于稽核链的数据稽核上下文，如果不需要对数据进行稽核，直接返回Null，稽核后如果需要对数据进行剔除等操作，请直接重写{@link #afterAudit(List, Map)}方法
     *
     * @return AuditContext
     * <AUTHOR> ^ ▽ ^ *)
     * @date 2023/5/5 12:17
     */
    protected abstract AuditContext auditContext();

    /**
     * 路由策略上下文，数据的路由采取策略模式，策略的选取交由客户端实现
     *
     * @return CollectedDatasourceRouteStrategyContext
     * <AUTHOR> ^ ▽ ^ *)
     * @date 2023/5/5 13:37
     */
    protected abstract CollectedDatasourceRouteStrategyContext strategyContext();


    protected void beforeCollect(Collector collector) {

    }

    protected void afterCollect(List<CollectedDatasource> datasourceList) {

    }

    protected void beforeHandle(CollectedDatasourceHandlerChain chain, List<CollectedDatasource> datasourceList) {

    }

    protected void afterHandle(List<CollectedDatasource> datasourceList) {

    }

    protected void beforeAudit(AuditContext auditContext, List<CollectedDatasource> datasourceList) {

    }

    protected void afterAudit(List<CollectedDatasource> datasourceList, Map<String, List<RefereeResult>> resultsMap) {

    }

    protected void beforeRoute(CollectedDatasourceRouteStrategyContext strategyContext, List<CollectedDatasource> datasourceList) {

    }

    protected void afterRoute(List<CollectedDatasource> datasourceList) {

    }

    /**
     * 主动采集，当指定了收集器时使用该方法，方法会自动调用收集器收集数据进行处理
     *
     * @return void
     * <AUTHOR> ^ ▽ ^ *)
     * @date 2023/5/10 14:06
     */
    public void executeActiveTemplate() throws CollectorException {
        long start = System.currentTimeMillis();
        try {
            log.info("主动采集模板启动");
            ServletUtil.bind();
            Collector collector = collector();
            if (collector == null) {
                throw new CollectorException("收集器为Null");
            }
            beforeCollect(collector);
            List<CollectedDatasource> datasourceList = collector.collect();
            if (datasourceList == null || datasourceList.size() == 0) {
                return;
            }
            afterCollect(datasourceList);
            int total = datasourceList.size();
            datasourceList = datasourceList
                    .stream()
                    .filter(CollectedDatasource::isAvailable)
                    .collect(Collectors.toList());
            if (datasourceList.size() < total) {
                log.info("主动采集模板过滤出{}条无效数据", total - datasourceList.size());
            }
            CollectedDatasourceHandlerChain handlerChain = handlerChain();
            if (handlerChain != null) {
                beforeHandle(handlerChain, datasourceList);
                List<CollectedDatasource> handed = handlerChain().execute(datasourceList);
                datasourceList = handed != null ? handed : datasourceList;
                afterHandle(datasourceList);
            }
            AuditContext auditContext = auditContext();
            if (auditContext != null) {
                beforeAudit(auditContext, datasourceList);
                auditContext.addProgressListener(progress -> log.info("当前稽核进度：{}/{}", progress.getFinishedCount(), progress.getTotalCount()));
                datasourceList.forEach(auditContext::submit);
                Map<String, List<RefereeResult>> results = auditContext.getResultsMapSync();
                afterAudit(datasourceList, results);
            }

            CollectedDatasourceRouteStrategyContext strategyContext = strategyContext();
            if (strategyContext == null) {
                throw new CollectorException("采集数据路由策略上下文为null");
            }
            log.info("最终采集到{}条数据", datasourceList.size());
            if (datasourceList.size() == 0) {
                return;
            }
            beforeRoute(strategyContext, datasourceList);
            strategyContext.execute(datasourceList);
            afterRoute(datasourceList);
            log.info("主动采集模板执行成功完成");
        } catch (Exception e) {
            log.error("主动采集模板执行失败：{}:{}", e
                    .getClass()
                    .getSimpleName(), e.getMessage());
            if (!(e instanceof CollectorException)) {
                throw new CollectorException(e);
            }
            throw e;
        } finally {
            log.info("主动采集模板执行耗时：{}ms", System.currentTimeMillis() - start);
        }
    }

    /**
     * 被动采集，当没有指定收集器时调用，如数据是第三方调用我们的API传来的数据就可以使用该方法
     *
     * @param datasourceList 第三方发送来的数据
     * @return void
     * <AUTHOR> ^ ▽ ^ *)
     * @date 2023/5/10 14:06
     */
    public void executePassiveTemplate(List<CollectedDatasource> datasourceList) throws CollectorException {
        long start = System.currentTimeMillis();
        try {
            if (datasourceList == null || datasourceList.size() == 0) {
                throw new CollectorException("被动采集模板处理失败，要求处理的数据为空");
            }
            log.info("被动采集模板启动");
            ServletUtil.bind();
            int total = datasourceList.size();
            datasourceList = datasourceList
                    .stream()
                    .filter(CollectedDatasource::isAvailable)
                    .collect(Collectors.toList());
            if (datasourceList.size() < total) {
                log.info("被动采集模板过滤出{}条无效数据", total - datasourceList.size());
            }
            log.info("采集到{}条有效数据", datasourceList.size());
            CollectedDatasourceHandlerChain handlerChain = handlerChain();
            if (handlerChain != null) {
                beforeHandle(handlerChain, datasourceList);
                List<CollectedDatasource> handed = handlerChain().execute(datasourceList);
                datasourceList = handed != null ? handed : datasourceList;
                afterHandle(datasourceList);
            }
            AuditContext auditContext = auditContext();
            if (auditContext != null) {
                beforeAudit(auditContext, datasourceList);
                datasourceList.forEach(auditContext::submit);
                Map<String, List<RefereeResult>> results = auditContext.getResultsMapSync();
                afterAudit(datasourceList, results);
            }

            CollectedDatasourceRouteStrategyContext strategyContext = strategyContext();
            if (strategyContext == null) {
                throw new CollectorException("采集数据路由策略上下文为null");
            }
            beforeRoute(strategyContext, datasourceList);
            strategyContext.execute(datasourceList);
            afterRoute(datasourceList);
            log.info("被动采集模板执行成功完成");
        } catch (Exception e) {
            log.error("被动采集模板执行失败：{}:{}", e
                    .getClass()
                    .getSimpleName(), e.getMessage());
            throw e;
        } finally {
            log.info("被动采集模板执行耗时：{}ms", System.currentTimeMillis() - start);
        }
    }

    /**
     * 由于采集器的数据在数据处理链、稽核链以及算法过程节点之间传输都使用的是上层泛型接口{@link CollectedDatasource}，在实际应用中可能每个节点处理的是继承
     * {@link CollectedDatasource}不同对象，因此模板提供转化方法。
     *
     * @param datasourceList 上层泛型采集数据，列表可以有多种继承{@link CollectedDatasource}的对象，该方法会自动筛选给定类型的数据并转化
     * @param type           要转化到的子类对象列表
     * @return java.util.List<T>
     * <AUTHOR> ^ ▽ ^ *)
     * @date 2023/6/1 15:53
     */
    @SuppressWarnings("unchecked")
    public static <T extends CollectedDatasource> List<T> convert(List<CollectedDatasource> datasourceList, Class<T> type) {
        if (datasourceList == null || type == null) {
            return Collections.emptyList();
        }
        return datasourceList
                .stream()
                .filter(item -> type.isAssignableFrom(item.getClass()))
                .map(item -> (T) item)
                .collect(Collectors.toList());
    }

    @SuppressWarnings("unchecked")
    public static <T extends AbstractCollectorTemplate> T convert(AbstractCollectorTemplate template, Class<T> type) {
        return type.isAssignableFrom(template.getClass()) ? (T) template : null;
    }
}
