package com.enrising.dcarbon.demo.delegate;

import com.enrising.dcarbon.delegate.NamedTask;
import com.enrising.dcarbon.demo.delegate.tasks.AddTask;
import com.enrising.dcarbon.demo.delegate.tasks.DelTask;
import com.enrising.dcarbon.demo.delegate.tasks.UpdateTask;

/**
 * 测试枚举操作类型
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-10 15:01
 * @email <EMAIL>
 */
public enum HandleType {
    ADD("add") {
        @Override
        public NamedTask getTask() {
            return new AddTask();
        }
    }, UPDATE("update") {
        @Override
        public NamedTask getTask() {
            return new UpdateTask();
        }
    }, DELETE("del") {
        @Override
        public NamedTask getTask() {
            return new DelTask();
        }
    };
    private final String name;

    HandleType(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public static HandleType findByName(String name) {
        for (HandleType type : HandleType.values()) {
            if (type.name.equals(name)) {
                return type;
            }
        }
        return null;
    }

    public NamedTask getTask() {
        throw new UnsupportedOperationException();
    }
}
