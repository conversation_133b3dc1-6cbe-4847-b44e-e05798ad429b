package com.enrising.dcarbon.generator;


/**
 * 代码脚手架
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-29 14:24
 * @email <EMAIL>
 */
public class GeneratorApplication {
    public static void main(String[] args) {
        //FrameworkGenerator
        //        .builder()
        //        .addTableName("aj_config")
        //        .addTablePrefix("aj_")
        //        .setAuthor("JingGe(* ^ ▽ ^ *)")
        //        .setPath("D:\\work\\创立\\项目源代码\\能耗-重构\\trunk\\dcarbon-biz\\dcarbon-biz-framework\\dcarbon-framework-demo\\src\\main\\java")
        //        .setParentPackage("com.example.demo")
        //        .setUseStatefulObserverService(true)
        //        .build()
        //        .generate();
        FrameworkGenerator.bootstrap();
    }
}
