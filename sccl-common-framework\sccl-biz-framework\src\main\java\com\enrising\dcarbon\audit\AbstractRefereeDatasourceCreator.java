package com.enrising.dcarbon.audit;


import com.enrising.dcarbon.bean.SpringUtil;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 抽象的评判数据源创建器
 *
 * <AUTHOR>
 * @Date 2022/11/10 17:32
 * @Email <EMAIL>
 */
public abstract class AbstractRefereeDatasourceCreator implements Cloneable {

    /**
     * 评判所需数据的收集，mapper的获取可以通过{@link SpringUtil}来获取
     *
     * @param auditable 可稽核对象
     * @return java.util.Map（评判数据源类型，评判数据）
     * <AUTHOR>
     * @date 2022/11/11 12:28
     */
    public abstract Map<Class<? extends RefereeDatasource>, List<RefereeDatasource>> collect(Auditable auditable);

    /**
     * 获取对应的评判器
     */
    public abstract AbstractReferee getReferee();

    /**
     * 获取Mapper实例，如果mapper没有找到会抛出{@link NullPointerException}
     *
     * @param mapperType mapper类型
     * @return T
     * <AUTHOR> ^ ▽ ^ *)
     * @date 2023/8/4 15:09
     */
    protected <T> T getMapperInstance(Class<T> mapperType) {
        return Objects.requireNonNull(SpringUtil.getBean(mapperType));
    }

    @Override
    protected Object clone() throws CloneNotSupportedException {
        return super.clone();
    }
}
