package com.enrising.dcarbon.audit;

import com.enrising.dcarbon.id.SystemClock;
import com.enrising.dcarbon.observer.EventListener;
import com.enrising.dcarbon.observer.EventManger;
import com.enrising.dcarbon.progress.ProgressManager;
import com.enrising.dcarbon.string.StringUtils;
import com.enrising.dcarbon.thread.ScheduleTaskUtil;
import com.enrising.dcarbon.thread.SyncHelper;
import lombok.extern.slf4j.Slf4j;

import java.io.Closeable;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 数据评判链上下文，强烈建议使用单例模式
 *
 * <AUTHOR> <PERSON>
 * @Date 2022/11/11 9:44
 * @Email <EMAIL>
 */
@Slf4j
public class AuditContext implements Closeable {
    /**
     * 当前稽核的对象
     */
    public static final InheritableThreadLocal<Auditable> currentAuditableObject = new InheritableThreadLocal<>();

    private final EventManger eventManger = new EventManger();

    public ScheduleTaskUtil singleThread;

    protected RefereeChain refereeChain;

    protected RefereeChainPool chainPool;

    private final LinkedBlockingQueue<Auditable> queue;

    /**
     * 用于存放异步稽核的评判结果（稽核对象ID，评判结果集）
     */
    private Map<String, List<RefereeResult>> resultsMap;

    private volatile boolean isFirstOpen = true;

    private volatile boolean isClose = false;

    private volatile boolean isOpenAsynchronousMode = false;

    private volatile boolean isOpenTimeoutControl = false;

    private final Object globalLock = new Object();

    private Thread currentAuditThread = null;

    private volatile long startAuditTime;

    private final boolean isUsePool;

    private final ProgressManager progressHelper = new ProgressManager();

    private final Map<String, ProgressManager> progressManagerMap = new ConcurrentHashMap<>();

    private final Map<String, String> progressKeyMap = new ConcurrentHashMap<>();

    private final List<ProgressListener> progressListeners = new ArrayList<>();

    private ThreadPoolExecutor poolExecutor;


    /**
     * 创建一个单链单线程且最大异步容量为5000的上下文，单链单线程下性能会较低
     *
     * @param refereeChain 评判链
     * <AUTHOR> Yongxiang
     * @date 2022/11/15 16:17
     */
    public AuditContext(RefereeChain refereeChain) {
        this(refereeChain, 5000);
    }

    /**
     * 创建给定异步容量的单链单线程上下文，单链单线程下性能会较低
     *
     * @param refereeChain 评判链
     * @param capacity     容量大小
     * <AUTHOR> Yongxiang
     * @date 2022/11/15 16:17
     */
    public AuditContext(RefereeChain refereeChain, int capacity) {
        if (refereeChain == null || refereeChain.isEmpty()) {
            throw new NullPointerException("评判链为空");
        }
        if (!refereeChain.isFinished()) {
            throw new IllegalArgumentException("不能提供正在使用中的评判器链");
        }
        this.refereeChain = refereeChain;
        isUsePool = false;
        singleThread = ScheduleTaskUtil.build(false, "singleChainAuditThread");
        queue = new LinkedBlockingQueue<>(capacity);
        this.refereeChain.currentAuditContext = this;
    }

    /**
     * 构建一个多链多线程的高性能评判上下文，这是建议使用的方式
     *
     * @param chainPool    评判链池
     * @param poolExecutor 评判线程池，性能最高是线程数和评判链数相等
     * @param capacity     容量
     * <AUTHOR> Yongxiang
     * @date 2022/12/1 10:48
     */
    public AuditContext(RefereeChainPool chainPool, ThreadPoolExecutor poolExecutor, int capacity) {
        if (chainPool == null) {
            throw new NullPointerException("空评判链池");
        }
        this.chainPool = chainPool;
        isUsePool = true;
        this.poolExecutor = poolExecutor;
        queue = new LinkedBlockingQueue<>(capacity);
    }

    /**
     * 构建一个多链多线程的高性能评判上下文，这是建议使用的方式，该构造方法会默认使用等同评判链池最大评判链容量的评判线程池，并且使用默认容量5000
     *
     * @param chainPool 评判链池
     * <AUTHOR> Yongxiang
     * @date 2022/12/1 11:02
     */
    public AuditContext(RefereeChainPool chainPool) {
        this(chainPool, new ThreadPoolExecutor(chainPool.getPoolSize(), chainPool.getPoolSize(), 60, TimeUnit.SECONDS, new LinkedBlockingQueue<>(5000)), 5000);
    }

    public void addProgressListener(ProgressListener listener) {
        if (listener == null) {
            throw new NullPointerException("放入的监听器不得为null");
        }
        synchronized (globalLock) {
            this.progressListeners.add(listener);
        }
    }

    public Optional<ProgressListener> findProgressListener(String name) {
        synchronized (globalLock) {
            return this.progressListeners
                    .stream()
                    .filter(item -> item
                            .name()
                            .equals(name))
                    .findFirst();
        }
    }

    public Optional<ProgressListener> findProgressListener(Class<? extends ProgressListener> listenerType) {
        synchronized (globalLock) {
            return findProgressListener(listenerType.getSimpleName());
        }
    }


    public void removeProgressListenerByName(String name) {
        synchronized (globalLock) {
            this.progressListeners.removeIf(item -> item
                    .name()
                    .equals(name));
        }
    }

    public void removeProgressListenerByType(Class<? extends ProgressListener> listenerType) {
        synchronized (globalLock) {
            removeProgressListenerByName(listenerType.getSimpleName());
        }
    }

    /**
     * 添加一个稽核事件监听器
     *
     * @param eventListener 添加的监听器实例
     * @return com.enrising.dcarbon.audit.AuditContext
     * <AUTHOR> ^ ▽ ^ *)
     * @date 2023/8/4 15:01
     */
    public AuditContext addAuditEventListener(EventListener<AuditEvent> eventListener) {
        this.eventManger.addListenerIfAbsent(AuditEvent.class, eventListener);
        return this;
    }

    protected void publishAuditEvent(AuditEvent event) {
        this.eventManger.publishEvent(event, AuditEvent.class, true);
    }

    /**
     * 开启超时控制，该方法用于解决部分稽核对象阻塞过长时间的情况，开启后如果有对象稽核占用过长时间将会在稽核线程加上interrupt
     * 标记，评判链每次向后传播时将会检查是否中断，中断后后续的稽核将会丢失，后续线程的状态该方法不会作任何判断
     *
     * @param timeout 超时时长
     * @param unit    单位
     * @return void
     * <AUTHOR> Yongxiang
     * @date 2022/11/15 16:40
     */
    public synchronized void openTimeoutControl(long timeout, TimeUnit unit) {
        if (isOpenTimeoutControl) {
            throw new IllegalStateException("超时控制已开启，如果需要重新设置，请先关闭超时控制");
        }
        isOpenTimeoutControl = true;
        Thread thread = new Thread(() -> {
            do {
                try {
                    SyncHelper.sleepQuietly(1, TimeUnit.MILLISECONDS);
                    if (startAuditTime > 0 && SystemClock.now() - startAuditTime > unit.toMillis(timeout) && currentAuditThread != null) {
                        log.warn("稽核超时，将会尝试中断");
                        currentAuditThread.interrupt();
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            } while (isOpenTimeoutControl);
        });
        thread.setName("timeoutController");
        thread.setDaemon(true);
        thread.start();
    }

    public void closeTimeoutControl() {
        isOpenTimeoutControl = false;
    }

    /**
     * 启动稽核，该方法是同步方法
     *
     * @param auditable 可稽核对象
     * @return void
     * <AUTHOR> Yongxiang
     * @date 2022/11/25 10:42
     */
    public synchronized void startAudit(Auditable auditable) {
        if (isClose) {
            throw new IllegalStateException("上下文已关闭");
        }
        if (auditable == null) {
            throw new NullPointerException("空稽核对象，无法评判");
        }
        RefereeChain refereeChain = getRefereeChain();
        if (refereeChain == null) {
            throw new IllegalStateException("暂无可供使用的评判链");
        }
        if (refereeChain.currentAuditContext == null) {
            refereeChain.currentAuditContext = this;
        }
        currentAuditThread = Thread.currentThread();
        startAuditTime = SystemClock.now();
        startAudit(refereeChain, auditable, false);
        startAuditTime = 0;
        currentAuditThread = null;
    }

    private void startAudit(RefereeChain availableChain, Auditable auditable, boolean isAutoSubmit) {
        if (isClose && !isAutoSubmit) {
            throw new IllegalStateException("上下文已关闭");
        }
        if (availableChain == null || !availableChain.isFinished()) {
            throw new IllegalStateException("评判链不可用");
        }
        if (availableChain.currentAuditContext == null) {
            availableChain.currentAuditContext = this;
        }
        currentAuditableObject.set(auditable);
        availableChain.startChainReferee(auditable);
        currentAuditableObject.remove();
        if (isOpenAsynchronousMode && resultsMap != null) {
            List<RefereeResult> refereeResults = availableChain.collectResults();
            //log.info("收集到{}的{}条稽核结果", auditable.getAuditKey(), refereeResults.size());
            if (!resultsMap.containsKey(auditable.getAuditKey())) {
                resultsMap.put(auditable.getAuditKey(), refereeResults);
            } else {
                resultsMap
                        .get(auditable.getAuditKey())
                        .addAll(refereeResults);
            }
        }
    }


    /**
     * 获取异步评判的结果，该方法实时返回，不代表最终结果
     *
     * @return java.util.Map
     * <AUTHOR> Yongxiang
     * @date 2022/11/11 16:59
     */
    public Map<String, List<RefereeResult>> getResultsMap() {
        if (!isOpenAsynchronousMode) {
            throw new IllegalStateException("上下文未开启异步模式，你可以通过submit()方法开启");
        }
        return resultsMap;
    }

    /**
     * 重设进度
     *
     * @return void
     * <AUTHOR> Yongxiang
     * @date 2022/12/5 16:29
     */
    public void resetProgress() {
        progressHelper.setTotalCount(0);
        progressHelper.update(0);
    }

    /**
     * 移除某个某账单的异步稽核结果，如果上下文没有开启异步模式将会抛出异常
     *
     * @param auditKey 要移除的稽核对象id
     * @return java.util.List<com.sccl.modules.business.stationaudit.framework.referee.RefereeResult>
     * <AUTHOR> Yongxiang
     * @date 2022/11/11 17:33
     */
    public List<RefereeResult> removeResult(String auditKey) {
        if (!isOpenAsynchronousMode) {
            throw new IllegalStateException("上下文未开启异步模式，你可以通过submit()方法开启");
        }
        if (!resultsMap.containsKey(auditKey)) {
            return Collections.emptyList();
        }
        return resultsMap.remove(auditKey);
    }

    /**
     * 清空整个异步稽核结果并通知gc，如果上下文没有开启异步模式将会抛出异常
     *
     * @return void
     * <AUTHOR> Yongxiang
     * @date 2022/11/11 17:33
     */
    public void cleanResultMap() {
        if (!isOpenAsynchronousMode) {
            throw new IllegalStateException("上下文未开启异步模式，你可以通过submit()方法开启");
        }
        resultsMap.clear();
        System.gc();
    }

    /**
     * 同步获取异步评判的结果，该方法会阻塞直到上下文处理完所有提交的稽核对象
     *
     * @return java.util.Map
     * <AUTHOR> Yongxiang
     * @date 2022/11/11 17:01
     */
    public Map<String, List<RefereeResult>> getResultsMapSync() {
        return getResultsMapSync(Long.MAX_VALUE, TimeUnit.MILLISECONDS);
    }

    /**
     * 同步获取异步评判的结果，该方法可以指定最长阻塞时间，阻塞时间完成后或者阻塞时间内提交的所有稽核对象稽核完成将会立即返回结果
     *
     * @param wait 等待的时间
     * @param unit 时间单位
     * @return java.util.Map
     * <AUTHOR> Yongxiang
     * @date 2022/11/15 17:10
     */
    public Map<String, List<RefereeResult>> getResultsMapSync(long wait, TimeUnit unit) {
        if (!isOpenAsynchronousMode) {
            throw new IllegalStateException("上下文未开启异步模式，你可以通过submit()方法开启");
        }
        if (isOpenAsynchronousMode) {
            if (!isUsePool) {
                SyncHelper.aWaitQuietly(() -> refereeChain.isFinished() && queue.size() == 0, wait, unit);
            } else {
                SyncHelper.aWaitQuietly(() -> queue.size() == 0 && progressHelper.getFinishedPercent() == 100, wait, unit);
            }
        } else {
            SyncHelper.aWaitQuietly(() -> refereeChain.isFinished(), wait, unit);
        }
        return resultsMap;
    }

    /**
     * 查询指定key的稽核结果，立即返回，如果没有开启异步模式会抛出异常
     *
     * @param auditKey 稽核key
     * @return java.util.List<com.enrising.dcarbon.audit.RefereeResult>
     * <AUTHOR> ^ ▽ ^ *)
     * @date 2023/8/15 16:47
     */
    public List<RefereeResult> getResult(String auditKey) {
        if (!isOpenAsynchronousMode) {
            throw new IllegalStateException("上下文未开启异步模式，你可以通过submit()方法开启");
        }
        return resultsMap.get(auditKey);
    }


    /**
     * 手动关闭异步模式
     *
     * @return void
     * <AUTHOR> Yongxiang
     * @date 2022/11/11 17:44
     */
    public void closeAsynchronousMode() {
        isOpenAsynchronousMode = false;
        isFirstOpen = true;
    }


    /**
     * 当前评判链是否空闲
     *
     * @return boolean
     * <AUTHOR> Yongxiang
     * @date 2022/11/11 10:08
     */
    public boolean isChainFree() {
        return refereeChain.isFinished();
    }

    /**
     * 提交一个待稽核稽核对象到上下文，容量已满时默认等待3秒
     *
     * @param auditable 提交的稽核对象
     * @return void
     * <AUTHOR> Yongxiang
     * @date 2022/11/11 10:25
     */
    public void submit(Auditable auditable) {
        submit(auditable, 3, TimeUnit.SECONDS, null);
    }

    public void submit(Auditable auditable, String progressKey) {
        submit(auditable, 3, TimeUnit.SECONDS, progressKey);
    }

    /**
     * 提交一个可稽核对象到上下文，如果此时评判链空闲将会直接评判，否则将会进行排队，注意，调用该方法前上下文都默认是同步模式，不会自动收集稽核对象评判结果到{@link #resultsMap}，一旦调用此方法后上下文将会开启异步模式，上下文将会启动异步稽核线程并且在每次稽核完后自动收集评判结果到{@link #resultsMap}，你可以通过{@link #closeAsynchronousMode()}来手动关闭异步模式
     *
     * @param auditableObject 可稽核对象
     * @param wait            如果容量已满，则等待的时长
     * @param unit            单位
     * @param progressKey     进度key，当指定时会给该稽核对象生成一个私有的进度管理器
     * @return void
     * <AUTHOR> Yongxiang
     * @date 2022/11/15 16:26
     */
    public void submit(Auditable auditableObject, long wait, TimeUnit unit, String progressKey) {
        if (auditableObject == null) {
            throw new NullPointerException("空可稽核对象，无法评判");
        }
        if (!auditableObject.isAvailable()) {
            throw new IllegalArgumentException("可稽核对象不可用");
        }
        if (isClose) {
            throw new IllegalStateException("上下文已关闭，不再接收新的稽核对象");
        }
        if (resultsMap == null) {
            synchronized (globalLock) {
                if (resultsMap == null) {
                    resultsMap = new ConcurrentHashMap<>();
                }
            }
        }
        ProgressManager current = null;
        if (!StringUtils.isEmpty(progressKey)) {
            if (!progressManagerMap.containsKey(progressKey)) {
                progressManagerMap.put(progressKey, new ProgressManager());
            }
            current = progressManagerMap.get(progressKey);
            progressKeyMap.put(auditableObject.getAuditKey(), progressKey);
        }
        if (current != null) {
            current.setTotalCount(current.getTotalCount() + 1);
        }
        progressHelper.setTotalCount(progressHelper.getTotalCount() + 1);

        try {
            if (!queue.offer(auditableObject, wait, unit)) {
                throw new IllegalStateException("上下文已满载，请稍后再试");
            }
        } catch (InterruptedException ignored) {
        } catch (IllegalStateException e) {
            e.printStackTrace();
            return;
        } catch (Exception e) {
            e.printStackTrace();
        }

        isOpenAsynchronousMode = true;
        //启动守护线程
        if (isFirstOpen) {
            synchronized (AuditContext.class) {
                if (isFirstOpen) {

                    Thread thread = new Thread(() -> {
                        do {
                            try {
                                SyncHelper.sleepQuietly(1, TimeUnit.MILLISECONDS);
                                if (queue.size() == 0) {
                                    continue;
                                }
                                RefereeChain availableChain = isUsePool ? chainPool.getRefereeChain() : refereeChain;
                                if (availableChain == null || !availableChain.isFinished()) {
                                    continue;
                                }
                                Runnable runnable = () -> {
                                    Auditable auditable = queue.poll();
                                    //if (queue.size() == 0) {
                                    //    progressHelper.setTotalCount(0);
                                    //    progressHelper.update(0);
                                    //}
                                    if (auditable != null) {
                                        startAudit(availableChain, auditable, true);
                                        if (progressKeyMap.containsKey(auditable.getAuditKey())) {
                                            String currentProgressKey = progressKeyMap.get(auditable.getAuditKey());
                                            progressManagerMap
                                                    .get(currentProgressKey)
                                                    .increment();
                                            executeProgressListeners(currentProgressKey);
                                            progressKeyMap.remove(auditable.getAuditKey());
                                        }
                                        progressHelper.increment();
                                        executeProgressListeners();
                                    }
                                };
                                //如果没有使用池
                                if (!isUsePool && isChainFree()) {
                                    singleThread.EOneTimeTask(() -> {
                                        runnable.run();
                                        return null;
                                    }, 0, TimeUnit.MILLISECONDS);
                                } else if (isUsePool) {
                                    poolExecutor.submit(() -> {
                                        try {
                                            runnable.run();
                                        } catch (Exception e) {
                                            e.printStackTrace();
                                        } finally {
                                            chainPool.release(availableChain);
                                        }
                                    });

                                }

                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        } while (!isClose && isOpenAsynchronousMode);
                    });
                    thread.setDaemon(true);
                    thread.setName("auditContextDaemonThread");
                    thread.start();
                    isFirstOpen = false;
                }
            }
        }
    }

    private volatile int lastProgress;

    private synchronized void executeProgressListeners() {
        if (progressListeners.size() > 0 && progressHelper.getTotalCount() > 0 && progressHelper.getFinishedCount() != lastProgress) {
            Progress progress = new Progress();
            progress.setProgressKey("globalProgress");
            progress.setFinishedCount(progressHelper.getFinishedCount());
            progress.setFinishedPercent(progressHelper.getFinishedPercent());
            progress.setPendingCount(progressHelper.getPendingCount());
            progress.setTotalCount(progressHelper.getTotalCount());
            lastProgress = progressHelper.getFinishedCount();
            try {
                progressListeners.forEach(listener -> {
                    try {
                        listener.onProgressChange(progress);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });
            } catch (ConcurrentModificationException ignored) {
            }
        }
        if (progressHelper.isOver()) {
            resetProgress();
        }
    }

    private synchronized void executeProgressListeners(String progressKey) {
        if (StringUtils.isEmpty(progressKey)) {
            return;
        }
        ProgressManager current = progressManagerMap.get(progressKey);
        if (current == null) {
            return;
        }
        if (progressListeners.size() > 0 && current.getTotalCount() > 0) {
            Progress progress = new Progress();
            progress.setProgressKey(progressKey);
            progress.setFinishedCount(current.getFinishedCount());
            progress.setFinishedPercent(current.getFinishedPercent());
            progress.setPendingCount(current.getPendingCount());
            progress.setTotalCount(current.getTotalCount());
            try {
                progressListeners.forEach(listener -> {
                    try {
                        listener.onProgressChange(progress);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });
            } catch (ConcurrentModificationException ignored) {
            }
        }
        if (current.isOver()) {
            progressManagerMap.remove(progressKey);
        }
    }

    private RefereeChain getRefereeChain() {
        if (refereeChain != null && refereeChain.isFinished()) {
            return refereeChain;
        } else if (chainPool != null) {
            return chainPool.getRefereeChain();
        }
        return null;
    }


    /**
     * 收集评判链内的评判结果
     *
     * @return java.util.List<com.sccl.modules.business.stationaudit.framework.referee.RefereeResult>
     * <AUTHOR> Yongxiang
     * @date 2022/11/11 10:09
     */
    public List<RefereeResult> collectRefereeResults() {
        return refereeChain.collectResults();
    }

    /**
     * 关闭上下文，关闭后上下文将不再接收新的评判
     *
     * @return void
     * <AUTHOR> Yongxiang
     * @date 2022/11/11 10:34
     */
    @Override
    public void close() {
        isClose = true;
        refereeChain = null;
        currentAuditableObject.remove();
        currentAuditThread = null;
        cleanResultMap();
    }
}
