package com.enrising.dcarbon.collector;

import java.util.List;

/**
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-06-05 16:08
 * @email <EMAIL>
 */
public abstract class AbstractCollectedDatasourceHandlerNode implements CollectedDatasourceHandler {
    public List<CollectedDatasource> handle(List<CollectedDatasource> datasourceList) {
        List<CollectedDatasource> handled = null;
        try {
            handled = doHandle(datasourceList);
            afterHandle(handled == null ? datasourceList : handled);
        } catch (Exception e) {
            handleError(handled == null ? datasourceList : handled, e);
        }
        return handled;
    }

    public void afterHandle(List<CollectedDatasource> datasourceList) {

    }

    public void handleError(List<CollectedDatasource> datasourceList, Throwable throwable) {

    }
}
