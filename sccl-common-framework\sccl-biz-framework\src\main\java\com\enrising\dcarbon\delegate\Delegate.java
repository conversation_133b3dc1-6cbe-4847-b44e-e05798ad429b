package com.enrising.dcarbon.delegate;

/**
 * 委派者，负责任务委派
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-10 14:13
 * @email <EMAIL>
 */
public interface Delegate extends TaskHandler {
    TaskHandler delegate(Task task);

    @Override
    default void handle(Task task) {
        try {
            delegate(task).handle(task);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
