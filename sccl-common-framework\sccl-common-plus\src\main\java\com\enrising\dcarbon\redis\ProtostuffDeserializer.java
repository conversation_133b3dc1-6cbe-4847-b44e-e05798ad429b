package com.enrising.dcarbon.redis;

import com.enrising.dcarbon.lang.Deserializer;
import com.enrising.dcarbon.message.MessageManager;

/**
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-07-13 14:30
 * @email <EMAIL>
 */
public class ProtostuffDeserializer<T> implements Deserializer<T, byte[]> {
    @Override
    public T deserialize(byte[] source, Class<T> type) {
        return MessageManager.fromMessage(source, type, MessageManager.PROTO_STUFF_DESERIALIZER);
    }
}
