package com.enrising.dcarbon.manage;

import com.enrising.dcarbon.observer.Event;
import com.enrising.dcarbon.observer.EventListener;
import com.enrising.dcarbon.servlet.ServletUtil;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.jingge.autojob.util.message.MessageManager;
import org.springframework.transaction.support.TransactionTemplate;

/**
 * Web专用的事件监听器，具备响应的写入功能
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-10 16:47
 * @email <EMAIL>
 */
public abstract class ResponsiveEventListener<E extends Event<?>> implements EventListener<E> {
    private volatile boolean spread = true;

    public boolean isSpread() {
        return spread;
    }

    public void setSpread(boolean spread) {
        this.spread = spread;
    }

    /**
     * 结束后续监听器处理，向前端返回响应，建议使用{@link MessageManager}进行消息构建
     *
     * @param msg 要响应的消息内容
     * @return void
     * <AUTHOR> ^ ▽ ^ *)
     * @date 2023/5/10 17:07
     */
    public void returnResponse(String msg) {
        ServletUtil.renderString(msg);
        spread = false;
    }

    /**
     * 写入消息到响应，不会立即返回，会继续向下传播
     *
     * @param msg 消息内容
     * @return void
     * <AUTHOR> ^ ▽ ^ *)
     * @date 2023/5/10 17:08
     */
    public void writeResponse(String msg) {
        ServletUtil.renderString(msg);
    }

    public HttpServletRequest request() {
        return ServletUtil.getRequest();
    }

    public HttpServletResponse response() {
        return ServletUtil.getResponse();
    }
}
