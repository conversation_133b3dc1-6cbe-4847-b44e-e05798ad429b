package com.enrising.dcarbon.generator;

import com.enrising.dcarbon.template.TemplateUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-31 10:01
 * @email <EMAIL>
 */
@Slf4j
public abstract class AbstractTemplateGenerator {
    protected String templatePath;

    public AbstractTemplateGenerator(String templatePath) {
        if (!templatePath.endsWith(".vm")) {
            throw new IllegalArgumentException("请输入正确的模板路径*.vm");
        }
        this.templatePath = templatePath;
    }

    public AbstractTemplateGenerator setTemplatePath(String templatePath) {
        this.templatePath = templatePath;
        return this;
    }

    public abstract Map<String, Object> metadata();

    public void afterMetadata(Map<String, Object> metadata) {

    }

    public abstract String dirPath();

    public void generate(String fileName) {
        try {
            Map<String, Object> metadata = metadata();
            if (metadata == null) {
                metadata = new HashMap<>();
            }
            TemplateUtil.execute(metadata, templatePath, dirPath(), fileName, metadata.containsKey("isAllowOverwriteListeners") && !(boolean) metadata.get("isAllowOverwriteListeners"));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
