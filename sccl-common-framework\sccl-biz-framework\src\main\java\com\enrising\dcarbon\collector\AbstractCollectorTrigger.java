package com.enrising.dcarbon.collector;

import com.enrising.dcarbon.lang.Trigger;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 采集器触发器
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-06 11:27
 * @email <EMAIL>
 */
@Slf4j
public abstract class AbstractCollectorTrigger implements Trigger {
    protected AbstractCollectorTemplate template;

    public AbstractCollectorTrigger(AbstractCollectorTemplate template) {
        if (template == null) {
            throw new NullPointerException("采集器模板不能为空");
        }
        this.template = template;
    }

    /**
     * 执行主动采集
     *
     * @return void
     * <AUTHOR> ^ ▽ ^ *)
     * @date 2023/5/12 14:53
     */
    protected void executeActiveTemplate() {
        template.executeActiveTemplate();
    }

    /**
     * 执行被动采集
     *
     * @param datasourceList 模板处理的数据
     * @return void
     * <AUTHOR> ^ ▽ ^ *)
     * @date 2023/5/12 14:54
     */
    protected void executePassiveTemplate(List<CollectedDatasource> datasourceList) {
        if (datasourceList == null || datasourceList.size() == 9) {
            return;
        }
        template.executePassiveTemplate(datasourceList);
    }

    public boolean allowTrig() {
        return true;
    }

    public void beforeTrig() {

    }

    public void afterTrig() {

    }

    public void errorTrig(Throwable throwable) {

    }


    /**
     * 触发逻辑写在这里
     *
     * @return void
     * <AUTHOR> ^ ▽ ^ *)
     * @date 2023/5/12 14:53
     */
    public abstract void execute();

    /**
     * 增强trig方法，增加前后钩子，模板的执行请调用{@link #execute()}方法
     *
     * @return void
     * <AUTHOR> ^ ▽ ^ *)
     * @date 2023/5/12 14:52
     */
    @Override
    public final void trig() {
        if (!allowTrig()) {
            log.warn("不允许被触发");
            return;
        }
        beforeTrig();
        try {
            execute();
            afterTrig();
        } catch (Exception e) {
            errorTrig(e);
        }
    }
}
