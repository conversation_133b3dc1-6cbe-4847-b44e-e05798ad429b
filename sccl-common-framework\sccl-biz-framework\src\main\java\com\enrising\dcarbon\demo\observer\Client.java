package com.enrising.dcarbon.demo.observer;

import com.enrising.dcarbon.observer.Event;
import com.enrising.dcarbon.observer.EventListener;
import com.enrising.dcarbon.observer.EventManger;

/**
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-09 14:50
 * @email <EMAIL>
 */
public class Client {
    public static void main(String[] args) {
        //创建事件1
        DemoEvent1 event1 = new DemoEvent1(new EventSource("这是事件1"));
        //创建事件2
        DemoEvent2 event2 = new DemoEvent2(new EventSource("这是事件2"));

        //创建事件管理器，建议使用单例
        EventManger eventManger = new EventManger();

        //创建监听事件1的监听器1
        EventListener<DemoEvent1> listener1 = event -> {
            System.out.println("监听到事件1，消息内容：" + event
                    .getSource()
                    .getMsg());
        };
        //创建监听事件2的监听器2
        EventListener<DemoEvent2> listener2 = event -> {
            System.out.println("监听到事件2，消息内容：" + event
                    .getSource()
                    .getMsg());
        };
        //创建监听所有事件的父事件监听器3
        EventListener<Event<?>> listener3 = event -> {
            System.out.println("父事件监听器监听到事件：" + event
                    .getClass()
                    .getSimpleName());
        };


        //添加监听器
        eventManger.addListener(DemoEvent1.class, listener1);
        eventManger.addListener(DemoEvent2.class, listener2);
        eventManger.addListener(Event.class, listener3);

        //发布事件1，允许事件冒泡
        eventManger.publishEventSync(event1, DemoEvent1.class, true);

        //发布事件2，不允许事件冒泡
        eventManger.publishEventSync(event2, DemoEvent2.class, false);
    }
}
