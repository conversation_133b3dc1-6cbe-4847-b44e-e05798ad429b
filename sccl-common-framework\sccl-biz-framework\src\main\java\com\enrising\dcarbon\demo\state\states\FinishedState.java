package com.enrising.dcarbon.demo.state.states;

import com.enrising.dcarbon.demo.state.DemoBaseData;
import com.enrising.dcarbon.demo.state.events.FinishedEvent;
import com.enrising.dcarbon.observer.EventManger;
import com.enrising.dcarbon.state.AbstractObserverState;
import com.enrising.dcarbon.state.StateEventFactory;

/**
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-10 10:19
 * @email <EMAIL>
 */
public class FinishedState extends DemoBaseDataState {

    public FinishedState(EventManger eventManger, DemoBaseData demoBaseData) {
        super(eventManger, demoBaseData);
    }

    @Override
    public StateEventFactory stateEventFactory() {
        return FinishedEvent::new;
    }
}
