package com.sccl.framework.aspectj.lang.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface ExcelExportField {

    /**
     * 导出到Excel中的名字 支持多级表头
     * @return
     */
    String[] title() default {};

    /**
     * 列顺序
     * @return
     */
    int order() default 0;

    String format() default "";
}
