package com.enrising.dcarbon.state;

/**
 * 状态的抽象接口
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-09 16:25
 * @email <EMAIL>
 */
public interface State {
    void change();

    /**
     * 状态可以携带一些信息
     *
     * @return Object
     * <AUTHOR> ^ ▽ ^ *)
     * @date 2023/5/10 11:26
     */
    Object getSource();

    /**
     * 返回管理当前状态的状态机
     *
     * @return com.enrising.dcarbon.state.StateMachine
     * <AUTHOR> ^ ▽ ^ *)
     * @date 2023/5/11 16:37
     */
    StateMachine currentStateMachine();
}
