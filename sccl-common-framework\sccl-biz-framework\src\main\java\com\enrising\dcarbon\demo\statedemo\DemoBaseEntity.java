package com.enrising.dcarbon.demo.statedemo;

import com.enrising.dcarbon.state.State;
import com.enrising.dcarbon.state.Stateful;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-17 9:56
 * @email <EMAIL>
 */
@Getter
@Setter
@ToString
public class DemoBaseEntity implements Stateful {
    private Long id;

    /**
     * 0-草稿 1-完成
     */
    private Integer status;

    @Override
    public State currentState() {
        if (status == 0) {
            return new DraftState(EventManagerInstance.EVENT_MANGER).setEntity(this);
        }
        return new FinishedState(EventManagerInstance.EVENT_MANGER).setEntity(this);
    }
}
