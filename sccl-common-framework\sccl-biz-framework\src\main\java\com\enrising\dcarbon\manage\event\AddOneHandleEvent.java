package com.enrising.dcarbon.manage.event;

import com.enrising.dcarbon.manage.BaseEntity;
import com.enrising.dcarbon.manage.WebHandleEvent;

/**
 * 新增一条记录事件
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-10 16:18
 * @email <EMAIL>
 */
public class AddOneHandleEvent extends WebHandleEvent<BaseEntity> {
    public AddOneHandleEvent() {
        super();
    }

    public AddOneHandleEvent(BaseEntity source) {
        super(source);
    }
}
