package com.enrising.dcarbon.demo.state.listener;

import com.enrising.dcarbon.demo.state.DemoBaseData;
import com.enrising.dcarbon.demo.state.events.AgendaEvent;
import com.enrising.dcarbon.observer.EventListener;

/**
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-10 11:09
 * @email <EMAIL>
 */
public class AgendaListener implements EventListener<AgendaEvent> {
    @Override
    public void onEvent(AgendaEvent event) {
        DemoBaseData data = (DemoBaseData) event
                .getSource()
                .getSource();
        System.out.println("监听到生成待办事件，当前数据状态：" + data.getStatus());
    }
}
