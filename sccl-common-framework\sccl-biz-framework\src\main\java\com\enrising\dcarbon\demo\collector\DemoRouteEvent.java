package com.enrising.dcarbon.demo.collector;

import com.enrising.dcarbon.collector.CollectedDatasource;
import com.enrising.dcarbon.collector.RouteEvent;

import java.util.List;

/**
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-15 17:02
 * @email <EMAIL>
 */
public class DemoRouteEvent extends RouteEvent {
    public DemoRouteEvent(List<CollectedDatasource> source) {
        super(source);
    }

    public DemoRouteEvent(List<CollectedDatasource> source, Class<? extends CollectedDatasource> publishType) {
        super(source, publishType);
    }
}
