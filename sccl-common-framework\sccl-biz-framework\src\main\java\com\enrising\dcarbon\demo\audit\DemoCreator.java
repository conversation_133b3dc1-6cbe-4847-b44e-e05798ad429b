package com.enrising.dcarbon.demo.audit;


import com.enrising.dcarbon.audit.Auditable;
import com.enrising.dcarbon.audit.RefereeDatasource;
import com.enrising.dcarbon.audit.AbstractReferee;
import com.enrising.dcarbon.audit.AbstractRefereeDatasourceCreator;
import com.enrising.dcarbon.bean.SpringUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 演示数据源创建者
 *
 * <AUTHOR>
 * @Date 2022/11/11 11:28
 * @Email <EMAIL>
 */
public class DemoCreator extends AbstractRefereeDatasourceCreator {
    @Override
    public Map<Class<? extends RefereeDatasource>, List<RefereeDatasource>> collect(Auditable auditable) {
        //从Spring上下文取得mapper
        DemoMapper mapper = SpringUtil.getBean(DemoMapper.class);
        //可能上下文没有这个mapper
        if (mapper == null) {
            return null;
        }
        Map<Class<? extends RefereeDatasource>, List<RefereeDatasource>> datasource = new HashMap<>();
        //可以添加多种不同类型的评判数据
        List<MssAccountbill> accountbills = new ArrayList<>();
        //强转对象，注意强转前应该判断类型
        if (auditable instanceof MssAccountbill) {
            datasource.put(DemoDatasource.class, new ArrayList<>(mapper.select((MssAccountbill) auditable)));
            datasource.put(OtherDemoDatasource.class, new ArrayList<>(mapper.selectOther((MssAccountbill) auditable)));
        }
        return datasource;
    }

    @Override
    public AbstractReferee getReferee() {
        return new DemoReferee();
    }
}
