package com.sccl.common.utils;

import com.sccl.common.lang.StringUtils;
import lombok.extern.slf4j.Slf4j;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import net.sf.jasperreports.engine.design.JasperDesign;
import net.sf.jasperreports.engine.export.JRPdfExporter;
import net.sf.jasperreports.engine.export.JRPdfExporterParameter;
import net.sf.jasperreports.engine.util.JRSaver;
import net.sf.jasperreports.engine.xml.JRXmlLoader;
import org.apache.commons.io.FileUtils;
import org.springframework.core.io.ClassPathResource;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.*;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 公共打印类
 *
 * <AUTHOR>
 */
@Slf4j
public class PrintUtil {

    /**
     * 打印方法(有预览）
     *
     * @param list         打印数据
     * @param templateName 打印模板名称
     * @param request
     * @param response
     */
    public static void print(List<?> list, String templateName, HttpServletRequest request, HttpServletResponse response) {
        printNow(list,templateName,request,response,"2");
    }

    /**
     * 打印方法(无预览）
     * @param list         打印数据
     * @param templateName 打印模板名称
     * @param request
     * @param response
     * @param lx=1
     */
    public static void printNow(List<?> list, String templateName, HttpServletRequest request, HttpServletResponse response,String lx) {
        try {
            ServletOutputStream out = getServletOutputStream(request, response);

            // 获取打印资源
            JasperPrint jasperPrint = getJasperPrint(templateName, request, new JRBeanCollectionDataSource(list));
            // 使用JRPdfExproter导出器导出pdf
            JRPdfExporter exporter = new JRPdfExporter();
            // 设置JasperPrintList
            if (jasperPrint == null) {
                throw new Exception("编译后的模板不存");
            }
            exporter.setParameter(JRPdfExporterParameter.JASPER_PRINT, jasperPrint);
            // 设置导出
            exporter.setParameter(JRPdfExporterParameter.OUTPUT_STREAM, out);

            //无预览画面
            if("1".equals(lx)){
                exporter.setParameter(JRPdfExporterParameter.PDF_JAVASCRIPT, "this.print();");
            }

            exporter.exportReport();

        } catch (Exception e) {
            log.error("异常",e);
        }
    }

    /**
     * 打印方法
     *
     * @param list         打印数据
     * @param templateName 打印模板名称
     * @param image 图片
     * @param request
     * @param response
     */
    public static void print(List<?> list, Image image, String templateName, HttpServletRequest request, HttpServletResponse response) {
        try {
            ServletOutputStream out = getServletOutputStream(request, response);

            // 获取打印资源
            JasperPrint jasperPrint = getJasperPrint(templateName,new Image[] {image}, request, new JRBeanCollectionDataSource(list));
            // 使用JRPdfExproter导出器导出pdf
            JRPdfExporter exporter = new JRPdfExporter();
            // 设置JasperPrintList
            if (jasperPrint == null) {
                throw new Exception("编译后的模板不存在");
            }
            exporter.setParameter(JRExporterParameter.JASPER_PRINT, jasperPrint);
            // 设置导出
            exporter.setParameter(JRExporterParameter.OUTPUT_STREAM, out);
            exporter.exportReport();

        } catch (Exception e) {
            log.error("异常",e);
        }
    }
    
    /**
     * 打印方法，多个image输出
     *
     * @param list         打印数据
     * @param templateName 打印模板名称
     * @param image 图片
     * @param request
     * @param response
     */
    public static void print(List<?> list, Image[] image, String templateName, HttpServletRequest request, HttpServletResponse response) {
        try {
            ServletOutputStream out = getServletOutputStream(request, response);
            // 获取打印资源
            JasperPrint jasperPrint = getJasperPrint(templateName,image, request, new JRBeanCollectionDataSource(list));
            // 使用JRPdfExproter导出器导出pdf
            JRPdfExporter exporter = new JRPdfExporter();
            // 设置JasperPrintList
            if (jasperPrint == null) {
                throw new Exception("编译后的模板不存在");
            }
            exporter.setParameter(JRExporterParameter.JASPER_PRINT, jasperPrint);
            // 设置导出
            exporter.setParameter(JRExporterParameter.OUTPUT_STREAM, out);
            exporter.exportReport();

        } catch (Exception e) {
            log.error("异常",e);
        }
    }

    /**
     * 打印方法
     *
     * @param allList         打印数据
     * @param templateName 打印模板名称
     * @param request
     * @param response
     */
    public static void printList(List<List<?>> allList, String templateName, HttpServletRequest request, HttpServletResponse response) {
        try {
            ServletOutputStream out = getServletOutputStream(request, response);
            List<JasperPrint> jasperPrintList = new ArrayList<JasperPrint>();

            for (List list:allList) {
                JRDataSource dataSource = new JRBeanCollectionDataSource(list);
                JasperPrint jasperPrint = getJasperPrint(templateName, request, dataSource);
                jasperPrintList.add(jasperPrint);
            }

            // 使用JRPdfExproter导出器导出pdf
            JRPdfExporter exporter = new JRPdfExporter();
            // 设置JasperPrintList
            if (jasperPrintList == null || jasperPrintList.size() == 0) {
                throw new Exception("编译后的模板不存");
            }
            exporter.setParameter(JRPdfExporterParameter.JASPER_PRINT_LIST, jasperPrintList);
            // 设置导出
            exporter.setParameter(JRPdfExporterParameter.OUTPUT_STREAM, out);
            exporter.exportReport();

        } catch (Exception e) {
            log.error("异常",e);
        }
    }


    /**
     * 打印方法
     *
     * @param allList         打印数据
     * @param templateName 打印模板名称
     * @param request
     * @param response
     * @param rightShift:向右偏移,单位cm,1位小数
     * @param bottomShift:向下偏移,单位cm,1位小数
     */
    public static void printList(List<List<?>> allList, String templateName, HttpServletRequest request, HttpServletResponse response,String rightShift,String bottomShift ) {
        try {
            ServletOutputStream out = getServletOutputStream(request, response);
            List<JasperPrint> jasperPrintList = new ArrayList<JasperPrint>();

            for (List list:allList) {
                JRDataSource dataSource = new JRBeanCollectionDataSource(list);
                JasperPrint jasperPrint = getJasperPrint(templateName, request, dataSource,rightShift, bottomShift);
                jasperPrintList.add(jasperPrint);
            }

            // 使用JRPdfExproter导出器导出pdf
            JRPdfExporter exporter = new JRPdfExporter();
            // 设置JasperPrintList
            if (jasperPrintList == null || jasperPrintList.size() == 0) {
                throw new Exception("编译后的模板不存");
            }
            exporter.setParameter(JRPdfExporterParameter.JASPER_PRINT_LIST, jasperPrintList);
            // 设置导出
            exporter.setParameter(JRPdfExporterParameter.OUTPUT_STREAM, out);
            exporter.exportReport();

        } catch (Exception e) {
            log.error("异常",e);
        }
    }



    /**
     * 打印方法 打印指定列
     *
     * @param list         打印数据
     * @param templateName 打印模板名称
     * @param request
     * @param response
     */
    public static void print(List<?> list, String templateName, HttpServletRequest request, HttpServletResponse response,String columns) {
        try {
            ServletOutputStream out = getServletOutputStream(request, response);

            // 获取打印资源
            JasperPrint jasperPrint = getJasperPrint(templateName, request, new JRBeanCollectionDataSource(list),columns);

            // 使用JRPdfExproter导出器导出pdf
            JRPdfExporter exporter = new JRPdfExporter();
            // 设置JasperPrintList
            if (jasperPrint == null) {
                throw new Exception("编译后的模板不存");
            }
            exporter.setParameter(JRPdfExporterParameter.JASPER_PRINT, jasperPrint);
            // 设置导出
            exporter.setParameter(JRPdfExporterParameter.OUTPUT_STREAM, out);
            exporter.exportReport();

        } catch (Exception e) {
            log.error("异常",e);
        }
    }


    /**
     * 打印方法 打印指定列
     *
     * @param list         打印数据
     * @param templateName 打印模板名称
     * @param request
     * @param response
     * @param rightShift
     * @param bottomShift
     */
    public static void print(List<?> list, String templateName, HttpServletRequest request, HttpServletResponse response,String columns,String rightShift,String bottomShift) {
        try {
            ServletOutputStream out = getServletOutputStream(request, response);

            // 获取打印资源
            JasperPrint jasperPrint = getJasperPrint(templateName, request, new JRBeanCollectionDataSource(list),columns,  rightShift,  bottomShift);

            // 使用JRPdfExproter导出器导出pdf
            JRPdfExporter exporter = new JRPdfExporter();

            // 设置JasperPrintList
            if (jasperPrint == null) {
                throw new Exception("编译后的模板不存");
            }
            exporter.setParameter(JRPdfExporterParameter.JASPER_PRINT, jasperPrint);
            // 设置导出
            exporter.setParameter(JRPdfExporterParameter.OUTPUT_STREAM, out);
            exporter.setParameter(JRPdfExporterParameter.PDF_JAVASCRIPT, "this.print();");

            exporter.exportReport();

        } catch (Exception e) {
            log.error("异常",e);
        }
    }



    // 获取打对象
    private static JasperPrint getJasperPrint(String templateName, HttpServletRequest request, JRDataSource dataSource) throws Exception {
        // 获取报表所在路径
        String realPath = RequestUtils.getRealPath(request);
        String fileName = null;
        //获取模板地址
        fileName = "template/" + templateName;

        // 为模板准备路径
        String root_path = realPath;
        Map<String, Object> parameters = new HashMap<String, Object>();
        //设置子模板路径che-tomcat-7.0.57-windows-x86\apache-tomcat-7.0.57\webapps\JKGL2\template\testSubreportSub.jasper
        parameters.put("SUBREPORT_DIR", root_path + "template/");
        parameters.put("SUBREPORT_DIR2", root_path + "template/");
        // 2.编译模板文件
        JasperPrint jasperPrint = new JasperPrint();// 装jasperPrinter对象
        // (1) 加载报表设计模板文件
        JasperDesign jasperDesign = null;
        File jRXmlRile = null;
        ClassPathResource res;
        try {
            res = new ClassPathResource(fileName);
            jRXmlRile = new File(root_path + fileName);
            FileUtils.copyInputStreamToFile(res.getInputStream(), jRXmlRile);//COPY模板文件到临时目录
            jasperDesign = JRXmlLoader.load(jRXmlRile.getPath());
        } catch (Exception e) {
            throw e;
        }

        // (2) 编译报表模板
        JasperReport jasperReport = null;
        try {
            jasperReport = JasperCompileManager.compileReport(jasperDesign);
        } catch (Exception e) {
            throw e;
        }
        // (3) 填充报表
        try {
            jasperPrint = JasperFillManager.fillReport(jasperReport,
                    parameters, dataSource);
        } catch (Exception e) {
            log.error("填充报表失败",e);
            throw new Exception("填充报表失败");
        }

        // (4) 把经过编译填充的模板保存为文件
        try {
            JRSaver.saveObject(jasperPrint, jRXmlRile.getPath() + ".jsper");
        } catch (Exception e) {
            throw new Exception("编译模板保存失败");
        }
        return jasperPrint;
    }

    // 获取指定列打对象
    private static JasperPrint getJasperPrint(String templateName, HttpServletRequest request, JRDataSource dataSource,String columns) throws Exception {
        // 获取报表所在路径
        String realPath = RequestUtils.getRealPath(request);
        String fileName = null;
        //获取模板地址
        fileName = "template/" + templateName;

        // 为模板准备路径
        String root_path = realPath;
        Map<String, Object> parameters = new HashMap<String, Object>();
        //设置子模板路径che-tomcat-7.0.57-windows-x86\apache-tomcat-7.0.57\webapps\JKGL2\template\testSubreportSub.jasper
        parameters.put("SUBREPORT_DIR", root_path + "template/");
        parameters.put("SUBREPORT_DIR2", root_path + "template/");
        parameters.put("columnIndex", columns);
        // 2.编译模板文件
        JasperPrint jasperPrint = new JasperPrint();// 装jasperPrinter对象
        // (1) 加载报表设计模板文件
        JasperDesign jasperDesign = null;
        File jRXmlRile = null;
        ClassPathResource res;
        try {
            res = new ClassPathResource(fileName);
            jRXmlRile = new File(root_path + fileName);
            FileUtils.copyInputStreamToFile(res.getInputStream(), jRXmlRile);//COPY模板文件到临时目录
            jasperDesign = JRXmlLoader.load(jRXmlRile.getPath());
        } catch (Exception e) {
            throw e;
        }

        // (2) 编译报表模板
        JasperReport jasperReport = null;
        try {
            jasperReport = JasperCompileManager.compileReport(jasperDesign);
        } catch (Exception e) {
            throw e;
        }
        // (3) 填充报表
        try {
            jasperPrint = JasperFillManager.fillReport(jasperReport,
                    parameters, dataSource);
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("填充报表失败");
        }

        // (4) 把经过编译填充的模板保存为文件
        try {
            JRSaver.saveObject(jasperPrint, jRXmlRile.getPath() + ".jsper");
        } catch (Exception e) {
            throw new Exception("编译模板保存失败");
        }
        return jasperPrint;
    }

    // 获取打印对象
    private static JasperPrint getJasperPrint(String templateName,Image[] image, HttpServletRequest request, JRDataSource dataSource) throws Exception {
        // 获取报表所在路径
        String realPath = RequestUtils.getRealPath(request);
        String fileName = null;
        //获取模板地址
        fileName = "template/" + templateName;

        // 为模板准备路径
        String root_path = realPath;
        Map<String, Object> parameters = new HashMap<String, Object>();
        //设置子模板路径che-tomcat-7.0.57-windows-x86\apache-tomcat-7.0.57\webapps\JKGL2\template\testSubreportSub.jasper
        parameters.put("SUBREPORT_DIR", root_path + "template/");
        parameters.put("SUBREPORT_DIR2", root_path + "template/");
		if (image != null && image.length > 0) {
			for (int i = 0; i < image.length; i++) {
				if (image[i] != null) {
					if (i == 0) {
						parameters.put("image", image[i]);
					} else {
						parameters.put("image" + i, image[i]);
					}
				}
			}
		}
        // 2.编译模板文件
        JasperPrint jasperPrint = new JasperPrint();// 装jasperPrinter对象
        // (1) 加载报表设计模板文件
        JasperDesign jasperDesign = null;
        File jRXmlRile = null;
        ClassPathResource res;
        try {
            res = new ClassPathResource(fileName);
            jRXmlRile = new File(root_path + fileName);
            FileUtils.copyInputStreamToFile(res.getInputStream(), jRXmlRile);//COPY模板文件到临时目录
            jasperDesign = JRXmlLoader.load(jRXmlRile.getPath());
        } catch (Exception e) {
            throw e;
        }

        // (2) 编译报表模板
        JasperReport jasperReport = null;
        try {
            jasperReport = JasperCompileManager.compileReport(jasperDesign);
        } catch (Exception e) {
            throw e;
        }
        // (3) 填充报表
        try {
            jasperPrint = JasperFillManager.fillReport(jasperReport,
                    parameters, dataSource);
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("填充报表失败");
        }

        // (4) 把经过编译填充的模板保存为文件
        try {
            JRSaver.saveObject(jasperPrint, jRXmlRile.getPath() + ".jsper");
        } catch (Exception e) {
            throw new Exception("编译模板保存失败");
        }
        return jasperPrint;
    }

    // 获取指定列打对象
    private static JasperPrint getJasperPrint(String templateName, HttpServletRequest request, JRDataSource dataSource,String columns,String rightShift,String bottomShift) throws Exception {
        //厘米换算率
        double rateCm=8;
        // 获取报表所在路径
        String realPath = RequestUtils.getRealPath(request);
        String fileName = null;
        //获取模板地址
        fileName = "template/" + templateName;

        // 为模板准备路径
        String root_path = realPath;
        Map<String, Object> parameters = new HashMap<String, Object>();
        //设置子模板路径che-tomcat-7.0.57-windows-x86\apache-tomcat-7.0.57\webapps\JKGL2\template\testSubreportSub.jasper
        parameters.put("SUBREPORT_DIR", root_path + "template/");
        parameters.put("SUBREPORT_DIR2", root_path + "template/");
        parameters.put("columnIndex", columns);
        // 2.编译模板文件
        JasperPrint jasperPrint = new JasperPrint();// 装jasperPrinter对象
        // (1) 加载报表设计模板文件
        JasperDesign jasperDesign = null;
        File jRXmlRile = null;
        ClassPathResource res;
        try {
            res = new ClassPathResource(fileName);
            jRXmlRile = new File(root_path + fileName);
            FileUtils.copyInputStreamToFile(res.getInputStream(), jRXmlRile);//COPY模板文件到临时目录
            jasperDesign = JRXmlLoader.load(jRXmlRile.getPath());
            if(StringUtils.isNotBlank(rightShift)){
                jasperDesign.setLeftMargin( jasperDesign.getLeftMargin()+(int)Math.round(rateCm*Double.parseDouble(rightShift)));
                jasperDesign.setRightMargin( jasperDesign.getRightMargin()-(int)Math.round(rateCm*Double.parseDouble(rightShift)));
            }
            if(StringUtils.isNotBlank(bottomShift)){
                jasperDesign.setTopMargin( jasperDesign.getTopMargin()+(int)Math.round(rateCm*Double.parseDouble(bottomShift)));
                jasperDesign.setBottomMargin( jasperDesign.getBottomMargin()-(int)Math.round(rateCm*Double.parseDouble(bottomShift)));
            }
        } catch (Exception e) {
            throw e;
        }

        // (2) 编译报表模板
        JasperReport jasperReport = null;
        try {
            jasperReport = JasperCompileManager.compileReport(jasperDesign);
        } catch (Exception e) {
            throw e;
        }
        // (3) 填充报表
        try {
            jasperPrint = JasperFillManager.fillReport(jasperReport,
                    parameters, dataSource);
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("填充报表失败");
        }

        // (4) 把经过编译填充的模板保存为文件
        try {
            JRSaver.saveObject(jasperPrint, jRXmlRile.getPath() + ".jsper");
        } catch (Exception e) {
            throw new Exception("编译模板保存失败");
        }
        return jasperPrint;
    }


    // 获取打对象

    /**
     *
     * @param templateName
     * @param request
     * @param dataSource
     * @param rightShift:向右偏移,单位cm,1位小数
     * @param bottomShift:向下偏移,单位cm,1位小数
     * @return
     * @throws Exception
     */
    private static JasperPrint getJasperPrint(String templateName, HttpServletRequest request, JRDataSource dataSource,String rightShift,String bottomShift) throws Exception {
        // 获取报表所在路径
        String realPath = RequestUtils.getRealPath(request);
        String fileName = null;
        //获取模板地址
        fileName = "template/" + templateName;
        //厘米换算率
        double rateCm=8;
        // 为模板准备路径
        String root_path = realPath;
        Map<String, Object> parameters = new HashMap<String, Object>();
        //设置子模板路径che-tomcat-7.0.57-windows-x86\apache-tomcat-7.0.57\webapps\JKGL2\template\testSubreportSub.jasper
        parameters.put("SUBREPORT_DIR", root_path + "template/");
        parameters.put("SUBREPORT_DIR2", root_path + "template/");
        // 2.编译模板文件
        JasperPrint jasperPrint = new JasperPrint();// 装jasperPrinter对象
        // (1) 加载报表设计模板文件
        JasperDesign jasperDesign = null;
        File jRXmlRile = null;
        ClassPathResource res;
        try {
            res = new ClassPathResource(fileName);
            jRXmlRile = new File(root_path + fileName);
            FileUtils.copyInputStreamToFile(res.getInputStream(), jRXmlRile);//COPY模板文件到临时目录
            jasperDesign = JRXmlLoader.load(jRXmlRile.getPath());
            if(StringUtils.isNotBlank(rightShift)){
                jasperDesign.setLeftMargin( jasperDesign.getLeftMargin()+(int)Math.round(rateCm*Double.parseDouble(rightShift)));
                jasperDesign.setRightMargin( jasperDesign.getRightMargin()-(int)Math.round(rateCm*Double.parseDouble(rightShift)));
            }
            if(StringUtils.isNotBlank(bottomShift)){
                jasperDesign.setTopMargin( jasperDesign.getTopMargin()+(int)Math.round(rateCm*Double.parseDouble(bottomShift)));
                jasperDesign.setBottomMargin( jasperDesign.getBottomMargin()-(int)Math.round(rateCm*Double.parseDouble(bottomShift)));
            }

        } catch (Exception e) {
            throw e;
        }

        // (2) 编译报表模板
        JasperReport jasperReport = null;
        try {
            jasperReport = JasperCompileManager.compileReport(jasperDesign);
        } catch (Exception e) {
            throw e;
        }
        // (3) 填充报表
        try {
            jasperPrint = JasperFillManager.fillReport(jasperReport,
                    parameters, dataSource);
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("填充报表失败");
        }

        // (4) 把经过编译填充的模板保存为文件
        try {
            JRSaver.saveObject(jasperPrint, jRXmlRile.getPath() + ".jsper");
        } catch (Exception e) {
            throw new Exception("编译模板保存失败");
        }
        return jasperPrint;
    }



    // 设定response
    private static ServletOutputStream getServletOutputStream(HttpServletRequest request, HttpServletResponse response) throws IOException {
        ServletOutputStream out = response.getOutputStream();

        response.setCharacterEncoding("utf-8");
        request.setCharacterEncoding("utf-8");
        response.setDateHeader("expires", -10);

        response.setContentType("application/pdf; charset=UTF-8");
        // 以下两句为取消在本地的缓存
        response.setHeader("Cache-Control", "no-cache");
        response.setHeader("Pragma", "no-cache");
        return out;
    }

}
