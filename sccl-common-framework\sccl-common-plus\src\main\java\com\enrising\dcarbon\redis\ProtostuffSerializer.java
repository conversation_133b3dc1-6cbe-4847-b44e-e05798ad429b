package com.enrising.dcarbon.redis;

import com.enrising.dcarbon.lang.Serializer;
import com.enrising.dcarbon.message.MessageManager;

/**
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-07-13 14:27
 * @email <EMAIL>
 */
public class ProtostuffSerializer<T> implements Serializer<byte[], T> {
    @Override
    public byte[] serialize(T source) {
        return MessageManager.getMessage(source, MessageManager.PROTO_STUFF_SERIALIZER);
    }
}
