## 代码风格
    1. Controller 没必要每个接口都写 try-catch，项目已有全局异常处理
    2. 不要使用魔法值，多个固定类型需要创建枚举类

## 增删改查
接收前端参数使用DTO对象
返回前端数据使用VO对象

### 增
    1. 新建DTO对象用于接收前端参数
    2. 使用mybatis-plus的功能插入数据表

### 查询
    主要有两个接口：一个是查询列表，使用startPage()进行分页;一个是查询详情。
    1. 返回对象新建VO，不要和数据表对应的实体类混用
    2. 统一使用mybatis xml的形式，接收接口对象的参数，判断非空非null的属性进行条件查询
### 删除
    1. 支持单个删除和批量删除
    2. 使用mybatis-plus的删除
### 修改
    1. 使用DTO对象接受参数
    2. 使用mybatis-plus的功能更新数据表

## 工具包
    尽量使用已有的工具包：lombok、hutool、spring框架
    1.对象拷贝
        优先使用spring框架的BeanUtils.copyProperties();
        import org.springframework.beans.BeanUtils;
        对象拷贝时，优先检查对象属性是否一致，如果不一致，使用BeanUtils.copyProperties()后，再单独赋值
    2.非空判断
        优先使用Hutool工具包
        StrUtil.isNotBlank()
        StrUtil.isBlank()
        ObjUtil.isEmpty()
        ObjUtil.isNotEmpty()
        CollUtil.isEmpty()
        CollUtil.isNotEmpty()



