package com.enrising.dcarbon.demo.statedemo;

import com.enrising.dcarbon.observer.EventManger;
import com.enrising.dcarbon.state.AbstractObserverState;
import com.enrising.dcarbon.state.StateEventFactory;

/**
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-17 9:57
 * @email <EMAIL>
 */
public class FinishedState extends AbstractObserverState {
    private DemoBaseEntity entity;

    public FinishedState(EventManger eventManger) {
        super(eventManger);
    }

    public FinishedState() {
    }

    public FinishedState setEntity(DemoBaseEntity entity) {
        this.entity = entity;
        return this;
    }

    @Override
    public StateEventFactory stateEventFactory() {
        return FinishedEvent::new;
    }

    @Override
    public Object getSource() {
        return null;
    }
}
