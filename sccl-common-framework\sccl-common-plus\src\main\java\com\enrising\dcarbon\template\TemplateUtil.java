package com.enrising.dcarbon.template;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.Velocity;
import org.apache.velocity.app.VelocityEngine;
import org.apache.velocity.Template;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Properties;

/**
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-30 17:38
 * @email <EMAIL>
 */
@Slf4j
public class TemplateUtil {
    private static final VelocityEngine engine = new VelocityEngine();

    static {
        Properties p = new Properties();
        p.setProperty("file.resource.loader.class", "org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader");
        p.setProperty(Velocity.FILE_RESOURCE_LOADER_PATH, "");
        p.setProperty(Velocity.ENCODING_DEFAULT, StandardCharsets.UTF_8.name());
        p.setProperty(Velocity.INPUT_ENCODING, StandardCharsets.UTF_8.name());
        p.setProperty("file.resource.loader.unicode", "true");
        engine.init(p);
    }

    private static Template getTemplate(String resource) {
        return engine.getTemplate(resource, "UTF-8");
    }

    private static File getDirFile(String dirPath) {
        return new File(dirPath);
    }

    private static File getWriteFile(File dirFile, String fileName) {
        return new File(dirFile.getPath() + "/" + fileName);
    }

    public static void execute(Map<String, Object> metaData, String templatePath, Writer writer) {
        try {
            VelocityContext context = new VelocityContext(metaData);
            Template template = getTemplate(templatePath);
            template.merge(context, writer);
        } finally {
            IOUtils.closeQuietly(writer);
        }
    }

    public static void execute(Map<String, Object> metaData, String templatePath, String dirPath, String fileName, boolean isCheckExist) {
        File dir = getDirFile(dirPath);
        if (!dir.exists() && !dir.mkdirs()) {
            throw new RuntimeException("创建目录：" + dirPath + "失败");
        }
        if (!dir.isDirectory()) {
            throw new IllegalArgumentException(dirPath + "不是一个目录路径");
        }
        File file = getWriteFile(dir, fileName);
        if (isCheckExist && file.exists()) {
            log.warn("文件{}已存在，将不会重新生成，如果需要覆盖请配置", fileName);
            return;
        }
        try (FileOutputStream fileOutputStream = new FileOutputStream(file)) {
            OutputStreamWriter outputStreamWriter = new OutputStreamWriter(fileOutputStream, StandardCharsets.UTF_8);
            execute(metaData, templatePath, new BufferedWriter(outputStreamWriter));
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
