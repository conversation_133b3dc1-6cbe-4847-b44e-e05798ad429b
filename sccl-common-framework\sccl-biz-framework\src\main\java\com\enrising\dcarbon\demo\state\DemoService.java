package com.enrising.dcarbon.demo.state;

import com.enrising.dcarbon.demo.state.events.AgendaEvent;
import com.enrising.dcarbon.demo.state.events.DraftEvent;
import com.enrising.dcarbon.demo.state.events.FinishedEvent;
import com.enrising.dcarbon.demo.state.events.ProcessEvent;
import com.enrising.dcarbon.demo.state.listener.*;
import com.enrising.dcarbon.demo.state.states.AgendaState;
import com.enrising.dcarbon.demo.state.states.DraftState;
import com.enrising.dcarbon.demo.state.states.FinishedState;
import com.enrising.dcarbon.demo.state.states.ProcessState;
import com.enrising.dcarbon.observer.EventManger;
import com.enrising.dcarbon.state.ObserverStateMachine;

/**
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-10 10:36
 * @email <EMAIL>
 */
public class DemoService {
    private static final DemoMapper MAPPER = new DemoMapper();
    private static final ObserverStateMachine STATE_MACHINE = new ObserverStateMachine();
    private static final EventManger EVENT_MANGER = EventManagerInstance.EVENT_MANGER;

    static {
        //将事件监听器添加进事件管理器
        EVENT_MANGER.addListener(AgendaEvent.class, new AgendaListener());
        EVENT_MANGER.addListener(DraftEvent.class, new DraftListener());
        EVENT_MANGER.addListener(FinishedEvent.class, new FinishedListener());
        EVENT_MANGER.addListener(ProcessEvent.class, new ProcessListener());
        //同一个事件可以添加多个监听器
        EVENT_MANGER.addListener(DraftEvent.class, new DraftListener2());
    }

    public void add(DemoBaseData data) {
        MAPPER.insert(data);
        MAPPER.updateStatus(data.getId(), 0);
        //使用状态机进行状态切换，这里使用的是基于观察者模式的状态机，因此需要提供事件管理器
        STATE_MACHINE.stateChange(new DraftState(EVENT_MANGER, data));
    }

    public void createAgenda(long id) {
        if (!STATE_MACHINE.isCurrentStateMatch(DraftState.class)) {
            System.out.println("当前初始状态不是草稿状态，不能创建待办");
            return;
        }
        MAPPER.updateStatus(id, 1);
        STATE_MACHINE.stateChange(new AgendaState(EVENT_MANGER, MAPPER.query(id)));
    }

    public void check(long id) {
        if (!STATE_MACHINE.isCurrentStateMatch(AgendaState.class)) {
            System.out.println("当前初始状态不是待办状态，不能审批");
            return;
        }
        MAPPER.updateStatus(id, 2);
        STATE_MACHINE.stateChange(new ProcessState(EVENT_MANGER, MAPPER.query(id)));
    }

    public void confirm(long id) {
        if (!STATE_MACHINE.isCurrentStateMatch(ProcessState.class)) {
            System.out.println("当前初始状态不是审批状态，不能确认");
            return;
        }
        MAPPER.updateStatus(id, 3);
        STATE_MACHINE.stateChange(new FinishedState(EVENT_MANGER, MAPPER.query(id)));
    }


}
