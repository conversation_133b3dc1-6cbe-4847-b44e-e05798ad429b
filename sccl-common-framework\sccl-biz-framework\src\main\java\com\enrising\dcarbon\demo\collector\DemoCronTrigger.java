package com.enrising.dcarbon.demo.collector;

import com.enrising.dcarbon.collector.AbstractCollectorTemplate;
import com.enrising.dcarbon.collector.CronCollectorTrigger;

/**
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-12 14:12
 * @email <EMAIL>
 */
public class DemoCronTrigger extends CronCollectorTrigger {
    public DemoCronTrigger(AbstractCollectorTemplate template, String cron) {
        super(template, cron);
    }

    @Override
    public void execute() {
        executeActiveTemplate();
    }
}
