package com.sccl.common.utils;

import com.alibaba.fastjson.JSON;
import com.sccl.common.lang.StringUtils;
import com.sccl.common.support.Convert;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.BufferedReader;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * Servlet工具类
 *
 * <AUTHOR>
 */
public class ServletUtils {
    /**
     * 获取String参数
     */
    public static String getParameter(String name) {
        return getRequest().getParameter(name);
    }

    /**
     * 获取String参数
     */
    public static String getParameter(String name, String defaultValue) {
        return Convert.toStr(getRequest().getParameter(name), defaultValue);
    }

    /**
     * 获取Integer参数
     */
    public static Integer getParameterToInt(String name) {
        return Convert.toInt(getRequest().getParameter(name));
    }

    @SuppressWarnings("unchecked")
    public static Object getPostParameter(String name) {

        Map<String, Object> params = new HashMap<String, Object>();
        BufferedReader br;
        try {
            br = getRequest().getReader();
            String str, wholeStr = "";
            while ((str = br.readLine()) != null) {
                wholeStr += str;
            }
            if (StringUtils.isNotEmpty(wholeStr)) {
                params = JSON.parseObject(wholeStr, Map.class);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return params.get(name);
    }

    /**
     * 获取Integer参数
     */
    public static Integer getParameterToInt(String name, Integer defaultValue) {
        return Convert.toInt(getRequest().getParameter(name), defaultValue);
    }

    /**
     * 获取request
     */
    public static HttpServletRequest getRequest() {
        return getRequestAttributes().getRequest();
    }

    /**
     * 获取response
     */
    public static HttpServletResponse getResponse() {
        return getRequestAttributes().getResponse();
    }

    /**
     * 获取session
     */
    public static HttpSession getSession() {
        return getRequest().getSession();
    }

    public static ServletRequestAttributes getRequestAttributes() {
        RequestAttributes attributes = RequestContextHolder.getRequestAttributes();
        return (ServletRequestAttributes) attributes;
    }

    /**
     * 将字符串渲染到客户端
     *
     * @param response 渲染对象
     * @param string   待渲染的字符串
     * @return null
     */
    public static String renderString(HttpServletResponse response, String string) {
        try {
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            response
                    .getWriter()
                    .print(string);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String getRequestUrl() {
        return getRequest().getRequestURI();
    }

    /**
     * 获取当前请求的IP地址
     * 优化处理多层代理的情况，避免重复IP导致限流问题
     *
     * @return 客户端真实IP地址
     * <AUTHOR> Yongxiang
     * @date 2022/1/5 9:55
     */
    public static String getRequestIp() {
        HttpServletRequest request = getRequest();
        return IpUtils.getIpAddr(request);
    }

    public String getTerminalInfo() {
        // 获取 User-Agent 头信息，通常包含设备的信息
        HttpServletRequest request = getRequest();

        String userAgent = request.getHeader("User-Agent");

        // 在实际场景中，你可能需要根据具体的 User-Agent 解析出终端信息
        String terminalInfo = parseUserAgent(userAgent);

        return terminalInfo;
    }

    private String parseUserAgent(String userAgent) {

        return "TerminalInfoPlaceholder";
    }

    /**
     * 是否是Ajax异步请求
     *
     * @param request
     */
    public static boolean isAjaxRequest(HttpServletRequest request) {

        String accept = request.getHeader("accept");
        if (accept != null && accept.indexOf("application/json") != -1) {
            return true;
        }

        String xRequestedWith = request.getHeader("X-Requested-With");
        if (xRequestedWith != null && xRequestedWith.indexOf("XMLHttpRequest") != -1) {
            return true;
        }

        String uri = request.getRequestURI();
        if (StringUtils.inStringIgnoreCase(uri, ".json", ".xml")) {
            return true;
        }

        String ajax = request.getParameter("__ajax");
        if (StringUtils.inStringIgnoreCase(ajax, "json", "xml")) {
            return true;
        }

        return false;
    }
}

