package com.enrising.dcarbon.observer;

/**
 * 事件监听器
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-09 13:55
 * @email <EMAIL>
 */
public interface EventListener<E extends Event<?>> {
    void onEvent(E event);

    default String name() {
        return getClass().getSimpleName();
    }

    /**
     * 监听器级别，高级别的监听器会被优先执行
     *
     * @return int
     * <AUTHOR> ^ ▽ ^ *)
     * @date 2023/5/9 16:57
     */
    default int level() {
        return 0;
    }
}
