package com.sccl.common.utils;

import com.google.gson.JsonArray;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.*;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.Map;

public class excelUtil {
    public static String readExcelToJson(String path) {
        Workbook wb = readExcel(path);
        Sheet sheet = null;
        String cellData = null;
        Row row = null;
        LinkedList<Map<String, String>> list = null;

        if (wb != null) {
            String[] col = getAttributeOfExcel(wb);
            if (col == null) {
                return null;
            }
            //用来存放表中数据
            list = new LinkedList<>();
            //获取第一个sheet
            sheet = wb.getSheetAt(0);
            //获取最大行数
            int rownum = sheet.getPhysicalNumberOfRows();
            //获取第一行
            row = sheet.getRow(0);
            //获取最大列数
            int colnum = row.getPhysicalNumberOfCells();
            for (int i = 1; i < rownum; i++) {
                LinkedHashMap<String, String> map = new LinkedHashMap<>();
                row = sheet.getRow(i);
                if (row != null) {
                    for (int j = 0; j < colnum; j++) {
                        cellData = (String) getXCellVal(row.getCell(j));
                        map.put(col[j], cellData);
                    }
                } else {
                    break;
                }
                list.add(map);
            }

        } else {
            return null;
        }
        return JsonUtil.getJson(list);

    }

    public static String readRowsExcel(int start, int end, String path) {
        Workbook wb = readExcel(path);
        Sheet sheet = null;
        String cellData = null;
        Row row = null;
        LinkedList<Map<String, String>> list = null;
        Map<String, Object> map2 = null;
        if (wb != null) {
            String[] col = getAttributeOfExcel(wb);
            if (col == null) {
                return null;
            }
            map2=new HashMap<>();
            //用来存放表中数据
            list = new LinkedList<>();
            //获取第一个sheet
            sheet = wb.getSheetAt(0);
            //获取最大行数
            int rownum = sheet.getPhysicalNumberOfRows();
            //获取第一行
            row = sheet.getRow(0);
            //获取最大列数
            int colnum = row.getPhysicalNumberOfCells();
            for (int i = start; i <= end; i++) {
                LinkedHashMap<String, String> map = new LinkedHashMap<>();
                row = sheet.getRow(i);
                if (row != null) {
                    for (int j = 0; j < colnum; j++) {
                        cellData = (String) getXCellVal(row.getCell(j));
                        map.put(col[j], cellData);
                    }
                } else {
                    break;
                }
                list.add(map);
            }
            map2.put("max",rownum);
            map2.put("data",list);

        } else {
            return null;
        }
        return JsonUtil.getMapJson(map2);
    }


    //读取excel
    public static Workbook readExcel(String filePath) {
        Workbook wb = null;
        if (filePath == null) {
            return null;
        }
        if (!new File(filePath).exists()) {
            return null;
        }
        String extString = filePath.substring(filePath.lastIndexOf("."));
        InputStream is = null;
        try {
            is = new FileInputStream(filePath);
            if (".xls".equals(extString)) {
                return wb = new HSSFWorkbook(is);
            } else if (".xlsx".equals(extString)) {
                return wb = new XSSFWorkbook(is);
            } else {
                return wb = null;
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return wb;
    }


    /**
     * @param wb workbook对象
     * @return 对应的属性数组，默认第一行为属性
     */
    public static String[] getAttributeOfExcel(Workbook wb) {
        String str[] = null;
        if (wb != null) {
            Sheet sheet = wb.getSheetAt(0);
            Row row = sheet.getRow(0);
            if (row != null) {
                //获取最大列数
                int colnum = row.getPhysicalNumberOfCells();
                str = new String[colnum];
                for (int i = 0; i < colnum; i++) {
                    str[i] = getXCellVal(row.getCell(i));
                }
            }
        }
        return str;
    }

    /**
     * 格式化解析Excel表格内容
     *
     * @param cell
     * @return
     */
    private static String getXCellVal(Cell cell) {
        String val;
        SimpleDateFormat fmt = new SimpleDateFormat("yyyy-MM-dd"); //日期格式yyyy-mm-dd
        DecimalFormat df = new DecimalFormat("#.#########");             //数字格式，防止长数字成为科学计数法形式，或者int变为double形式
        switch (cell.getCellType()) {
            case Cell.CELL_TYPE_NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    val = fmt.format(cell.getDateCellValue()); //日期型
                } else {
                    val = df.format(cell.getNumericCellValue()); //数字型
                }
                break;
            case Cell.CELL_TYPE_STRING: //文本类型
                val = cell.getStringCellValue();
                break;
            case Cell.CELL_TYPE_BOOLEAN: //布尔型
                val = String.valueOf(cell.getBooleanCellValue());
                break;
            case Cell.CELL_TYPE_BLANK: //空白
                val = cell.getStringCellValue();
                break;
            case Cell.CELL_TYPE_ERROR: //错误
                val = "错误";
                break;
            case Cell.CELL_TYPE_FORMULA: //公式
                try {
                    val = String.valueOf(cell.getStringCellValue());
                } catch (IllegalStateException e) {
                    val = String.valueOf(cell.getNumericCellValue());
                }
                break;
            default:
                val = cell.getRichStringCellValue() == null ? null : cell.getRichStringCellValue().toString();
        }
        return val;
    }

    /**
     * @param sheetname 表名
     * @param att       属性数组
     * @param path      写入路径
     * @param json      写入的json数据
     * @param name      文件名+后缀
     * @return 是否写入成功
     */
    public static boolean WriteJsonArrayToPath(String sheetname, JsonArray att, String path, String json, String name) {
        JsonArray jarr = null;
        int flag = 0;
        if (json != null) {
            jarr = JsonUtil.stringToJsonArray(json);
        } else {
            return false;
        }
        Workbook wb = null;
        //解析名称后缀
        String na = name.substring(name.lastIndexOf("."));
        System.out.println(na);
        if (na.equalsIgnoreCase(".xls")) {
            wb = new HSSFWorkbook();
        } else {
            //对于xlsx文件，使用SXSSF模式，内存最多存在500行
            flag = 1;
            wb = new SXSSFWorkbook(500);
        }

        //创建表
        Sheet sheet = wb.createSheet(sheetname);

        //创建属性行
        //解析一个JSON对象获取keys
        Row row = sheet.createRow(0);
        Map<String, Object> map;
        for (int i = 0; i < att.size(); i++) {
            if(att.get(i)!=null) {
                row.createCell(i).setCellValue(att.get(i).toString());
            }
            else{
                row.createCell(i).setCellValue("");
            }
        }

        //解析所有对象将值填入wb
        Row zrow = null;
        for (int i = 0; i < jarr.size(); i++) {
            map = JsonUtil.jsonObjToMap( jarr.get(i).getAsJsonObject());
            zrow = sheet.createRow(i + 1);
            for (int j = 0; j < att.size(); j++) {
                String val;
                if(att.get(j)!=null&&map!=null&&map.get(att.get(j))!=null) {
                    val = map.get(att.get(j)).toString();
                }
                else{
                    val="NaN";
                }
                zrow.createCell(j).setCellValue(val);
            }

        }

        //将文件写入指定路径
        String rp = path + "/" + name;
        try {
            System.out.println("开始写入文件："+name);
            FileOutputStream outputStream = new FileOutputStream(rp);
            wb.write(outputStream);
            /*如果使用SXSSF类型则清空临时文件*/
            if (flag == 1) {
                ((SXSSFWorkbook) wb).dispose();
            }
            wb.close();
            outputStream.close();
            System.out.println("写入路径" + rp + "成功！");

        } catch (Exception e) {
            System.out.println("写入路径" + rp + "失败！");
            e.printStackTrace();
            return false;
        }
        return true;
    }
}


