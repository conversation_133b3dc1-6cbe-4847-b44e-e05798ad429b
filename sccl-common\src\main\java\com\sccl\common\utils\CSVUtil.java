package com.sccl.common.utils;

import com.opencsv.*;
import com.opencsv.bean.ColumnPositionMappingStrategy;
import com.opencsv.bean.CsvToBean;
import com.opencsv.bean.CsvToBeanBuilder;
import com.opencsv.bean.HeaderColumnNameMappingStrategy;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;

import java.lang.reflect.Method;
import java.util.*;

/**
 * @Description CSV工具类
 * @<PERSON><PERSON> <PERSON>
 * @Date 2021/11/29 11:18
 */
@Setter(AccessLevel.PRIVATE)
@Getter(AccessLevel.PUBLIC)
public class CSVUtil {
    private CSVReader reader;
    private static String coding = "UTF-8";
    private static char separator = ',';
    private static final Logger logger = LoggerFactory.getLogger(CSVUtil.class);


    public static CSVUtil build(InputStream inputStream) {
        if (inputStream == null) {
            return null;
        }
        CSVUtil csvUtil = new CSVUtil();
        try {
            InputStreamReader inputStreamReader = new InputStreamReader(inputStream, coding);
            csvUtil.setReader(new CSVReaderBuilder(inputStreamReader).build());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return csvUtil;
    }

    public static CSVUtil build(InputStream inputStream, char separator) {
        if (inputStream == null) {
            return null;
        }
        CSVUtil csvUtil = new CSVUtil();
        try {
            InputStreamReader inputStreamReader = new InputStreamReader(inputStream, coding);
            csvUtil.setReader(new CSVReaderBuilder(inputStreamReader).build());
            setSeparator(separator);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return csvUtil;
    }

    public static CSVUtil build(InputStream inputStream, char separator, String coding) {
        if (inputStream == null) {
            return null;
        }
        CSVUtil csvUtil = new CSVUtil();
        try {
            InputStreamReader inputStreamReader = new InputStreamReader(inputStream, coding);
            csvUtil.setReader(new CSVReaderBuilder(inputStreamReader).build());
            setSeparator(separator);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return csvUtil;
    }

    private static void setCoding(String coding) {
        CSVUtil.coding = coding;
    }

    private static void setSeparator(char separator) {
        CSVUtil.separator = separator;
    }

    public static String getCoding() {
        return coding;
    }

    public static char getSeparator() {
        return separator;
    }

    /**
     * 基于字段名称映射的CSV转成任意类型的Bean列表
     *
     * @param clazz 要转成的bean类型
     * @return java.util.List<org.apache.poi.ss.formula.functions.T>
     * <AUTHOR> Yongxiang
     * @date 2021/11/29 11:49
     */
    public <T> List<T> csvToBeanByColumnName(Class<T> clazz) {
        if (reader == null) {
            logger.error("reader为null");
            return null;
        }
        CsvToBean<T> csvToBean = null;
        HeaderColumnNameMappingStrategy<T> strategy = new HeaderColumnNameMappingStrategy<>();
        List<T> beans = null;
        try {
            strategy.setType(clazz);
            csvToBean = new CsvToBeanBuilder<T>(reader).withSeparator(separator).withMappingStrategy(strategy).build();
            beans = csvToBean.parse();
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("数据转化失败");
        }
        return beans;
    }

    /**
     * 基于字段索引映射的CSV转成任意类型的Bean列表
     *
     * @param clazz 要转的Bean类型
     * @return java.util.List<T>
     * <AUTHOR> Yongxiang
     * @date 2021/11/30 13:34
     */
    public <T> List<T> csvToBeanByColumnPosition(Class<T> clazz) {
        if (reader == null) {
            logger.error("reader为null");
            return null;
        }
        CsvToBean<T> csvToBean = null;
        ColumnPositionMappingStrategy<T> strategy = new ColumnPositionMappingStrategy<>();
        strategy.setType(clazz);
        List<T> beans = null;
        try {
            strategy.setType(clazz);
            csvToBean = new CsvToBeanBuilder<T>(reader).withSeparator(separator).withMappingStrategy(strategy).build();
            beans = csvToBean.parse();
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("数据转化失败");
        }
        return beans;

    }

    /**
     * 给定列名和属性名的映射map，读取CSV创建Bean
     *
     * @param clazz      要转为的pojo类
     * @param columnMap  [CSV列名:POJO类属性名]
     * @param emptyLines 标题行前的无效行数
     * @return java.util.List<T>
     * <AUTHOR> Yongxiang
     * @date 2021/11/30 14:19
     */
    public <T> List<T> csvToBeanByMap(Class<T> clazz, Map<String, String> columnMap, int emptyLines) {
        if (reader == null) {
            logger.error("reader为null");
            return null;
        }
        if (clazz == null || columnMap == null) {
            logger.error("重要参数为null，csv转Bean失败");
            return null;
        }
        String[] head = readHead(emptyLines);
        List<T> beans = null;
        if (head != null) {
            beans = new LinkedList<>();
            List<String[]> all = readAll(emptyLines + 1);
            //便览所有行
            for (String[] row : all) {
                if (row == null) {
                    continue;
                }
                try {
                    T pojo = clazz.newInstance();
                    for (int i = 0; i < row.length; i++) {
                        //当前元素的title
                        String title = head[i];
                        //当前元素对应pojo类的属性名
                        String attributeName = columnMap.get(title);
                        if (attributeName == null) {
                            logger.error("当前元素：{}的列名：{}无与之对应的pojo类属性名，请检查", row[i], title);
                            continue;
                        }
                        String value = row[i] == null ? "" : row[i];
                        //替换值里面可能出现的制表符
                        value = value.replace("\t", "");
                        Method[] methods = clazz.getDeclaredMethods();
                        String methodName = "set" + attributeName.substring(0, 1).toUpperCase() + attributeName.substring(1);
                        for (Method method : methods) {
                            if (method.getName().equals(methodName)) {
                                method.invoke(pojo, value);
                                break;
                            }
                        }
                    }
                    beans.add(pojo);
                } catch (Exception e) {
                    logger.error("创建{}类实例失败", clazz.getSimpleName());
                    e.printStackTrace();
                }
            }
        } else {
            logger.error("没有读取到head行");
            return null;
        }
        return beans;
    }


    /**
     * 读取CSV的所有内容
     *
     * @param skipLines 要跳过的无效行数
     * @return java.util.List<java.lang.String [ ]>
     * <AUTHOR> Yongxiang
     * @date 2021/12/1 9:56
     */
    public List<String[]> readAll(int skipLines) {
        if (reader == null) {
            logger.error("reader为null");
            return null;
        }
        List<String[]> list = null;
        try {
            reader.skip(skipLines);
            list = reader.readAll();
            logger.debug("共读取到{}行数据", list.size());
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("读取失败");
        }
        return list;
    }

    /**
     * 读取CSV的首行，默认是第一行
     *
     * @return java.lang.String[]
     * <AUTHOR> Yongxiang
     * @date 2021/12/1 9:59
     */
    public String[] readHead() {
        if (reader == null) {
            logger.error("reader为null");
            return null;
        }
        String[] res = null;
        try {
            Iterator<String[]> iterator = reader.iterator();
            boolean head = true;
            while (head && iterator.hasNext()) {
                res = iterator.next();
                head = false;
            }
            logger.debug("共读取到{}列数据", res != null ? res.length : 0);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("读取失败");
        }
        return res;

    }

    /**
     * 读取CSV的首行
     *
     * @param skipLines 首行前的无效行数
     * @return java.lang.String[]
     * <AUTHOR> Yongxiang
     * @date 2021/12/1 10:00
     */
    public String[] readHead(int skipLines) {
        if (reader == null) {
            logger.error("reader为null");
            return null;
        }
        String[] res = null;
        try {
            Iterator<String[]> iterator = reader.iterator();
            boolean head = true;
            int i = 0;
            while ((i++) < skipLines && iterator.hasNext()) {
                iterator.next();
            }
            while (head && iterator.hasNext()) {
                res = iterator.next();
                head = false;
            }
            logger.debug("共读取到{}列数据", res != null ? res.length : 0);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("读取失败");
        }
        return res;

    }


}
