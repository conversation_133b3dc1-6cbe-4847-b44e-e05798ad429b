package com.enrising.dcarbon.demo.collector;

import com.enrising.dcarbon.collector.AbstractCollectorTemplate;
import com.enrising.dcarbon.collector.CyclicallyCollectorTrigger;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-12 14:40
 * @email <EMAIL>
 */
public class DemoCycleTrigger extends CyclicallyCollectorTrigger {
    public DemoCycleTrigger(AbstractCollectorTemplate template, long cycle, TimeUnit unit) {
        super(template, cycle, unit);
    }

    @Override
    public void execute() {
        executeActiveTemplate();
    }
}
