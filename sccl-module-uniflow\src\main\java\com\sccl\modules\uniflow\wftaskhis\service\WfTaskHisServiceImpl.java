package com.sccl.modules.uniflow.wftaskhis.service;

import com.sccl.framework.service.BaseServiceImpl;
import com.sccl.modules.uniflow.wftask.domain.WfTask;
import com.sccl.modules.uniflow.wftaskhis.domain.WfTaskHis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 任务实例(历史记录) 服务层实现
 *
 * <AUTHOR>
 * @date 2019-03-15
 */
@Service
public class WfTaskHisServiceImpl extends BaseServiceImpl<WfTaskHis> implements IWfTaskHisService
{
    private static final Logger logger = LoggerFactory.getLogger(WfTaskHisServiceImpl.class);
    /**
     * 迁移待办到已办表中
     *
     * @param wfTask 待办任务
     */
    @Override
    public void moveTaskToHis(WfTask wfTask) {
        //1、先判断历史表中是否已经在
        Map<String,Object> param = new HashMap<>(2);
        param.put("id", wfTask.getId());
        param.put("shardKey", wfTask.getShardKey());
        logger.info("迁移待办到已办表中");
        List<WfTaskHis> wfTaskHisList= this.selectBy(param);
        //如果存在  则跳过，不存在，则插入
        if(wfTaskHisList == null || wfTaskHisList.isEmpty()) {
            try {
                WfTaskHis wfTaskHis = new WfTaskHis();
                BeanUtils.copyProperties(wfTask,wfTaskHis);
                this.insert(wfTaskHis);
            } catch (org.springframework.dao.DuplicateKeyException e) {
                // 如果发生主键重复异常，说明在并发情况下已经有其他线程插入了相同记录
                // 这种情况下直接忽略，不需要抛出异常
                logger.warn("任务ID: {} 已存在于历史表中，跳过插入操作", wfTask.getId());
            }
        }
    }
}
