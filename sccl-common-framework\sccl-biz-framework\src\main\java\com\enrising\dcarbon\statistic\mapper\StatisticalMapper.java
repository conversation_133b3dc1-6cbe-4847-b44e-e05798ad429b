package com.enrising.dcarbon.statistic.mapper;

import com.enrising.dcarbon.statistic.StatisticalEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 统计指标 数据层
 *
 * <AUTHOR>
 * @date 2022-10-25
 */
@Mapper
public interface StatisticalMapper {
    int deleteByIds(@Param("array") String[] ids);

    int insertList(List<StatisticalEntity> entities);

    List<StatisticalEntity> selectList(StatisticalEntity query);

    Long selectGroupId(@Param("groupName") String groupName);

    int updateAll(@Param("entity") StatisticalEntity model, @Param("ids") List<Long> ids);
}