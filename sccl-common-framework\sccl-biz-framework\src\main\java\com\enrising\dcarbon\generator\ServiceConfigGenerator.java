package com.enrising.dcarbon.generator;

import com.enrising.dcarbon.property.MapParamsBuilder;

import java.util.Map;

/**
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-31 14:43
 * @email <EMAIL>
 */
public class ServiceConfigGenerator extends AbstractTemplateGenerator {
    private final Map<String, Object> metadata;

    public static Builder builder(){
        return new Builder();
    }

    public ServiceConfigGenerator(Map<String, Object> metadata) {
        super("/template/ServiceConfig.java.vm");
        this.metadata = metadata;
    }

    @Override
    public Map<String, Object> metadata() {
        return metadata;
    }

    @Override
    public String dirPath() {
        return metadata.get("path") + "\\" + ((String) metadata.get("parentPackage")).replace(".", "\\") + "\\config";
    }

    @Override
    public void generate(String fileName) {
        super.generate(metadata.get("serviceImplName") + "ServiceConfig.java");
    }

    public static class Builder extends MapParamsBuilder {
        public Builder() {
        }

        public ServiceConfigGenerator build() {
            return new ServiceConfigGenerator(getParams());
        }
    }
}
