package com.enrising.dcarbon.collector;

import com.enrising.dcarbon.observer.Event;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 路由事件
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-12 11:20
 * @email <EMAIL>
 */
public class RouteEvent extends Event<List<CollectedDatasource>> {
    private final List<Class<? extends CollectedDatasource>> publishTypes = new ArrayList<>();

    public RouteEvent(List<CollectedDatasource> source) {
        super(source);
        if (source != null) {
            for (CollectedDatasource data : source) {
                publishTypes.add(data.getClass());
            }
        }
    }

    @SafeVarargs
    public RouteEvent(List<CollectedDatasource> source, Class<? extends CollectedDatasource>... publishTypes) {
        super(source);
        this.publishTypes.addAll(Arrays.asList(publishTypes));
    }

    public boolean withType(Class<? extends CollectedDatasource> type) {
        return publishTypes.contains(type);
    }

    public List<Class<? extends CollectedDatasource>> getPublishType() {
        return publishTypes;
    }
}
