package com.sccl.framework.aspectj.lang.constant;

/**
 * 业务操作类型
 * 
 * <AUTHOR>
 *
 */
public class BusinessType
{
    /** 其它 */
    public static final String OTHER = "0";
    /** 新增 */
    public static final String INSERT = "1";
    /** 修改 */
    public static final String UPDATE = "2";
    /** 删除 */
    public static final String DELETE = "3";
    /** 授权 */
    public static final String GRANT = "4";
    /** 导出 */
    public static final String EXPORT = "5";
    /** 导入 */
    public static final String IMPORT = "6";
    /** 强退 */
    public static final String FORCE = "7";
    /** 生成代码 */
    public static final String GENCODE = "8";
    /** 登录 */
    public static final String LOGIN = "9";
}
