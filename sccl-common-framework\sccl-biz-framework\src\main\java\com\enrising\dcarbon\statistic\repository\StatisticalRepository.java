package com.enrising.dcarbon.statistic.repository;


import com.enrising.dcarbon.statistic.StatisticalIndex;
import com.enrising.dcarbon.statistic.codec.StatisticalDeserializer;
import com.enrising.dcarbon.statistic.codec.StatisticalSerializer;

import java.util.List;

/**
 * 统计指标存储库
 *
 * <AUTHOR>
 * @Date 2022/10/25 17:19
 */
public interface StatisticalRepository<R> {
    boolean save(List<StatisticalIndex> statisticalIndices, StatisticalSerializer<R> serializer);

    List<StatisticalIndex> query(StatisticalDeserializer<R> deserializer, R query);

    boolean update(R param, R query);

    boolean delete(R query);
}
