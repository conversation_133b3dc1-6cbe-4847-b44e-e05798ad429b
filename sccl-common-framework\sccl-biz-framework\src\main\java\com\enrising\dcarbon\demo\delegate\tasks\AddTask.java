package com.enrising.dcarbon.demo.delegate.tasks;

import com.enrising.dcarbon.delegate.NamedTask;
import com.enrising.dcarbon.demo.delegate.HandleType;


/**
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-10 15:06
 * @email <EMAIL>
 */
public class AddTask implements NamedTask {
    @Override
    public String taskName() {
        return HandleType.ADD.getName();
    }

    @Override
    public void doing() {
        System.out.println("新增操作");
    }
}
