package com.sccl.common.constant;

import java.util.ArrayList;
import java.util.List;

public class LocalNet {
	public static List<String[]> AREACODE = new ArrayList<String[]>();
	static
	  {
	    AREACODE.add(new String[] { "RMP", "100000", "省公司" });
	    AREACODE.add(new String[] { "ECM", "101000", "成都" });
	    AREACODE.add(new String[] { "RES", "102000", "自贡" });
	    AREACODE.add(new String[] { "MY", "106000", "绵阳" });
	    AREACODE.add(new String[] { "PZ", "103000", "攀枝花" });
	    AREACODE.add(new String[] { "LZ", "104000", "泸州" });
	    AREACODE.add(new String[] { "DY", "105000", "德阳" });
	    AREACODE.add(new String[] { "GY", "107000", "广元" });
	    AREACODE.add(new String[] { "SN", "108000", "遂宁" });
	    AREACODE.add(new String[] { "NJ", "109000", "内江" });
	    AREACODE.add(new String[] { "LS", "110000", "乐山" });
	    AREACODE.add(new String[] { "ZY", "121000", "资阳" });
	    AREACODE.add(new String[] { "YB", "112000", "宜宾" });
	    AREACODE.add(new String[] { "NC", "111000", "南充" });
	    AREACODE.add(new String[] { "DZ", "114000", "达州" });
	    AREACODE.add(new String[] { "YA", "115000", "雅安" });
	    AREACODE.add(new String[] { "AB", "116000", "阿坝" });
	    AREACODE.add(new String[] { "GZ", "117000", "甘孜" });
	    AREACODE.add(new String[] { "LL", "118000", "凉山" });
	    AREACODE.add(new String[] { "GA", "113000", "广安" });
	    AREACODE.add(new String[] { "BZ", "119000", "巴中" });
	    AREACODE.add(new String[] { "MS", "120000", "眉山" });
	  }
	
	/**
	 * 根据本地网ID获取本地网编码
	 * @param id
	 * @return
	 */
	public static String getAreaCodeById(String id) {
		for (String[] item : AREACODE)
	    {
	      if (item[1].equals(id))
	      {
	        return item[0];
	      }
	    }
		
		String code=getAreaCodeByShortId(id);

		return code;
	}
	
	/**
	 * 短本地网ID的情况 例如成都 10100
	 * @return
	 */
	private static String getAreaCodeByShortId(String id) {
		for (String[] item : AREACODE)
	    {
			String sid=item[1].substring(0, item[1].length()-1);
	      if (sid.equals(id))
	      {
	        return item[0];
	      }
	    }

		return "";
	}
}
