package com.enrising.dcarbon.demo.collector;

import com.enrising.dcarbon.audit.AuditContext;
import com.enrising.dcarbon.collector.*;

/**
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-12 14:16
 * @email <EMAIL>
 */
public class DemoCollectorTemplate extends AbstractCollectorTemplate {
    @Override
    public Collector collector() {
        return new DemoCollector();
    }

    @Override
    protected CollectedDatasourceHandlerChain handler<PERSON>hain() {
        return null;
    }


    @Override
    public AuditContext auditContext() {
        return null;
    }

    @Override
    public CollectedDatasourceRouteStrategyContext strategyContext() {
        RouteEventManagerInstance
                .getInstance()
                .addListenerIfAbsent(RouteEvent.class, new DemoRouteEventListener());
        //使用观察者策略
        return new CollectedDatasourceRouteStrategyContext(new ObserverRouteStrategy(DemoRouteEvent::new));
    }


}
