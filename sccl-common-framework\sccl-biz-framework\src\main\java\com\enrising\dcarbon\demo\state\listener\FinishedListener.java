package com.enrising.dcarbon.demo.state.listener;

import com.enrising.dcarbon.demo.state.DemoBaseData;
import com.enrising.dcarbon.demo.state.events.FinishedEvent;
import com.enrising.dcarbon.observer.EventListener;

/**
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-10 11:10
 * @email <EMAIL>
 */
public class FinishedListener implements EventListener<FinishedEvent> {
    @Override
    public void onEvent(FinishedEvent event) {
        DemoBaseData data = (DemoBaseData) event
                .getSource()
                .getSource();
        System.out.println("监听到完成事件，当前数据状态：" + data.getStatus());
    }
}
