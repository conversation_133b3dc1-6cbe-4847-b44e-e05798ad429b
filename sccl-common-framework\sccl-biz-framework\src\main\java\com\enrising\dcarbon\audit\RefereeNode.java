package com.enrising.dcarbon.audit;


import com.enrising.dcarbon.servlet.ServletUtil;

import java.util.List;
import java.util.Map;

/**
 * 评判节点，一个评判节点进行单个规则的评判
 *
 * <AUTHOR>
 * @Date 2022/11/10 17:43
 * @Email <EMAIL>
 */
public class RefereeNode implements Cloneable {
    private static final int FREE = 0;
    private static final int BUSY = 1;
    /**
     * 评判器
     */
    protected AbstractReferee referee;
    /**
     * 评判数据源创建器
     */
    protected AbstractRefereeDatasourceCreator datasourceCreator;
    /**
     * 节点名称
     */
    protected String nodeName;
    /**
     * 评判结果
     */
    protected RefereeResult refereeResult;
    /**
     * 下一个评判节点
     */
    protected RefereeNode next;

    /**
     * 是否允许发布稽核结果相关事件
     */
    protected boolean isEnablePublishEvent;

    private volatile int status = FREE;

    public RefereeNode(AbstractRefereeDatasourceCreator datasourceCreator) {
        this(datasourceCreator, null);
    }

    public RefereeNode(AbstractRefereeDatasourceCreator datasourceCreator, String nodeName) {
        this.datasourceCreator = datasourceCreator;
        this.nodeName = nodeName;
        this.referee = datasourceCreator.getReferee();
    }

    public RefereeNode(AbstractRefereeDatasourceCreator datasourceCreator, String nodeName, boolean isEnablePublishEvent) {
        this.datasourceCreator = datasourceCreator;
        this.nodeName = nodeName;
        this.isEnablePublishEvent = isEnablePublishEvent;
    }


    public RefereeNode enableAuditEvent() {
        isEnablePublishEvent = true;
        return this;
    }

    public RefereeNode disableAuditEvent() {
        isEnablePublishEvent = false;
        return this;
    }

    void next(RefereeNode next) {
        this.next = next;
    }

    public String getNodeName() {
        return nodeName;
    }

    public RefereeNode setNodeName(String nodeName) {
        this.nodeName = nodeName;
        return this;
    }

    /**
     * 如果后续还存在评判节点，是否向后续传播，默认无条件传播，如果需要阻止后续的节点评判，该方法应该返回false，请通过继承拓展该功能
     *
     * @param refereeResult 评判结果
     * @return boolean 是否往后续节点传播
     * <AUTHOR> Yongxiang
     * @date 2022/11/10 17:51
     */
    protected boolean isSpread(RefereeResult refereeResult) {
        return true;
    }

    public boolean isBusy() {
        return status == BUSY;
    }

    public boolean isFree() {
        return status == FREE;
    }

    public void writeResponse(String msg) {
        ServletUtil.renderString(msg);
    }

    /**
     * 获取当前节点的评判结果
     *
     * @return com.sccl.modules.business.stationaudit.framework.referee.RefereeResult
     * <AUTHOR> Yongxiang
     * @date 2022/11/11 10:01
     */
    public RefereeResult getRefereeResult() {
        return refereeResult;
    }

    protected void referee(RefereeResult lastNodeResult, Auditable auditable) {
        if (status == BUSY) {
            throw new IllegalStateException("节点繁忙");
        }
        status = BUSY;
        try {
            Map<Class<? extends RefereeDatasource>, List<RefereeDatasource>> refereeDatasource = datasourceCreator.collect(auditable);
            if (refereeDatasource != null) {
                for (Map.Entry<Class<? extends RefereeDatasource>, List<RefereeDatasource>> entry : refereeDatasource.entrySet()) {
                    entry
                            .getValue()
                            .removeIf(data -> data != null && !data.isAvailable());
                }
            }
            refereeResult = referee.doReferee(lastNodeResult, auditable, refereeDatasource);
            if (isEnablePublishEvent) {
                RefereeChain.currentRefereeChain.get().currentAuditContext.publishAuditEvent(new AuditEvent(new AuditEventLoad(auditable, refereeResult, refereeDatasource, nodeName)));
            }
        } catch (Exception e) {
            if (isEnablePublishEvent) {
                RefereeChain.currentRefereeChain.get().currentAuditContext.publishAuditEvent(new AuditEvent(new AuditEventLoad(auditable, nodeName, e)));
            }
            e.printStackTrace();
        } finally {
            status = FREE;
        }
        if (next != null && isSpread(refereeResult) && !Thread
                .currentThread()
                .isInterrupted()) {
            next.referee(refereeResult, auditable);
        }

    }

    void reset() {
        refereeResult = null;
        status = FREE;
    }

    @Override
    protected Object clone() throws CloneNotSupportedException {
        RefereeNode node = (RefereeNode) super.clone();
        if (next != null) {
            node.next = (RefereeNode) this.next.clone();
        }
        if (datasourceCreator != null) {
            node.datasourceCreator = (AbstractRefereeDatasourceCreator) this.datasourceCreator.clone();
        }
        if (referee != null) {
            node.referee = (AbstractReferee) this.referee.clone();
        }
        return node;
    }
}
