package com.sccl.common.utils;

import com.sccl.common.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;

/**
 * IP地址辅助工具类
 * 提供IP获取、验证、分析等功能
 *
 * <AUTHOR>
 * @date 2024-12-01
 */
public class IpHelper {
    private static final Logger log = LoggerFactory.getLogger(IpHelper.class);

    /**
     * 获取客户端真实IP地址
     * 统一入口，优先使用IpUtils.getIpAddr()
     *
     * @param request HTTP请求对象
     * @return 客户端真实IP地址，获取失败返回"unknown"
     */
    public static String getRealIp(HttpServletRequest request) {
        try {
            String ip = IpUtils.getIpAddr(request);
            if (StringUtils.isNotBlank(ip) && !"unknown".equalsIgnoreCase(ip)) {
                log.debug("获取到客户端IP: {}", ip);
                return ip;
            }
        } catch (Exception e) {
            log.warn("获取客户端IP失败", e);
        }
        return "unknown";
    }

    /**
     * 获取所有可能的IP地址信息（用于调试）
     * 返回所有代理头中的IP信息
     *
     * @param request HTTP请求对象
     * @return IP信息字符串
     */
    public static String getAllIpInfo(HttpServletRequest request) {
        if (request == null) {
            return "request is null";
        }

        StringBuilder sb = new StringBuilder();
        sb.append("RemoteAddr: ").append(request.getRemoteAddr()).append("; ");

        List<String> headers = Arrays.asList(
                "X-Real-IP", "X-Forwarded-For", "x-forwarded-for",
                "Proxy-Client-IP", "WL-Proxy-Client-IP",
                "HTTP_CLIENT_IP", "HTTP_X_FORWARDED_FOR"
        );

        for (String header : headers) {
            String value = request.getHeader(header);
            if (StringUtils.isNotBlank(value)) {
                sb.append(header).append(": ").append(value).append("; ");
            }
        }

        return sb.toString();
    }

    /**
     * 判断IP是否为内网地址
     *
     * @param ip IP地址
     * @return true-内网地址，false-公网地址
     */
    public static boolean isInternalIp(String ip) {
        if (StringUtils.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            return false;
        }

        // 本地回环地址
        if (ip.startsWith("127.") || "localhost".equalsIgnoreCase(ip)) {
            return true;
        }

        // 内网IP段
        if (ip.startsWith("192.168.") ||
            ip.startsWith("10.") ||
            (ip.startsWith("172.") && isInRange172(ip))) {
            return true;
        }

        return false;
    }

    /**
     * 判断是否在**********-**************范围内
     */
    private static boolean isInRange172(String ip) {
        try {
            String[] parts = ip.split("\\.");
            if (parts.length >= 2) {
                int secondOctet = Integer.parseInt(parts[1]);
                return secondOctet >= 16 && secondOctet <= 31;
            }
        } catch (NumberFormatException e) {
            log.debug("解析IP地址失败: {}", ip);
        }
        return false;
    }

    /**
     * 记录IP访问日志（用于调试和监控）
     *
     * @param request HTTP请求对象
     * @param action 操作描述
     */
    public static void logIpAccess(HttpServletRequest request, String action) {
        if (log.isDebugEnabled()) {
            String realIp = getRealIp(request);
            String allIpInfo = getAllIpInfo(request);
            log.debug("IP访问记录 - 操作: {}, 真实IP: {}, 所有IP信息: {}",
                    action, realIp, allIpInfo);
        }
    }
}
