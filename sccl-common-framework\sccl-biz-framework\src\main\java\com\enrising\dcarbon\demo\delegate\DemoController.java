package com.enrising.dcarbon.demo.delegate;

/**
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-10 15:05
 * @email <EMAIL>
 */
public class DemoController {
    private static final DemoService SERVICE = new DemoService();

    public void add(DemoBaseData data) {
        SERVICE.doing(data, HandleType.ADD);
    }

    public void update(long id) {
        SERVICE.doing(null, HandleType.UPDATE);
    }

    public void del(long id) {
        SERVICE.doing(null, HandleType.DELETE);
    }

}
