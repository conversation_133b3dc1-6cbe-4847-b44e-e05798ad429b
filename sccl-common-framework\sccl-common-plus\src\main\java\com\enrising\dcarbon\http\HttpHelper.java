package com.enrising.dcarbon.http;

import com.enrising.dcarbon.codec.JsonUtil;
import com.enrising.dcarbon.string.StringUtils;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import lombok.AccessLevel;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.Header;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpEntityEnclosingRequestBase;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.impl.client.DefaultConnectionKeepAliveStrategy;
import org.apache.http.impl.client.DefaultHttpRequestRetryHandler;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicHeader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.*;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import java.net.URI;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 请求数据类，目前支持带参数、body以及请求头的get和post请求
 *
 * @Auther Huang Yongxiang
 * @Date 2021/08/10 9:05
 */
@Setter(AccessLevel.PUBLIC)
@Accessors(chain = true)
@Slf4j
public class HttpHelper {
    /**
     * URL地址
     */
    private String url;

    /**
     * 请求方式：post/get，对大小写不敏感
     */
    private String ways;

    /**
     * 参数集合
     */
    private Map<String, Object> attributes;

    /**
     * 请求头
     */
    private HttpHeaders requestHeaders;

    /**
     * 响应体
     */
    private ResponseEntity<String> responseEntity;

    private RestTemplate restTemplate;

    /**
     * 填入请求体body中的信息
     */
    private String body;

    /**
     * 建立连接超时时间：ms
     */
    private static Integer createLinkTimeOut = 30000;

    /**
     * 从服务器获取数据超时时间：ms
     */
    private static Integer getDataTimeOut = 30000;
    private static final Logger logger = LoggerFactory.getLogger(HttpHelper.class);

    ////用于发送带body的get请求
    //private static class HttpGetWithEntity extends HttpEntityEnclosingRequestBase {
    //    private final static String METHOD_NAME = "GET";
    //
    //    @Override
    //    public String getMethod() {
    //        return METHOD_NAME;
    //    }
    //
    //    public HttpGetWithEntity() {
    //        super();
    //    }
    //
    //    public HttpGetWithEntity(final URI uri) {
    //        super();
    //        setURI(uri);
    //    }
    //
    //    HttpGetWithEntity(final String uri) {
    //        super();
    //        setURI(URI.create(uri));
    //    }
    //
    //}

    public HttpHelper() {
    }


    public static Builder builder() {
        Builder builder = new Builder();
        builder.setCreateLinkTimeOut(10 * 1000);
        builder.setGetDataTimeOut(5 * 1000);
        return builder;
    }


    /**
     * 清空对象所占资源并通知GC
     *
     * @param
     * @return void
     * <AUTHOR> Yongxiang
     * @date 2021/10/12 10:20
     */
    public void clear() {
        url = null;
        ways = null;
        attributes = null;
        requestHeaders = null;
        responseEntity = null;
        body = null;
        System.gc();
    }


    /**
     * 发送不带任何参数，请求头和请求体的请求
     *
     * @return org.springframework.http.ResponseEntity
     * <AUTHOR> Yongxiang
     * @date 2021/10/12 11:08
     */
    public ResponseEntity<String> send() {
        ways = ways.toLowerCase();
        switch (ways) {
            case "post": {
                this.responseEntity = restTemplate.postForEntity(url, null, String.class);
                break;
            }
            case "get": {
                this.responseEntity = restTemplate.getForEntity(url, String.class);
                break;
            }
            default: {
                System.out.println("方法没有提供" + ways + "方式的请求！");
                break;
            }
        }
        return this.responseEntity;
    }

    /**
     * 发送带参数的请求获取Response，目前只支持POST和GET两种请求
     *
     * @return org.springframework.http.ResponseEntity
     * <AUTHOR> Yongxiang
     * @date 2021/8/10 13:51
     */
    public ResponseEntity<String> sendWithAttributes() {
        ways = ways.toLowerCase();
        switch (ways) {
            case "post": {
                this.responseEntity = restTemplate.postForEntity(url, attributes, String.class);
                break;
            }
            case "get": {
                String newUrl = url + "?" + formatGetAttributes(attributes);
                this.responseEntity = restTemplate.getForEntity(newUrl, String.class, attributes);
                break;
            }
            default: {
                System.out.println("方法没有提供" + ways + "方式的请求！");
                break;
            }
        }
        return this.responseEntity;
    }

    /**
     * 发送带Header的请求
     *
     * @return org.springframework.http.ResponseEntity
     * <AUTHOR> Yongxiang
     * @date 2021/10/12 11:08
     */
    public ResponseEntity<String> sendWithHeader() {
        if (!StringUtils.isEmpty(ways)) {
            ways = ways.toLowerCase();
            switch (ways) {
                case "post": {
                    responseEntity = restTemplate.postForEntity(url, new HttpEntity<String>(requestHeaders), String.class);
                    break;
                }
                case "get": {
                    responseEntity = restTemplate.exchange(url, HttpMethod.GET, new HttpEntity<String>(requestHeaders), String.class);
                    break;
                }
                default: {
                    System.out.println("方法没有提供" + ways + "方式的请求！");
                    break;
                }
            }
        } else {
            responseEntity = restTemplate.postForEntity(url, new HttpEntity<String>(requestHeaders), String.class);
        }
        return responseEntity;
    }

    /**
     * 发送带Body的Post请求，如果构建时使用了Header，则该方法发送的请求也将包含添加的header
     *
     * @return org.springframework.http.ResponseEntity
     * <AUTHOR> Yongxiang
     * @date 2021/10/12 12:23
     */
    public ResponseEntity<String> sendWithBody() {
        if (requestHeaders == null) {
            requestHeaders = new HttpHeaders();
        }
        requestHeaders.add("Content-Type", "application/json;charset=UTF-8");
        String newUrl = url;
        if (attributes != null && attributes.size() > 0) {
            newUrl = url + "?" + formatGetAttributes(attributes);
        }
        responseEntity = restTemplate.postForEntity(newUrl, new HttpEntity<>(body, requestHeaders), String.class);
        return responseEntity;
    }

    /**
     * 发送带Body的Get请求，注意该方法使用的是apache的库，该方法暂不支持添加额外的Header
     *
     * @return org.apache.http.HttpResponse
     * <AUTHOR> Yongxiang
     * @date 2021/10/12 12:23
     */
    public ResponseEntity<String> sendWithBodyGet() {
        try {
            restTemplate.setRequestFactory(new HttpComponentsClientRestfulHttpRequestFactory());
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> entity = new HttpEntity<>(body, httpHeaders);
            this.responseEntity = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);
            return this.responseEntity;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            restTemplate.setRequestFactory(new SimpleClientHttpRequestFactory());
        }
        return null;
    }

    public String getResponseAsString() {
        if (responseEntity == null) {
            logger.error("清先发送请求！");
            return null;
        }
        return responseEntity.getBody();
    }


    /**
     * 获取响应体包装的JSON对象
     *
     * @return net.sf.json.JSONObject
     * <AUTHOR> Yongxiang
     * @date 2021/8/10 13:55
     */
    public JsonObject getResponseJsonObject() {

        if (responseEntity == null) {
            logger.error("清先发送请求！");
            return null;
        }
        return JsonUtil.jsonStringToJsonObj(responseEntity.getBody());
    }

    public Map<String, String> getResponseAsMap() {
        return JsonUtil.jsonObject2StringMap(getResponseJsonObject());
    }


    /**
     * 获取响应体包装的JSON对象数组
     *
     * @return net.sf.json.JSONArray
     * <AUTHOR> Yongxiang
     * @date 2021/8/10 13:58
     */
    public JsonArray getResponseJsonArray() {
        if (responseEntity == null) {
            logger.error("清先发送请求！");
            return null;
        }
        return JsonUtil.stringToJsonArray(responseEntity.getBody());
    }

    public static String formatGetAttributes(Map<String, Object> params) {
        if (params == null) {
            return "";
        }
        StringBuilder stringBuilder = new StringBuilder();
        for (String key : params.keySet()) {
            stringBuilder
                    .append(key)
                    .append("={")
                    .append(key)
                    .append("}&");
        }
        String res = stringBuilder.toString();
        return res.substring(0, res.lastIndexOf("&"));
    }

    /**
     * 获取响应体中指定key的值
     *
     * @param key 响应体中的key
     * @return java.lang.String
     * <AUTHOR> Yongxiang
     * @date 2021/8/10 14:00
     */
    public Object getResponseByKey(String key) {
        if (responseEntity == null) {
            logger.error("清先发送请求！");
            return null;
        }
        return getResponseJsonObject().get(key);
    }

    public void reset() {
        responseEntity = null;
        body = null;
        if (attributes != null) {
            attributes.clear();
        }
        if (requestHeaders != null && requestHeaders.size() > 0) {
            requestHeaders.clear();
        }
    }


    @Setter
    @Accessors(chain = true)
    public static class Builder {
        /**
         * URL地址
         */
        private String url;

        /**
         * 请求方式：post/get，对大小写不敏感
         */
        private String ways;

        /**
         * 参数集合
         */
        private Map<String, Object> attributes;

        /**
         * 请求头
         */
        private HttpHeaders requestHeaders;
        /**
         * 长连接保持时间
         */
        private Long longConnectionTimeout = 60000L;

        private RestTemplate restTemplate;
        /**
         * 最大路由数
         */
        private Integer maxTotal = 5;
        /**
         * 填入请求体body中的信息
         */
        private String body;

        /**
         * 建立连接超时时间：ms
         */
        private Integer createLinkTimeOut = 10000;

        /**
         * 从服务器获取数据超时时间：ms
         */
        private Integer getDataTimeOut = 5000;

        public Builder setCreateLinkTimeOut(int createLinkTimeOut, TimeUnit unit) {
            this.createLinkTimeOut = Math.toIntExact(unit.toMillis(createLinkTimeOut));
            return this;
        }

        public Builder setGetDataTimeOut(int getDataTimeOut, TimeUnit unit) {
            this.getDataTimeOut = Math.toIntExact(unit.toMillis(getDataTimeOut));
            return this;
        }

        public Builder setLongConnectionTimeout(long longConnectionTimeout, TimeUnit unit) {
            this.longConnectionTimeout = unit.toMillis(longConnectionTimeout);
            return this;
        }

        public HttpHelper build() {
            HttpHelper responseGet = new HttpHelper();
            responseGet.setBody(body);
            responseGet.setUrl(url);
            responseGet.setRequestHeaders(requestHeaders);
            responseGet.setAttributes(attributes);
            responseGet.setWays(ways);
            responseGet.setRestTemplate(restTemplate == null ? getRestTemplate() : restTemplate);
            return responseGet;
        }

        private RestTemplate getRestTemplate() {
            HttpComponentsClientHttpRequestFactory httpRequestFactory = httpComponentsClientHttpRequestFactory();
            RestTemplate restTemplate = new RestTemplate(httpRequestFactory);
            //解决首次预热耗时长
            //if (StringUtils.isNotEmpty(url)) {
            //    try {
            //        restTemplate.postForEntity(url, "", String.class);
            //    } catch (Exception e) {
            //        log.error("preHeat url error:{}", e.getMessage());
            //    }
            //}
            return restTemplate;
        }

        private HttpComponentsClientHttpRequestFactory httpComponentsClientHttpRequestFactory() {
            //Httpclient连接池，长连接保持时间
            PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager(longConnectionTimeout, TimeUnit.MILLISECONDS);
            //设置总连接数
            connectionManager.setMaxTotal(maxTotal);
            //设置同路由的并发数
            connectionManager.setDefaultMaxPerRoute(maxTotal);
            //设置header
            List<Header> headers = new ArrayList<Header>();
            headers.add(new BasicHeader("Accept-Encoding", "gzip, deflate"));
            headers.add(new BasicHeader("Accept-Language", "zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3"));
            headers.add(new BasicHeader("Connection", "keep-alive"));
            //创建HttpClient
            HttpClient httpClient = HttpClientBuilder
                    .create()
                    .setConnectionManager(connectionManager)
                    .setDefaultHeaders(headers)
                    .setRetryHandler(new DefaultHttpRequestRetryHandler(3, true)) //设置重试次数
                    .setKeepAliveStrategy(new DefaultConnectionKeepAliveStrategy()) //设置保持长连接
                    .build();
            //创建HttpComponentsClientHttpRequestFactory实例
            HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory(httpClient);
            //设置客户端和服务端建立连接的超时时间
            requestFactory.setConnectTimeout(createLinkTimeOut);
            //设置客户端从服务端读取数据的超时时间
            requestFactory.setReadTimeout(getDataTimeOut);
            //设置从连接池获取连接的超时时间，不宜过长
            requestFactory.setConnectionRequestTimeout(5000);
            //缓冲请求数据，默认为true。通过POST或者PUT大量发送数据时，建议将此更改为false，以免耗尽内存
            requestFactory.setBufferRequestBody(false);
            return requestFactory;
        }
    }

    public static class HttpComponentsClientRestfulHttpRequestFactory extends HttpComponentsClientHttpRequestFactory {
        @Override
        protected HttpUriRequest createHttpUriRequest(HttpMethod httpMethod, URI uri) {

            if (httpMethod == HttpMethod.GET) {
                return new HttpGetRequestWithEntity(uri);
            }
            return super.createHttpUriRequest(httpMethod, uri);
        }

        /**
         * 定义HttpGetRequestWithEntity实现HttpEntityEnclosingRequestBase抽象类，以支持GET请求携带body数据
         */

        private static final class HttpGetRequestWithEntity extends HttpEntityEnclosingRequestBase {
            public HttpGetRequestWithEntity(final URI uri) {
                super.setURI(uri);
            }

            @Override
            public String getMethod() {
                return HttpMethod.GET.name();

            }
        }
    }

    public String getUrl() {
        return url;
    }

    public String getWays() {
        return ways;
    }

    public RestTemplate getRestTemplate() {
        return restTemplate;
    }
}
