package com.sccl.common.utils;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.sccl.common.lang.StringUtils;
import lombok.AccessLevel;
import lombok.Setter;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpEntityEnclosingRequestBase;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClients;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import java.net.MalformedURLException;
import java.net.URI;
import java.net.URL;
import java.util.Map;

/**
 * @Description 请求数据类，目前支持带参数、body以及请求头的get和post请求
 * @Auther Huang Yongxiang
 * @Date 2021/08/10 9:05
 */
@Setter(AccessLevel.PUBLIC)
public class ResponseGet {
    /**
     * URL地址
     */
    private String url;

    /**
     * 请求方式：post/get，对大小写不敏感
     */
    private String ways;

    /**
     * 参数集合
     */
    private Map<String, Object> attributes;

    /**
     * 请求头
     */
    private HttpHeaders requestHeaders;

    /**
     * 响应体
     */
    private ResponseEntity<String> responseEntity;

    private RestTemplate restTemplate;

    /**
     * 填入请求体body中的信息
     */
    private String body;

    /**
     * 建立连接超时时间：ms
     */
    private static Integer createLinkTimeOut = 30000;

    /**
     * 从服务器获取数据超时时间：ms
     */
    private static Integer getDataTimeOut = 30000;
    private static final Logger logger = LoggerFactory.getLogger(ResponseGet.class);

    //用于发送带body的get请求
    private static class HttpGetWithEntity extends HttpEntityEnclosingRequestBase {
        private final static String METHOD_NAME = "GET";

        @Override
        public String getMethod() {
            return METHOD_NAME;
        }

        public HttpGetWithEntity() {
            super();
        }

        public HttpGetWithEntity(final URI uri) {
            super();
            setURI(uri);
        }

        HttpGetWithEntity(final String uri) {
            super();
            setURI(URI.create(uri));
        }

    }

    public ResponseGet() {
    }

    /**
     * 创建对象实例，指定建立连接超时时间和获取数据超时时间
     *
     * @param createLinkTimeout 建立连接超时时间
     * @param getDataTimeout    获取数据超时时间
     * @return com.sccl.common.utils.ResponseGet
     * <AUTHOR> Yongxiang
     * @date 2022/1/12 14:55
     */
    public ResponseGet create(int createLinkTimeout, int getDataTimeout) {
        if (restTemplate == null) {
            RestTemplate restTemplate = new RestTemplate();
            SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
/*            factory.setConnectTimeout(createLinkTimeout);
            factory.setReadTimeout(getDataTimeout);*/
            restTemplate.setRequestFactory(factory);
            this.restTemplate = restTemplate;
        }
        return this;
    }


    /**
     * 使用URL路径、请求类型、参数创建ResponseGet对象单例
     *
     * @param path      URL路径
     * @param type      请求类型：post/get大小写均可
     * @param attribute 参数
     * @return com.sccl.modules.business.datafilter.util.ResponseGet
     * <AUTHOR> Yongxiang
     * @date 2021/10/12 9:57
     */
    public static ResponseGet build(String path, String type, Map<String, Object> attribute) {
        ResponseGet responseGet = new ResponseGet();
        responseGet.setWays(type);
        responseGet.setUrl(path);
        responseGet.setAttributes(attribute);
        return responseGet;
    }

    /**
     * 使用URL路径和请求类型创建ResponseGet对象单例
     *
     * @param path URL路径
     * @param type 类型：post/get大小写均可
     * @return com.sccl.modules.business.datafilter.util.ResponseGet
     * <AUTHOR> Yongxiang
     * @date 2021/10/12 9:56
     */
    public static ResponseGet build(String path, String type) {
        ResponseGet responseGet = new ResponseGet();
        responseGet.setUrl(path);
        responseGet.setWays(type);
        return responseGet;
    }

    /**
     * 构建带URL、header以及参数的ResponseGet对象单例，请求类型默认为Post
     *
     * @param path      URL路径
     * @param headers   请求头
     * @param attribute 参数
     * @return com.sccl.modules.business.datafilter.util.ResponseGet
     * <AUTHOR> Yongxiang
     * @date 2021/10/12 10:17
     */
    public static ResponseGet build(String path, HttpHeaders headers, Map<String, Object> attribute) {
        ResponseGet responseGet = new ResponseGet();
        responseGet.setUrl(path);
        responseGet.setRequestHeaders(headers);
        responseGet.setAttributes(attribute);
        return responseGet;
    }

    /**
     * 构建带URL、header以及JSON格式的请求体的ResponseGet对象单例，请求类型默认为Post
     *
     * @param path          URL路径
     * @param headers       请求头
     * @param requestEntity 请求体
     * @return com.sccl.modules.business.datafilter.util.ResponseGet
     * <AUTHOR> Yongxiang
     * @date 2021/10/12 10:06
     */
    public static ResponseGet build(String path, HttpHeaders headers, String requestEntity) {
        ResponseGet responseGet = new ResponseGet();
        responseGet.setUrl(path);
        responseGet.setRequestHeaders(headers);
        responseGet.setBody(requestEntity);
        return responseGet;
    }

    /**
     * 构建带URL路径、header、JSON格式的请求体以及参数的ResponseGet对象单例，请求类型默认为Post
     *
     * @param path          URL路径
     * @param headers       请求头
     * @param requestEntity 请求体
     * @param attribute     参数
     * @return
     */
    public static ResponseGet build(String path, HttpHeaders headers, String requestEntity, Map<String, Object> attribute) {
        ResponseGet responseGet = new ResponseGet();
        responseGet.setUrl(path);
        responseGet.setRequestHeaders(headers);
        responseGet.setBody(requestEntity);
        responseGet.setAttributes(attribute);
        return responseGet;
    }

    /**
     * 仅提供路径构建，默认使用POST方法
     *
     * @param path URL路径
     * @return com.sccl.modules.business.datafilter.util.ResponseGet
     * <AUTHOR> Yongxiang
     * @date 2021/10/12 9:55
     */
    public static ResponseGet build(String path) {
        ResponseGet responseGet = new ResponseGet();
        responseGet.setUrl(path);
        responseGet.setWays("post");
        return responseGet;
    }

    /**
     * 构建带URL路径、请求类型、请求头、请求体以及参数的ResponseGet对象单例
     *
     * @param path          URL路径
     * @param type          请求类型：post/get大小写均可
     * @param headers       请求头
     * @param requestEntity JSON格式请求体
     * @param attribute     参数
     * @return com.sccl.modules.business.datafilter.util.ResponseGet
     * <AUTHOR> Yongxiang
     * @date 2021/10/12 10:30
     */
    public static ResponseGet build(String path, String type, HttpHeaders headers, String requestEntity, Map<String, Object> attribute) {
        ResponseGet responseGet = new ResponseGet();
        responseGet.setUrl(path);
        responseGet.setRequestHeaders(headers);
        responseGet.setBody(requestEntity);
        responseGet.setAttributes(attribute);
        responseGet.setWays(type);
        return responseGet;
    }

    /**
     * 清空对象所占资源并通知GC
     *
     * @param
     * @return void
     * <AUTHOR> Yongxiang
     * @date 2021/10/12 10:20
     */
    public void clear() {
        url = null;
        ways = null;
        attributes = null;
        requestHeaders = null;
        responseEntity = null;
        body = null;
        System.gc();
    }


    /**
     * 获取路径的URL类
     *
     * @param
     * @return java.net.URL
     * <AUTHOR> Yongxiang
     * @date 2021/8/10 9:19
     */
    public URL getUrl() {
        URL link = null;
        try {
            link = new URL(url);
        } catch (MalformedURLException e) {
            System.out.println("你输入的网址格式不合法！" + url);
            e.printStackTrace();
        }
        return link;
    }

    /**
     * 发送不带任何参数，请求头和请求体的请求
     *
     * @param
     * @return org.springframework.http.ResponseEntity
     * <AUTHOR> Yongxiang
     * @date 2021/10/12 11:08
     */
    public ResponseEntity<String> send() {
        if (restTemplate == null) {
            create(20 * 1000, 20 * 1000);
        }
        ways = ways.toLowerCase();
        switch (ways) {
            case "post": {
                this.responseEntity = restTemplate.postForEntity(url, null, String.class);
                break;
            }
            case "get": {
                this.responseEntity = restTemplate.getForEntity(url, String.class);
                break;
            }
            default: {
                System.out.println("方法没有提供" + ways + "方式的请求！");
                break;
            }
        }
        return this.responseEntity;
    }

    /**
     * 发送带参数的请求获取Response，目前只支持POST和GET两种请求
     *
     * @param
     * @return org.springframework.http.ResponseEntity
     * <AUTHOR> Yongxiang
     * @date 2021/8/10 13:51
     */
    public ResponseEntity<String> sendWithAttributes() {
        if (restTemplate == null) {
            create(20 * 1000, 20 * 1000);
        }
        ways = ways.toLowerCase();
        switch (ways) {
            case "post": {
                this.responseEntity = restTemplate.postForEntity(url, attributes, String.class);
                break;
            }
            case "get": {
                this.responseEntity = restTemplate.getForEntity(url, String.class, attributes);
                break;
            }
            default: {
                System.out.println("方法没有提供" + ways + "方式的请求！");
                break;
            }
        }
        return this.responseEntity;
    }

    /**
     * 发送带Header的请求
     *
     * @param
     * @return org.springframework.http.ResponseEntity
     * <AUTHOR> Yongxiang
     * @date 2021/10/12 11:08
     */
    public ResponseEntity<String> sendWithHeader() {
        if (restTemplate == null) {
            create(20 * 1000, 20 * 1000);
        }
        if (!StringUtils.isEmpty(ways)) {
            ways = ways.toLowerCase();
            switch (ways) {
                case "post": {
                    responseEntity = restTemplate.postForEntity(url, new HttpEntity<String>(requestHeaders), String.class);
                    break;
                }
                case "get": {
                    responseEntity = restTemplate.exchange(url, HttpMethod.GET, new HttpEntity<String>(requestHeaders), String.class);
                    break;
                }
                default: {
                    System.out.println("方法没有提供" + ways + "方式的请求！");
                    break;
                }
            }
        } else {
            responseEntity = restTemplate.postForEntity(url, new HttpEntity<String>(requestHeaders), String.class);
        }
        return responseEntity;
    }

    /**
     * 发送带Body的Post请求，如果构建时使用了Header，则该方法发送的请求也将包含添加的header
     *
     * @param
     * @return org.springframework.http.ResponseEntity
     * <AUTHOR> Yongxiang
     * @date 2021/10/12 12:23
     */
    public ResponseEntity<String> sendWithBody() {
        if (restTemplate == null) {
            create(20 * 1000, 20 * 1000);
        }
        if (requestHeaders == null) {
            requestHeaders = new HttpHeaders();
        }
        requestHeaders.add("Content-Type", "application/json;charset=UTF-8");

        responseEntity = restTemplate.postForEntity(url, new HttpEntity<>(body, requestHeaders), String.class);
        return responseEntity;
    }

    /**
     * 发送带Body的Get请求，注意该方法使用的是apache的库，该方法暂不支持添加额外的Header
     *
     * @param
     * @return org.apache.http.HttpResponse
     * <AUTHOR> Yongxiang
     * @date 2021/10/12 12:23
     */
    public HttpResponse sendWithBodyGet() {

        HttpGetWithEntity oHttpGet = new HttpGetWithEntity(url);
        RequestConfig config = RequestConfig.custom().setConnectTimeout(createLinkTimeOut).setConnectionRequestTimeout(getDataTimeOut).build();
        oHttpGet.setConfig(config);
        HttpResponse response = null;
        try {
            oHttpGet.setEntity(new StringEntity(body));
            oHttpGet.setHeader("Content-Type", "application/json;charset=UTF-8");
            HttpClient httpclient = HttpClients.createDefault();
            response = httpclient.execute(oHttpGet);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return response;
    }


    /**
     * 获取响应体包装的JSON对象
     *
     * @param
     * @return net.sf.json.JSONObject
     * <AUTHOR> Yongxiang
     * @date 2021/8/10 13:55
     */
    public JsonObject getResponseJsonObject() {

        if (responseEntity == null) {
            logger.error("清先发送请求！");
            return null;
        }
        return JsonUtil.stringToJsonObj(responseEntity.getBody());
    }

    /**
     * 获取响应体包装的JSON对象数组
     *
     * @param
     * @return net.sf.json.JSONArray
     * <AUTHOR> Yongxiang
     * @date 2021/8/10 13:58
     */
    public JsonArray getResponseJsonArray() {
        if (responseEntity == null) {
            logger.error("清先发送请求！");
            return null;
        }
        return JsonUtil.stringToJsonArray(responseEntity.getBody());
    }

    /**
     * 获取响应体中指定key的值
     *
     * @param key 响应体中的key
     * @return java.lang.String
     * <AUTHOR> Yongxiang
     * @date 2021/8/10 14:00
     */
    public Object getResponseByKey(String key) {
        if (responseEntity == null) {
            logger.error("清先发送请求！");
            return null;
        }
        return getResponseJsonObject().get(key);
    }

}
