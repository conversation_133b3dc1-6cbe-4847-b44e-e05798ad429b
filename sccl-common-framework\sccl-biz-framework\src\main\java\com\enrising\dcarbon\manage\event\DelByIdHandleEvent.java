package com.enrising.dcarbon.manage.event;

import com.enrising.dcarbon.manage.WebHandleEvent;

import java.util.List;

/**
 * 根据ID物理删除事件
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-10 16:19
 * @email <EMAIL>
 */
public class DelByIdHandleEvent extends WebHandleEvent<List<String>> {
    public DelByIdHandleEvent() {
        super();
    }

    public DelByIdHandleEvent(List<String> source) {
        super(source);
    }
}
