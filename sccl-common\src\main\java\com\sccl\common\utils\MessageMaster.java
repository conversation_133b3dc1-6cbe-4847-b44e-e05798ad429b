package com.sccl.common.utils;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description 消息对象管理类
 * @<PERSON>ther <PERSON>
 * @Date 2021/09/29 16:12
 */
public class MessageMaster {
    /**
     * 序列化消息时是否忽略没使用Expose注解的字段
     */
    private boolean isExcludeFieldsWithoutExposeAnnotation;
    /**
     * 基础信息字段
     */
    private StringBuilder message;

    /**
     * 基础回执码字段
     */
    private Integer code;

    /**
     * 基础数据字段
     */
    private Object data;

    /**
     * 其他消息字段
     */
    private static Map<String, Object> others;

    public MessageMaster(Code code, String message, Object data) {
        this.code = code.flag;
        this.message = new StringBuilder(message);
        this.data = data;
        others = new HashMap<>();
        others.put("code", code.flag);
        others.put("message", message);
        others.put("data", data);
    }

    public MessageMaster(Integer code, String message, Object data) {
        this.code = code;
        this.message = new StringBuilder(message);
        this.data = data;
        others = new HashMap<>();
        others.put("code", code);
        others.put("message", message);
        others.put("data", data);
    }

    public MessageMaster() {
        others = new HashMap<>();
    }

    /**
     * 给定基本参数直接返回一个消息的JSON字符串
     *
     * @param code    状态值
     * @param message 消息
     * @return java.lang.String
     * <AUTHOR> Yongxiang
     * @date 2021/9/30 9:24
     */
    public static String getMessage(int code, String message) {
        Map<String, Object> msg = new HashMap<>();
        msg.put("code", code);
        msg.put("message", message);
        return JsonUtil.getMapJson(msg);
    }

    public static String getMessage(Code code, String message) {
        Map<String, Object> msg = new HashMap<>();
        msg.put("code", code.getFlag());
        msg.put("message", message);
        return JsonUtil.getMapJson(msg);
    }

    /**
     * 给定基本参数直接返回一个消息的JSON字符串
     *
     * @param code    状态值
     * @param message 消息
     * @param data    数据
     * @return java.lang.String
     * <AUTHOR> Yongxiang
     * @date 2021/9/30 9:24
     */
    public static String getMessage(int code, String message, Object data) {
        Map<String, Object> msg = new HashMap<>();
        msg.put("code", code);
        msg.put("message", message);
        msg.put("data", data);
        return JsonUtil.getMapJson(msg);
    }

    public static String getMessage(Code code, String message, Object data) {
        Map<String, Object> msg = new HashMap<>();
        msg.put("code", code.getFlag());
        msg.put("message", message);
        msg.put("data", data);
        return JsonUtil.getMapJson(msg);
    }


    public String getMessage() {
        return message.toString();
    }

    public void setMessage(String message) {
        this.message = new StringBuilder(message);
        others.put("message", message);
    }

    /**
     * 插入一条新的消息字段，除基本字段：code,message,data之外的消息字段，如果原字段存在则会覆盖
     *
     * @param key   消息名
     * @param value 消息内容
     * @return void
     * <AUTHOR> Yongxiang
     * @date 2021/9/29 17:01
     */
    public void insertNewMessage(String key, Object value) {
        others.put(key, value);
    }

    /**
     * 从消息对象中移除一个消息字段
     *
     * @param key 消息名
     * @return java.lang.Object 不存在返回null
     * <AUTHOR> Yongxiang
     * @date 2021/9/29 17:01
     */
    public Object removeMessage(String key) {
        return others.remove(key);
    }

    /**
     * 重写toString方法，返回值是一个消息的格式化JSON字符串
     *
     * @param
     * @return java.lang.String
     * <AUTHOR> Yongxiang
     * @date 2021/9/29 17:12
     */
    @Override
    public String toString() {
        JsonUtil jsonUtil=JsonUtil.build(isExcludeFieldsWithoutExposeAnnotation);
        return jsonUtil.getMapJsonI(others);
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
        others.put("code", code);
    }

    public void setCode(Code code) {
        this.code = code.flag;
        others.put("code", code.flag);
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
        others.put("data", data);
    }

    public boolean isExcludeFieldsWithoutExposeAnnotation() {
        return isExcludeFieldsWithoutExposeAnnotation;
    }

    public void setExcludeFieldsWithoutExposeAnnotation(boolean excludeFieldsWithoutExposeAnnotation) {
        isExcludeFieldsWithoutExposeAnnotation = excludeFieldsWithoutExposeAnnotation;
    }

    /**
     * 状态枚举
     *
     * <AUTHOR> Yongxiang
     * @date 2021/9/30 9:25
     */
    public enum Code {
        /**
         * 请求成功
         */
        OK("success", 200),
        /**
         * 服务器内部错误
         */
        ERROR("error", 500),
        /**
         * 客户端请求的语法错误，服务器无法理解
         */
        BAD_REQUEST("bad request", 400),
        /**
         * 服务器理解请求客户端的请求，但是拒绝执行此请求
         */
        FORBIDDEN("Forbidden", 403),
        /**
         * 超时
         */
        TIME_OUT("time out", 408),
        /**
         * 服务器无法根据客户端请求的内容特性完成请求
         */
        NOT_ACCEPTABLE("Not Acceptable", 406),
        /**
         * 服务器不支持请求的功能，无法完成请求
         */
        NOT_SUPPORT("Not Implemented", 501),
        /**
         * 已接受。已经接受请求，但未处理完成
         */
        ACCEPTED("Accepted", 202);
        private String message;
        private Integer flag;

        Code(String message, int flag) {
            this.message = message;
            this.flag = flag;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public Integer getFlag() {
            return flag;
        }

        public void setFlag(Integer flag) {
            this.flag = flag;
        }


    }


}
