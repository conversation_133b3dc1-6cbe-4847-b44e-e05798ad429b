package com.enrising.dcarbon.demo.state;

/**
 * 测试客户端
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-10 13:40
 * @email <EMAIL>
 */
public class Client {
    public static void main(String[] args) {
        //测试调用Controller
        DemoController controller = new DemoController();
        DemoBaseData data = new DemoBaseData();
        data.setId(1L);
        //可以尝试注释下面的一些语句，状态的变化是不可跳跃的
        controller.add(data);
        controller.createAgenda(1L);
        controller.check(1L);
        controller.confirm(1L);
    }
}
