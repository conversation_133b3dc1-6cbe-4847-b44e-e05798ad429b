package com.enrising.dcarbon.manage;

import com.enrising.dcarbon.manage.event.*;
import com.enrising.dcarbon.servlet.ServletUtil;

import java.util.List;
import java.util.Map;

/**
 * 基于观察者模式的service，注意该类的所有返回值都是无效值，响应的写入由对应listener进行
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-10 16:12
 * @email <EMAIL>
 */
public class ObserverService<T extends BaseEntity> implements IService<T> {
    /**
     * web事件管理器，用于发布service操作事件的事件管理器
     */
    protected final ResponsiveEventManager responsiveEventManager = new ResponsiveEventManager();

    protected final PerRequestContext perRequestContext = new PerRequestContext();

    public ResponsiveEventManager getWebEventManger() {
        return responsiveEventManager;
    }

    @Override
    public int addOne(T data) {
        publishHandleEvent(new AddOneHandleEvent(data));
        return 0;
    }

    @Override
    @SuppressWarnings("unchecked")
    public int addList(List<T> dataList) {
        publishHandleEvent(new AddListHandleEvent((List<BaseEntity>) dataList));
        return 0;
    }

    @Override
    public int update(T param) {
        publishHandleEvent(new UpdateByEntityHandleEvent(param));
        return 0;
    }

    @Override
    public int update(Map<String, Object> params, String key) {
        publishHandleEvent(new UpdateByMapHandleEvent(new UpdateMap(key, params)));
        return 0;
    }

    @Override
    public int deleteLogicByIds(List<String> ids) {
        publishHandleEvent(new DelLogicByIdHandleEvent(ids));
        return 0;
    }

    @Override
    public int deleteLogic(T param) {
        publishHandleEvent(new DelLogicByEntityHandleEvent(param));
        return 0;
    }

    @Override
    public int deleteLoginByMap(Map<String, Object> params) {
        publishHandleEvent(new DelLogicByMapHandleEvent(params));
        return 0;
    }

    @Override
    public int deleteByIds(List<String> ids) {
        publishHandleEvent(new DelByIdHandleEvent(ids));
        return 0;
    }

    @Override
    public int delete(T param) {
        publishHandleEvent(new DelByEntityHandleEvent(param));
        return 0;
    }

    @Override
    public int deleteByMap(Map<String, Object> params) {
        publishHandleEvent(new DelByMapHandleEvent(params));
        return 0;
    }

    protected void publishHandleEvent(WebHandleEvent<?> event) {
        ServletUtil.bind();
        event.setStateEventManager(null);
        event.setWebEventManager(responsiveEventManager);
        event.setCurrentRequestContext(perRequestContext);
        responsiveEventManager.publishEventSync(event, event.getClass(), true);
        perRequestContext.remove();
    }

    public PerRequestContext getCurrentRequestContext() {
        return perRequestContext;
    }
}
