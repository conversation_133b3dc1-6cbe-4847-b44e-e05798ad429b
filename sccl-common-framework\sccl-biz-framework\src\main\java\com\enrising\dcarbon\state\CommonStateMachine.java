package com.enrising.dcarbon.state;

/**
 * 普通的状态机，状态改变的处理交由状态本身处理
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-10 10:07
 * @email <EMAIL>
 */
public class CommonStateMachine implements StateMachine {
    private AbstractCommonState currentState=new EmptyCommonState();

    public CommonStateMachine(AbstractCommonState currentState) {
        this.currentState = currentState;
    }

    public CommonStateMachine(Stateful stateful) {
        this.currentState = (AbstractCommonState) stateful.currentState();
    }

    public CommonStateMachine() {
    }

    @Override
    public void initialize(Stateful stateful) {
        currentState = (AbstractCommonState) stateful.currentState();
    }

    @Override
    public void stateChange(State state) {
        if (isCurrentStateMatch(state.getClass())) {
            throw new IllegalArgumentException("要变化到的状态与当前状态一致，无法变化");
        }
        currentState = (AbstractCommonState) state;
        currentState.setFromStateMachine(this);
        state.change();
    }

    @Override
    public AbstractCommonState currentState() {
        return currentState;
    }
}
