package com.enrising.dcarbon.demo.state;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-10 10:33
 * @email <EMAIL>
 */
public class DemoMapper {
    private static final Map<Long, DemoBaseData> virtualDB = new HashMap<>();

    public int insert(DemoBaseData data) {
        virtualDB.put(data.getId(), data);
        return 1;
    }

    public DemoBaseData query(long id) {
        return virtualDB.get(id);
    }

    public int updateStatus(long id, int status) {
        if (!virtualDB.containsKey(id)) {
            return 0;
        }
        query(id).setStatus(status);
        return 1;
    }
}
