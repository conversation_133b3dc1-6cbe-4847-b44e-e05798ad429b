package com.enrising.dcarbon.redis;

import com.ctg.itrdc.cache.pool.CtgJedisPool;
import com.ctg.itrdc.cache.pool.CtgJedisPoolConfig;
import com.ctg.itrdc.cache.pool.CtgJedisPoolException;
import com.ctg.itrdc.cache.vjedis.jedis.JedisPoolConfig;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import redis.clients.jedis.HostAndPort;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-07-05 10:17
 * @email <EMAIL>
 */
@ConditionalOnProperty(name="ctg.cache.enable",havingValue="true")
@Configuration
public class CtgJedisConfig {
    private final String CTG_CACHE_CLUSTER_NODES="ctg.cache.cluster.nodes";
    private final String CTG_CACHE_PASSWORD="ctg.cache.password";
    private final String CTG_CACHE_USER="ctg.cache.user";
    private final String CTG_CACHE_GROUP="ctg.cache.group";

    @Autowired
    private Environment envYml;

    @Bean
    public CtgJedisPool getCtgJedisPool() throws CtgJedisPoolException {

        String address=envYml.getProperty(CTG_CACHE_CLUSTER_NODES, "");

        List<HostAndPort> nodes=new ArrayList<HostAndPort>();
        String password=envYml.getProperty(CTG_CACHE_PASSWORD, "");
        String user=envYml.getProperty(CTG_CACHE_USER, "");
        String group=envYml.getProperty(CTG_CACHE_GROUP, "");
        String [] serverArray=address.split(",");
        //"用户#密码"
        String pwd=user+"#"+password;
        for (String ipPort:serverArray){
            String [] ipPortPair=ipPort.split(":");
            nodes.add(new HostAndPort(ipPortPair[0].trim(),Integer.parseInt(ipPortPair[1].trim())));
        }

        GenericObjectPoolConfig poolConfig = new JedisPoolConfig(); //线程池配置
        poolConfig.setMaxTotal(10); // 最大连接数（空闲+使用中）
        poolConfig.setMaxIdle(3); //最大空闲连接数
        poolConfig.setMinIdle(3); //保持的最小空闲连接数
        poolConfig.setMaxWaitMillis(3000); //借出连接时最大的等待时间
        CtgJedisPoolConfig config = new CtgJedisPoolConfig(nodes);
        config.setDatabase(group) //分组或者对应的int桶位(0--255)
              .setPassword(pwd) //用户#密码
              .setPoolConfig(poolConfig) //线程池配置
              .setPeriod(5000)  //后台监控执行周期，毫秒
              .setMonitorTimeout(500) //后台监控ping命令超时时间,毫秒
              .setMonitorLog(false);
        //.setMonitorLog(false);  //后台监控日志 稳定后可以关闭
        CtgJedisPool pool = new CtgJedisPool(config); //创建连接池
        return pool;
    }
}
