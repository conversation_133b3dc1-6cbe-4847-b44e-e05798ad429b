package com.enrising.dcarbon.demo.statedemo;

import com.enrising.dcarbon.observer.EventListener;
import com.enrising.dcarbon.state.StateChangeEvent;

/**
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-17 10:04
 * @email <EMAIL>
 */
public class StateChangeEventListener1 implements EventListener<StateChangeEvent> {
    @Override
    public void onEvent(StateChangeEvent event) {
        DemoBaseEntity entity = (DemoBaseEntity) event
                .getSource()
                .getSource();
        System.out.println(entity);
        System.out.println("监听到状态改变");

    }
}
