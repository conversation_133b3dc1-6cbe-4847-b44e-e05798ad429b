package com.enrising.dcarbon.state;

/**
 * 空状态
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-12 9:44
 * @email <EMAIL>
 */
public class EmptyObserverState extends AbstractObserverState implements EmptyState{

    @Override
    public StateEventFactory stateEventFactory() {
        return null;
    }

    @Override
    public void change() {
    }

    @Override
    public Object getSource() {
        return null;
    }

    @Override
    public StateMachine currentStateMachine() {
        return null;
    }
}
