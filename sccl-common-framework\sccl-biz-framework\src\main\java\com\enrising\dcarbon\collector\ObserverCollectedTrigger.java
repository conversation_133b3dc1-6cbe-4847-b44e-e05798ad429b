package com.enrising.dcarbon.collector;

import com.enrising.dcarbon.observer.EventListener;
import com.enrising.dcarbon.observer.EventManger;

/**
 * 基于观察者模式的采集触发器，当监听到{@link ExecuteCollectorEvent}事件及其子事件时触发采集，该触发器的创建代价比较高，最好
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-16 16:00
 * @email <EMAIL>
 */
public abstract class ObserverCollectedTrigger<E extends ExecuteCollectorEvent<?>> extends AbstractCollectorTrigger implements EventListener<E> {
    /**
     * 如果要通过事件来触发采集的话请使用该事件管理器发布事件
     */
    private static final EventManger EVENT_MANGER = new EventManger();

    public ObserverCollectedTrigger(AbstractCollectorTemplate template) {
        super(template);
        EVENT_MANGER.addListenerIfAbsent(ExecuteCollectorEvent.class, this);
    }

    public static void publishExecuteCollectorEvent(ExecuteCollectorEvent<?> executeCollectorEvent) {
        EVENT_MANGER.publishEvent(executeCollectorEvent, executeCollectorEvent.getClass(), true);
    }

    @Override
    public void onEvent(E event) {
        execute();
    }
}
