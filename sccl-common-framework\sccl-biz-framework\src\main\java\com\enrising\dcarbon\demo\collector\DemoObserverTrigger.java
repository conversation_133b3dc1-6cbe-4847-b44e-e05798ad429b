package com.enrising.dcarbon.demo.collector;

import com.enrising.dcarbon.collector.AbstractCollectorTemplate;
import com.enrising.dcarbon.collector.ExecuteCollectorEvent;
import com.enrising.dcarbon.collector.ObserverCollectedTrigger;

/**
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-16 16:07
 * @email <EMAIL>
 */
public class DemoObserverTrigger extends ObserverCollectedTrigger<ExecuteCollectorEvent<Object>> {
    public DemoObserverTrigger(AbstractCollectorTemplate template) {
        super(template);
    }

    @Override
    public void execute() {
        executeActiveTemplate();
    }
}
