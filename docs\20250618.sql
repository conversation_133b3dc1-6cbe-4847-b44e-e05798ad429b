-- 审计日志表
CREATE TABLE `sys_audit_log`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `month`            varchar(7)  NOT NULL COMMENT '月份，格式：YYYY-MM',
    `attachment_id`    bigint(20)           DEFAULT NULL COMMENT '附件ID，关联attachments表',
    `attachment_name`  varchar(255)         DEFAULT NULL COMMENT '附件名称',
    `upload_user_id`   bigint(20)  NOT NULL COMMENT '上传人ID',
    `upload_user_name` varchar(50) NOT NULL COMMENT '上传人姓名',
    `upload_time`      datetime    NOT NULL COMMENT '上传时间',
    `remark`           text COMMENT '备注',
    `create_time`      datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`      datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `del_flag`         char(1)     NOT NULL DEFAULT '0' COMMENT '删除标志，0:正常 1:删除',
    `create_by`        varchar(50)          DEFAULT NULL COMMENT '创建人',
    `update_by`        varchar(50)          DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_month` (`month`) USING BTREE,
    KEY `idx_upload_user` (`upload_user_id`) USING BTREE,
    KEY `idx_upload_time` (`upload_time`) USING BTREE,
    KEY `idx_del_flag` (`del_flag`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='审计日志表';

-- 检查上级菜单是否是系统管理
INSERT INTO `rmp`.`sys_authorities` (`id`, `name`, `code`, `parent_id`, `idx_num`, `auth_url`, `auth_type`, `descn`, `visible`, `perms`, `icon`, `del_flag`,
                                     `creator_id`, `creator_name`, `create_time`, `update_by_id`, `update_by_name`, `update_time`, `remark`)
VALUES (4699300954405756928, '审计日志', '', 3849276838270636037, 9, '/system/auditlog', 'C', '', '0', 'system:auditlog', '', '0', 1, '系统管理员',
        '2025-06-17 14:24:17', 0, '', NULL, '');


-- 用户登录日志表
CREATE TABLE `sys_login_log`
(
    `id`         bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '日志主键ID',
    `user_id`    bigint(20) unsigned          DEFAULT NULL COMMENT '用户ID',
    `username`   varchar(64)         NOT NULL COMMENT '用户名',
    `login_time` datetime(3)         NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '登录时间',
    `ip_address` varchar(45)         NOT NULL COMMENT '登录IP地址',
    `user_agent` varchar(512)                 DEFAULT NULL COMMENT '用户代理（浏览器和操作系统信息）',
    `status`     tinyint(1)          NOT NULL COMMENT '登录状态（1: 成功, 0: 失败）',
    `remark`     varchar(255)                 DEFAULT NULL COMMENT '备注信息（如失败原因）',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_login_time` (`login_time`),
    KEY `idx_ip_address` (`ip_address`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 133
  DEFAULT CHARSET = utf8mb4 COMMENT ='用户登录日志表';



-- 电表及协议管理新增修改查看新增转供电电表单价类型.md
-- power_ammeterorprotocol 表
ALTER TABLE power_ammeterorprotocol
    ADD COLUMN price_type     INT COMMENT '单价类型 1-固定单价 2-浮动单价',
    ADD COLUMN unit_price     DECIMAL(10, 4) COMMENT '单价',
    ADD COLUMN floating_price DECIMAL(10, 4) COMMENT '浮动单价',
    ADD COLUMN include_tax    TINYINT COMMENT '是否含税 0-否 1-是';

-- power_ammeterorprotocol_record 表
ALTER TABLE power_ammeterorprotocol_record
    ADD COLUMN price_type     INT COMMENT '单价类型 1-固定单价 2-浮动单价',
    ADD COLUMN unit_price     DECIMAL(10, 4) COMMENT '单价',
    ADD COLUMN floating_price DECIMAL(10, 4) COMMENT '浮动单价',
    ADD COLUMN include_tax    TINYINT COMMENT '是否含税 0-否 1-是';


-- 水电费数据记录表
CREATE TABLE `water_electricity_record`
(
    `id`                      bigint(20)   NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `date_dim`                varchar(6)   NOT NULL COMMENT '时间维度 格式: YYYYMM',
    `province`                varchar(10)  NOT NULL COMMENT '省份编码',
    `province_name`           varchar(50)  NOT NULL COMMENT '省份名',
    `city`                    varchar(10)  NOT NULL COMMENT '城市编码',
    `city_name`               varchar(50)  NOT NULL COMMENT '城市名',
    `dc_id`                   varchar(50)  NOT NULL COMMENT '数据中心编码',
    `dc_name`                 varchar(100) NOT NULL COMMENT '数据中心名称',
    `pue_act`                 decimal(10, 3)        DEFAULT NULL COMMENT '电能运行效率',
    `water_consumption`       decimal(15, 3)        DEFAULT NULL COMMENT '每月出账水费用水量 单位：吨',
    `water_bill`              decimal(15, 2)        DEFAULT NULL COMMENT '每月出账水费（不含税） 单位：元',
    `electricity_consumption` decimal(15, 3)        DEFAULT NULL COMMENT '每月用电量 单位：度',
    `electricity_bill`        decimal(15, 2)        DEFAULT NULL COMMENT '出账电费（不含税） 单位：元',
    `create_time`             datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`             datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `report_status`           tinyint(1)            DEFAULT '0' COMMENT '上报状态 0-未上报，1-已上报，2-上报失败',
    `report_time`             datetime              DEFAULT NULL COMMENT '上报时间',
    `report_message`          text COMMENT '上报响应信息',
    `department_id`           bigint(20)            DEFAULT NULL COMMENT '部门ID',
    PRIMARY KEY (`id`),
    KEY `idx_date_dim` (`date_dim`),
    KEY `idx_report_status` (`report_status`),
    KEY `idx_city` (`city`),
    KEY `idx_create_time` (`create_time`),
    KEY `idx_water_electricity_compound` (`date_dim`, `report_status`, `city`),
    KEY `idx_water_electricity_report_time` (`report_time`),
    KEY `idx_department_id` (`department_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4 COMMENT ='水电费数据记录表 - 用于存储四川省各地市IDC水电费数据并上报集团';


-- 转供电默认为固定单价，不含税
UPDATE `power_ammeterorprotocol` t
SET price_type  = 1,
    include_tax = 0
WHERE t.directsupplyflag = 2
  and t.status = 1
