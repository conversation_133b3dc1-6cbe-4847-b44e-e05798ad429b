package com.enrising.dcarbon.demo.statedemo;

import com.enrising.dcarbon.observer.EventManger;
import com.enrising.dcarbon.state.AbstractObserverState;
import com.enrising.dcarbon.state.StateEventFactory;

/**
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-17 9:56
 * @email <EMAIL>
 */
public class DraftState extends AbstractObserverState {
    private DemoBaseEntity entity;

    public DraftState(EventManger eventManger) {
        super(eventManger);
    }

    public DraftState() {
    }

    public DraftState setEntity(DemoBaseEntity entity) {
        this.entity = entity;
        return this;
    }

    @Override
    public StateEventFactory stateEventFactory() {
        return null;
    }

    @Override
    public Object getSource() {
        return entity;
    }
}
