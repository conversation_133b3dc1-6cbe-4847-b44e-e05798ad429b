package com.enrising.dcarbon.state;

import com.enrising.dcarbon.observer.EventManger;

/**
 * 基于观察者模式处理的状态机实现
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-09 17:06
 * @email <EMAIL>
 */
public class ObserverStateMachine implements StateMachine {
    private AbstractObserverState currentState = new EmptyObserverState();
    private EventManger eventManger;

    public ObserverStateMachine(AbstractObserverState currentState) {
        this.currentState = currentState;
    }

    public ObserverStateMachine(Stateful stateful) {
        this.currentState = (AbstractObserverState) stateful.currentState();
    }

    public ObserverStateMachine(AbstractObserverState currentState, EventManger eventManger) {
        this.currentState = currentState;
        this.eventManger = eventManger;
    }

    public ObserverStateMachine(Stateful stateful, EventManger eventManger) {
        this.currentState = (AbstractObserverState) stateful.currentState();
        this.eventManger = eventManger;
    }

    public ObserverStateMachine(EventManger eventManger) {
        this.eventManger = eventManger;
    }

    public ObserverStateMachine() {
    }

    /**
     * 配置事件管理器，配置后如果状态未设置事件管理器则会直接使用该状态机持有的事件管理器
     *
     * @param eventManger 事件管理器
     * @return void
     * <AUTHOR> ^ ▽ ^ *)
     * @date 2023/5/11 11:39
     */
    public void configEventManager(EventManger eventManger) {
        this.eventManger = eventManger;
    }

    public EventManger getEventManger() {
        return eventManger;
    }

    @Override
    public void initialize(Stateful stateful) {
        State initialState = stateful.currentState();
        if (initialState == null) {
            return;
        }
        if (initialState instanceof AbstractObserverState) {
            currentState = (AbstractObserverState) initialState;
        } else {
            throw new UnsupportedOperationException();
        }
    }

    @Override
    public void stateChange(State state) {
        if (isCurrentStateMatch(state.getClass())) {
            throw new IllegalArgumentException("要变化到的状态与当前状态一致，无法变化");
        }
        if (state instanceof AbstractObserverState) {
            currentState = (AbstractObserverState) state;
            if (currentState.getEventManger() == null && eventManger != null) {
                currentState.setEventManger(eventManger);
            }
            currentState.setFromStateMachine(this);
            currentState.change();
        } else {
            throw new UnsupportedOperationException("该状态机不支持这种状态");
        }
    }

    @Override
    public AbstractObserverState currentState() {
        return currentState;
    }
}
