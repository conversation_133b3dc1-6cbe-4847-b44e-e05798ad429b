package com.enrising.dcarbon.audit;

import lombok.Setter;
import lombok.experimental.Accessors;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;

import java.util.concurrent.TimeUnit;

/**
 * 评判链池，由于评判链不是线程安全的，为了提高评判效率对评判链进行了池化
 *
 * <AUTHOR>
 * @Date 2022/11/18 17:29
 * @Email <EMAIL>
 */
public class RefereeChainPool extends GenericObjectPool<RefereeChain> {
    private int poolSize;

    private RefereeChainPool(RefereeChain chain, GenericObjectPoolConfig config) {
        super(new RefereeChainFactory(chain), config);
    }

    public static Builder builder(RefereeChain parentChain) {
        return new Builder(parentChain);
    }

    public RefereeChain getRefereeChain() {
        try {
            return this.borrowObject();
        } catch (Exception ignored) {

        }
        return null;
    }

    public void release(RefereeChain chain) {
        this.returnObject(chain);
    }

    public int getPoolSize() {
        return poolSize;
    }

    @Setter
    @Accessors(chain = true)
    public static class Builder {
        /**
         * 最大存活时长，空闲时间超过该时长的评判链将会被回收，但线程池存活评判链数不会低于{@link #minIdle}给定的值
         */
        private long keepAliveTime = 20 * 60 * 1000;
        /**
         * 池内最大评判链数，负数时无限制
         */
        private int poolSize = 5;
        /**
         * 评判链池存活的最小评判链数
         */
        private int minIdle = 1;
        private long getChainTimeout = 3000;
        private final RefereeChain parentChain;

        /**
         * 创建一个评判链池构建者对象，评判池内的对象创建是基于克隆的
         *
         * @param parentChain 父评判链，评判链池内的评判链创建将会基于该链来克隆
         * <AUTHOR> Yongxiang
         * @date 2022/12/1 10:46
         */
        public Builder(RefereeChain parentChain) {
            this.parentChain = parentChain;
        }

        public Builder setKeepAliveTime(long keepAliveTime, TimeUnit unit) {
            this.keepAliveTime = unit.toMillis(keepAliveTime);
            return this;
        }

        public Builder setGetChainTimeout(long getChainTimeout, TimeUnit unit) {
            this.getChainTimeout = unit.toMillis(getChainTimeout);
            return this;
        }

        public RefereeChainPool build() {
            GenericObjectPoolConfig config = new GenericObjectPoolConfig();
            config.setMinIdle(minIdle);
            config.setMaxIdle(poolSize);
            config.setMaxTotal(poolSize);
            config.setMaxWaitMillis(getChainTimeout);
            config.setSoftMinEvictableIdleTimeMillis(keepAliveTime);
            RefereeChainPool chainPool = new RefereeChainPool(parentChain, config);
            chainPool.poolSize = this.poolSize;
            return chainPool;
        }


    }


}
