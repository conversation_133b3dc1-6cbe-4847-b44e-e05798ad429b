package com.enrising.dcarbon.audit;


import com.enrising.dcarbon.id.IdGenerator;
import com.enrising.dcarbon.reflect.ObjectUtil;

import java.util.Date;

/**
 * 抽象评判结果内容类
 *
 * <AUTHOR>
 * @date 2022-12-01 15:45
 * @email <EMAIL>
 */

public abstract class AbstractRefereeContent {
    public RefereeResult refereeResult;
    private Integer step;
    private String auditKey;

    /**
     * 创建一个新的评判结果内容类
     *
     * @param refereeResult 节点输出的评判结果
     * @param step          节点步序
     * @param auditKey      稽核对象主键
     * <AUTHOR>
     * @date 2022/12/1 16:28
     */
    public AbstractRefereeContent(RefereeResult refereeResult, int step, String auditKey) {
        this.refereeResult = refereeResult;
        this.step = step;
        this.auditKey = auditKey;
    }

    public AbstractRefereeContent() {
    }

    /**
     * 获取评判结果内容的序列化器，默认使用JSON进行序列化
     *
     * @return com.sccl.modules.business.stationaudit.framework.result.RefereeContentSerializer<java.lang.String>
     * <AUTHOR>
     * @date 2022/12/1 16:36
     */
    public RefereeContentSerializer<String> getRefereeContentSerializer() {
        return new JsonSerializerAndDeserializer();
    }

    /**
     * 将评判结果实体对象的内容反序列化成评判内容对象
     *
     * @param auditResult  评判结果实体
     * @param deserializer 反序列化器
     * @return com.sccl.modules.business.stationaudit.framework.result.AbstractRefereeContent
     * <AUTHOR> Yongxiang
     * @date 2022/12/1 16:43
     */
    @SuppressWarnings("unchecked")
    public static AbstractRefereeContent forAuditResult(AuditResult auditResult, RefereeContentDeserializer<AbstractRefereeContent, String> deserializer) {
        if (auditResult == null) {
            return null;
        }
        return deserializer.deserialize(auditResult.getContent(), (Class<AbstractRefereeContent>) ObjectUtil.classPath2Class(auditResult.getContentType()));
    }

    /**
     * 将评判结果实体对象的内容反序列化成评判内容对象，使用JSON反序列化器
     *
     * @param auditResult 评判结果实体
     * @return com.sccl.modules.business.stationaudit.framework.result.AbstractRefereeContent
     * <AUTHOR> Yongxiang
     * @date 2022/12/1 16:43
     */
    public static AbstractRefereeContent forAuditResultWithJson(AuditResult auditResult) {
        return forAuditResult(auditResult, new JsonSerializerAndDeserializer());
    }

    public String getContentType() {
        return this
                .getClass()
                .getName();
    }

    /**
     * 忽略抽象评判结果的固有属性，父类的固有属性将会被清空，该方法目的是防止序列化时该父类对象中的属性也被序列化，如使用JSON进行序列化时，不希望{@link #refereeResult}等属性被序列化，可以参照{@link JsonSerializerAndDeserializer#deserialize(String, Class)}中的使用方式，请确保该方法不会在调用{@link #getAuditResult()}方法前被执行，否则可能出现获得稽核结果实体对象时发生异常
     *
     * @return void
     * <AUTHOR> Yongxiang
     * @date 2022/12/1 16:29
     */
    public final void ignoreParentAttributes() {
        refereeResult = null;
        step = null;
        auditKey = null;
    }

    public AuditResult getAuditResult() {
        AuditResult auditResult = new AuditResult();
        auditResult.setId(IdGenerator.getNextIdAsLong());
        auditResult.setNodeTopic(refereeResult.getTopic());
        auditResult.setRefereeMessage(refereeResult.getRefereeMessage());
        auditResult.setStep(step);
        auditResult.setAuditKey(auditKey);
        auditResult.setCreateTime(new Date());
        auditResult.setContentType(getContentType());
        auditResult.setContent(getRefereeContentSerializer().serialize(this));
        return auditResult;
    }

    public RefereeResult getRefereeResult() {
        return refereeResult;
    }

    public void setRefereeResult(RefereeResult refereeResult) {
        this.refereeResult = refereeResult;
    }

    public Integer getStep() {
        return step;
    }

    public void setStep(Integer step) {
        this.step = step;
    }

    public String getAuditKey() {
        return auditKey;
    }

    public void setAuditKey(String auditKey) {
        this.auditKey = auditKey;
    }
}
