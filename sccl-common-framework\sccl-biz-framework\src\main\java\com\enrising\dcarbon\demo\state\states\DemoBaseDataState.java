package com.enrising.dcarbon.demo.state.states;

import com.enrising.dcarbon.demo.state.DemoBaseData;
import com.enrising.dcarbon.observer.EventManger;
import com.enrising.dcarbon.state.AbstractObserverState;

/**
 * 测试观察者状态，状态可以携带一些数据，这里携带的是基础数据{@link DemoBaseData}
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-10 11:16
 * @email <EMAIL>
 */
public abstract class DemoBaseDataState extends AbstractObserverState {
    private final DemoBaseData demoBaseData;

    public DemoBaseDataState(EventManger eventManger, DemoBaseData demoBaseData) {
        super(eventManger);
        this.demoBaseData = demoBaseData;
    }

    @Override
    public DemoBaseData getSource() {
        return demoBaseData;
    }
}
