# 电表基础信息新增字段说明

## 新增字段

为电表基础信息增加了4个字段，用于转供电时的单价管理：

### 1. 单价类型 (priceType)

- **字段类型**: Integer
- **取值**: 1-固定单价，2-浮动单价
- **规则**: 当电表类型为转供电时（directsupplyflag = 2），此字段必填

### 2. 单价 (unitPrice)

- **字段类型**: BigDecimal
- **规则**:
    - 转供电时必填
    - 浮动单价类型时必填
    - 保留4位小数
    - 不能为负数

### 3. 浮动单价 (floatingPrice)

- **字段类型**: BigDecimal
- **规则**:
    - 单价类型为浮动单价时必填
    - 必须大于单价
    - 保留4位小数
    - 不能为负数
    - 固定单价类型时不能有值

### 4. 是否含税 (includeTax)

- **字段类型**: Integer
- **取值**: 0-否，1-是
- **规则**: 当电表类型为转供电时，此字段必填

## 验证规则

当 `directsupplyflag = 2`（转供电）时：

1. **单价类型**、**是否含税** 为必填项
2. **固定单价类型** (priceType = 1)：
    - 单价必填且不能为负数
    - 浮动单价不能有值
3. **浮动单价类型** (priceType = 2)：
    - 单价和浮动单价都必填
    - 两者都不能为负数
    - 浮动单价必须大于单价

## 影响范围

### 修改的文件：

1. `Ammeterorprotocol.java` - 主实体类，添加4个新字段及其getter/setter方法
2. `AmmeterorprotocolRecord.java` - 记录实体类，添加4个新字段及其getter/setter方法
3. `AmmeterorprotocolServiceImpl.java` - 服务实现类，添加验证逻辑
4. `AmmeterorprotocolMapper.xml` - Mapper文件，添加字段映射
5. `AmmeterorprotocolRecordMapper.xml` - Record Mapper文件，添加字段映射

### 应用接口：

- 新增接口：`/business/ammeterorprotocol/add`
- 修改接口：`/business/ammeterorprotocol/edit`
- 查询接口：`/business/ammeterorprotocol/list`

## 数据库字段

需要在数据库表中添加以下字段：

```sql
-- power_ammeterorprotocol 表
ALTER TABLE power_ammeterorprotocol
    ADD COLUMN price_type     INT COMMENT '单价类型 1-固定单价 2-浮动单价',
    ADD COLUMN unit_price     DECIMAL(10, 4) COMMENT '单价',
    ADD COLUMN floating_price DECIMAL(10, 4) COMMENT '浮动单价',
    ADD COLUMN include_tax    TINYINT COMMENT '是否含税 0-否 1-是';

-- power_ammeterorprotocol_record 表  
ALTER TABLE power_ammeterorprotocol_record
    ADD COLUMN price_type     INT COMMENT '单价类型 1-固定单价 2-浮动单价',
    ADD COLUMN unit_price     DECIMAL(10, 4) COMMENT '单价',
    ADD COLUMN floating_price DECIMAL(10, 4) COMMENT '浮动单价',
    ADD COLUMN include_tax    TINYINT COMMENT '是否含税 0-否 1-是';
```

## 使用示例

### 固定单价类型

```json
{
  "directsupplyflag": 2,
  "priceType": 1,
  "unitPrice": 0.8500,
  "floatingPrice": null,
  "includeTax": 1
}
```

### 浮动单价类型

```json
{
  "directsupplyflag": 2,
  "priceType": 2,
  "unitPrice": 0.8000,
  "floatingPrice": 1.2000,
  "includeTax": 0
}
``` 
