package com.enrising.dcarbon.manage;


/**
 * 单次请求的上下文，用于参数传播
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-07-06 16:22
 * @email <EMAIL>
 */
public class PerRequestContext {
    private final InheritableThreadLocal<Object> context = new InheritableThreadLocal<>();

    public boolean exist() {
        return context.get() != null;
    }

    public void setValue(Object value) {
        context.set(value);
    }

    @SuppressWarnings("unchecked")
    public <T> T get() {
        return (T) context.get();
    }

    public void remove() {
        context.remove();
    }
}
