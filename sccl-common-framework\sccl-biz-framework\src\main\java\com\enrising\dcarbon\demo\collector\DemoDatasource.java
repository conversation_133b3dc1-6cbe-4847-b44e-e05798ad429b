package com.enrising.dcarbon.demo.collector;

import com.enrising.dcarbon.collector.CollectedDatasource;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 测试收集数据源
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-12 14:04
 * @email <EMAIL>
 */
@Getter
@Setter
@AllArgsConstructor
@ToString
public class DemoDatasource implements CollectedDatasource {
    private Long id;

    private Integer status;

    @Override
    public String getAuditKey() {
        return id + "";
    }

    @Override
    public boolean isAvailable() {
        return status != null && status == 1;
    }
}
