package com.enrising.dcarbon.manage.event;

import com.enrising.dcarbon.manage.WebHandleEvent;

import java.util.List;

/**
 * 根据ID逻辑删除事件
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-10 16:19
 * @email <EMAIL>
 */
public class DelLogicByIdHandleEvent extends WebHandleEvent<List<String>> {
    public DelLogicByIdHandleEvent() {
        super();
    }

    public DelLogicByIdHandleEvent(List<String> source) {
        super(source);
    }
}
