package com.enrising.dcarbon.manage.event;

import com.enrising.dcarbon.manage.BaseEntity;
import com.enrising.dcarbon.manage.WebHandleEvent;

/**
 * 根据实体参数物理删除事件
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-10 16:20
 * @email <EMAIL>
 */
public class DelByEntityHandleEvent extends WebHandleEvent<BaseEntity> {
    public DelByEntityHandleEvent() {
        super();
    }

    public DelByEntityHandleEvent(BaseEntity source) {
        super(source);
    }
}
