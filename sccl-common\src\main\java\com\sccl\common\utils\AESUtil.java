package com.sccl.common.utils;

import org.bouncycastle.jce.provider.BouncyCastleProvider;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.security.Security;
import java.util.Arrays;

/**
 * @Description
 * @Auther Huang Yongxiang
 * @Date 2021/12/06 16:25
 */
public class AESUtil {
    /**
     * AES128 算法
     * CBC 模式
     * PKCS7Padding 填充模式
     * CBC模式需要添加一个参数iv
     * 介于java 不支持PKCS7Padding，只支持PKCS5Padding 但是PKCS7Padding 和 PKCS5Padding 没有什么区别
     * 要实现在java端用PKCS7Padding填充，需要用到bouncycastle组件来实现
     */

// 算法名称
    static final String KEY_ALGORITHM = "AES";
    // 加解密算法/模式/填充方式
    static final String algorithmStr = "AES/CBC/PKCS7Padding";
    //
    private static Key key;
    private static Cipher cipher;
    boolean isInited = false;

    static byte[] iv = { 0x30, 0x31, 0x30, 0x32, 0x30, 0x33, 0x30, 0x34, 0x30, 0x35, 0x30, 0x36, 0x30, 0x37, 0x30, 0x38 };
    public static void init(byte[] keyBytes) {

        // 如果密钥不足16位，那么就补足.  这个if 中的内容很重要
        int base = 16;
        if (keyBytes.length % base != 0) {
            int groups = keyBytes.length / base + (keyBytes.length % base != 0 ? 1 : 0);
            byte[] temp = new byte[groups * base];
            Arrays.fill(temp, (byte) 0);
            System.arraycopy(keyBytes, 0, temp, 0, keyBytes.length);
            keyBytes = temp;
        }
        // 初始化
        Security.addProvider(new BouncyCastleProvider());
        // 转化成JAVA的密钥格式
        key = new SecretKeySpec(keyBytes, KEY_ALGORITHM);
        try {
            // 初始化cipher
            cipher = Cipher.getInstance(algorithmStr, "BC");
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }
    /**
     * 加密方法
     *
     * @param content
     *            要加密的字符串
     * @param keyBytes
     *            加密密钥
     * @return
     */
    public static byte[] encrypt(byte[] content, byte[] keyBytes) {
        byte[] encryptedText = null;
        init(keyBytes);
        System.out.println("IV：" + new String(iv));
        try {
            cipher.init(Cipher.ENCRYPT_MODE, key, new IvParameterSpec("1234567890000000".getBytes()));
            encryptedText = cipher.doFinal(content);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        return encryptedText;
    }
    /**
     * 解密方法
     *
     * @param encryptedData
     *            要解密的字符串
     * @param keyBytes
     *            解密密钥
     * @return
     */
    public static byte[] decrypt(byte[] encryptedData, byte[] keyBytes) {
        byte[] encryptedText = null;
        init(keyBytes);
        System.out.println("IV：" + new String(iv));
        try {
            cipher.init(Cipher.DECRYPT_MODE, key, new IvParameterSpec("1234567890000000".getBytes()));
            encryptedText = cipher.doFinal(encryptedData);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        return encryptedText;
    }
    public static void main(String[] args){
        String code="FD58C1F5EDA8CFB9C54A30BDDCE7A99A6803C85F4F03A1C235C7FE7BE7FCFBB16A4E" +
                "0BAA1EC6A8F6D20767E6D699AE0829D17D7F04780323B8DE5E185D8D989982657EED23FC" +
                "73B7B6FA9C2D7196D1E9E263F04C4A0832B6C04B48795D84B92BF1CCC3DBFA7A5C173EE0" +
                "B5DC8124C41342A0FB4D12945B9D882498538A201652D533E08441EEA9E0F2111497D07E" +
                "5FA4F94C123BD1C729FF999F1A2E98D23F7DFB3D453F712B08EDFC817F9DFFBD6349A815" +
                "E01668560D280E7BDE969FA30D4D98CF239A5C091901E104588051472E7FAA04C625BF3F" +
                "7CFBA51794E186B6214FFF36CF7874D6370D91C8745F490C0AA9081AD9296B7B56F36674" +
                "5D6733E54949AA1ED6ACCF072C911A922255E027214B24259E170A9899AB061B4C8E1B49" +
                "8EAC";
        String key= null;
        key = new String(code.substring(0,16).getBytes(), StandardCharsets.UTF_8);
        byte[] keyBytes=key.getBytes();
        try {
            byte[] hexCode=HexUtil.parseHexString(code);
            String base64Code=Base64Utils.encode(hexCode);
            byte[] res=decrypt(base64Code.getBytes(),keyBytes);
            String r=new String(res,StandardCharsets.UTF_8);
            System.out.println(r);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
}

