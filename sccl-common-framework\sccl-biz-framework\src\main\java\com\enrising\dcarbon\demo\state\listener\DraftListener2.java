package com.enrising.dcarbon.demo.state.listener;

import com.enrising.dcarbon.demo.state.events.DraftEvent;
import com.enrising.dcarbon.observer.EventListener;

/**
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-10 13:52
 * @email <EMAIL>
 */
public class DraftListener2 implements EventListener<DraftEvent> {
    @Override
    public void onEvent(DraftEvent event) {
        System.out.println("监听到草稿生成，该监听器优先级为" + level());
    }

    @Override
    public int level() {
        return 1;
    }
}
