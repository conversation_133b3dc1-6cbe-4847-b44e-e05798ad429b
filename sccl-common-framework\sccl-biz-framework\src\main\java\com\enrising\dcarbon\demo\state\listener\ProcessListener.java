package com.enrising.dcarbon.demo.state.listener;

import com.enrising.dcarbon.demo.state.DemoBaseData;
import com.enrising.dcarbon.demo.state.events.ProcessEvent;
import com.enrising.dcarbon.observer.EventListener;

/**
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-10 11:10
 * @email <EMAIL>
 */
public class ProcessListener implements EventListener<ProcessEvent> {
    @Override
    public void onEvent(ProcessEvent event) {
        DemoBaseData data = (DemoBaseData) event
                .getSource()
                .getSource();
        System.out.println("监听到流程开启事件，当前数据状态：" + data.getStatus());

    }
}
