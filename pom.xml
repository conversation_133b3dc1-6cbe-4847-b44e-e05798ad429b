<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.sccl</groupId>
    <artifactId>sccl-basic-frame</artifactId>
    <version>1.0</version>
    <name>sccl-basic-frame</name>
    <description>软件中心基础框架</description>
    <packaging>pom</packaging>


    <properties>
        <java.version>1.8</java.version>
        <sccl-basic-frame.version>1.0</sccl-basic-frame.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <!-- attachments version setting -->
        <commons-lang3.version>3.4</commons-lang3.version>
        <commons-io.version>2.5</commons-io.version>
        <commons-fileupload.version>1.3.1</commons-fileupload.version>
        <commons-beanutils.version>1.9.3</commons-beanutils.version>
        <ruedigermoeller-fst.version>2.56</ruedigermoeller-fst.version>
        <json.version>20170516</json.version>
        <dozer.version>5.5.1</dozer.version>
        <poi.version>3.14</poi.version>
        <fastjson.version>1.2.73</fastjson.version>
        <expiring-map.version>0.5.10</expiring-map.version>
        <drools.version>7.44.0.Final</drools.version>
        <ruedigermoeller-fst.version>2.56</ruedigermoeller-fst.version>
        <pagehelper.spring.boot.starter.version>1.4.5</pagehelper.spring.boot.starter.version>
        <mybatis.spring.boot.starter.version>2.3.2</mybatis.spring.boot.starter.version>
        <httpclient.version>4.5.8</httpclient.version>
        <httpcore.version>4.4.11</httpcore.version>
        <reflections.version>0.10.2</reflections.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>2.0.5.RELEASE</version>
                <type>pom</type>
                <scope>import</scope>

            </dependency>

            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>${httpclient.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.httpcomponents</groupId>
                        <artifactId>httpcore</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpcore</artifactId>
                <version>${httpcore.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.sctel.aiolog</groupId>
                <artifactId>aiolog-common-sdk</artifactId>
                <version>0.0.1</version>
            </dependency>

            <!-- BouncyCastle 依赖管理，统一版本避免冲突 -->
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk18on</artifactId>
                <version>1.76</version>
            </dependency>
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcmail-jdk18on</artifactId>
                <version>1.76</version>
            </dependency>
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcpkix-jdk18on</artifactId>
                <version>1.76</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <!-- WEB begin -->
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
            <scope>provided</scope>
        </dependency>
        <!-- WEB end -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <optional>true</optional>
        </dependency>

    </dependencies>


    <modules>
        <module>sccl-common</module>
        <module>sccl-framework</module>
        <module>sccl-module-domain</module>
        <module>sccl-module-base</module>
        <module>sccl-module-business</module>
        <module>sccl-module-uniflow</module>
        <module>sccl-module-timing</module>
        <module>sccl-web</module>
        <module>sccl-common-framework</module>
        <module>sccl-interface</module>
    </modules>

    <profiles>
        <profile>
            <id>loc</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <spring.profiles.active>loc</spring.profiles.active>
                <log.level>info</log.level>
            </properties>
        </profile>
        <profile>
            <id>devln</id>
            <properties>
                <spring.profiles.active>dev-ln</spring.profiles.active>
                <log.level>debug</log.level>
            </properties>
        </profile>
        <profile>
            <id>ln</id>
            <properties>
                <spring.profiles.active>ln</spring.profiles.active>
                <log.level>info</log.level>
            </properties>
        </profile>
        <profile>
            <id>devsc</id>
            <properties>
                <spring.profiles.active>dev-sc</spring.profiles.active>
                <log.level>debug</log.level>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <spring.profiles.active>prod</spring.profiles.active>
                <log.level>info</log.level>
            </properties>
        </profile>
    </profiles>

    <build>
        <plugins>
            <!--定义不需要过滤的文件-->
<!--            <plugin>
                <artifactId>maven-resources-plugin</artifactId>
                <configuration>
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>keytab</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>-->
            <!-- 本地测试时开启 begin -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>1.5.8.RELEASE</version>
                <configuration>
                    <fork>true</fork>
                </configuration>
            </plugin>
            <!-- 本地测试时开启 end -->

            <!-- 打包发布时开启 begin-->
            <!--<plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <configuration>
                    <failOnMissingWebXml>false</failOnMissingWebXml>
                </configuration>
            </plugin>-->
            <!-- 打包发布时开启 end-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.7.0</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>

        </plugins>
    </build>

</project>