package com.enrising.dcarbon.manage.event;

import com.enrising.dcarbon.manage.WebHandleEvent;

import java.util.Map;

/**
 * 根据MAP参数物理删除事件
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-11 9:30
 * @email <EMAIL>
 */
public class DelByMapHandleEvent extends WebHandleEvent<Map<String, Object>> {
    public DelByMapHandleEvent() {
        super();
    }

    public DelByMapHandleEvent(Map<String, Object> source) {
        super(source);
    }
}
