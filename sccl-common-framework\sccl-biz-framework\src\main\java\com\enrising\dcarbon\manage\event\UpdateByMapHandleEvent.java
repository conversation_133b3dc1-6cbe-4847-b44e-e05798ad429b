package com.enrising.dcarbon.manage.event;

import com.enrising.dcarbon.manage.UpdateMap;
import com.enrising.dcarbon.manage.WebHandleEvent;

/**
 * 根据MAP参数更新事件
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-10 16:19
 * @email <EMAIL>
 */
public class UpdateByMapHandleEvent extends WebHandleEvent<UpdateMap> {
    public UpdateByMapHandleEvent() {
        super();
    }

    public UpdateByMapHandleEvent(UpdateMap source) {
        super(source);
    }
}
