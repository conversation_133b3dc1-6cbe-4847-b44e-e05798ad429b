package com.enrising.dcarbon.collector;

import com.enrising.dcarbon.date.DateUtils;
import com.enrising.dcarbon.lang.Refreshable;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * 周期性触发的触发器
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-12 10:37
 * @email <EMAIL>
 */
@Slf4j
public abstract class CyclicallyCollectorTrigger extends AbstractCollectorTrigger implements Refreshable {
    final long cycle;
    long nextTriggeringTime;
    volatile boolean isRunning = false;
    volatile boolean isWaiting = false;

    public CyclicallyCollectorTrigger(AbstractCollectorTemplate template, long cycle, TimeUnit unit) {
        super(template);
        this.cycle = unit.toMillis(cycle);
    }

    /**
     * 触发器必须注册才能生效
     *
     * @return void
     * <AUTHOR> ^ ▽ ^ *)
     * @date 2023/5/12 14:57
     */
    public void register() {
        refresh();
        CycleTrigLoop
                .getInstance()
                .register(this);
    }


    boolean isReachTriggerTime() {
        long in = nextTriggeringTime - System.currentTimeMillis();
        return Math.abs(in) <= 1000;
    }

    @Override
    public void refresh() {
        nextTriggeringTime = System.currentTimeMillis() + cycle;
        nextTriggeringTime = nextTriggeringTime - nextTriggeringTime % 1000;
        log.info("cycle触发器刷新成功，下次执行时间：{}", DateUtils.formatDateTime(new Date(nextTriggeringTime)));
    }

    @Override
    public final void beforeTrig() {
        isRunning = true;
    }

    @Override
    public final void afterTrig() {
        isRunning = false;
        isWaiting = false;
        refresh();
    }

    @Override
    public final void errorTrig(Throwable throwable) {
        if (throwable != null) {
            throwable.printStackTrace();
        }
        afterTrig();
    }
}
