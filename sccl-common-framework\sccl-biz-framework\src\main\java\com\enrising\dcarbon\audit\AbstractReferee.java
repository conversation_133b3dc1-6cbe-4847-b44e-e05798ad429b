package com.enrising.dcarbon.audit;


import java.util.List;
import java.util.Map;

/**
 * 抽象评判器
 *
 * <AUTHOR>
 * @Date 2022/11/10 17:15
 * @Email <EMAIL>
 */
public abstract class AbstractReferee implements Cloneable {
    /**
     * 评判器名称
     */
    protected String refereeName;

    public AbstractReferee(String refereeName) {
        this.refereeName = refereeName;
    }

    public String getRefereeName() {
        return refereeName;
    }

    public AbstractReferee setRefereeName(String refereeName) {
        this.refereeName = refereeName;
        return this;
    }

    /**
     * 子类必须实现评判的逻辑
     *
     * @param lastRefereeResult     上一个节点评判结果
     * @param auditable             当前可稽核的对象
     * @param refereeDatasourceList 评判的数据源
     * @return com.sccl.modules.business.stationaudit.framework.referee.RefereeResult
     * <AUTHOR>
     * @date 2022/11/10 18:11
     */
    public abstract RefereeResult doReferee(RefereeResult lastRefereeResult, Auditable auditable, Map<Class<? extends RefereeDatasource>, List<RefereeDatasource>> refereeDatasourceList);

    @Override
    protected Object clone() throws CloneNotSupportedException {
        return super.clone();
    }
}
