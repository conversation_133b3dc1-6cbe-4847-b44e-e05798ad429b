package com.enrising.dcarbon.audit;

import org.apache.commons.pool2.PooledObject;
import org.apache.commons.pool2.PooledObjectFactory;
import org.apache.commons.pool2.impl.DefaultPooledObject;

/**
 * <AUTHOR>
 * @Date 2022/11/18 17:34
 * @Email <EMAIL>
 */
public class RefereeChainFactory implements PooledObjectFactory<RefereeChain> {
    private final RefereeChain parentChain;

    public RefereeChainFactory(RefereeChain parentChain) {
        if (parentChain == null) {
            throw new NullPointerException();
        }
        this.parentChain = parentChain;
    }

    @Override
    public void activateObject(PooledObject<RefereeChain> p) {
        p
                .getObject()
                .reset();
    }

    @Override
    public void destroyObject(PooledObject<RefereeChain> p) {

    }

    @Override
    public PooledObject<RefereeChain> makeObject() throws Exception {
        return new DefaultPooledObject<>((<PERSON><PERSON>hain) parentChain.clone());
    }

    @Override
    public void passivateObject(PooledObject<RefereeChain> p) {
        p
                .getObject()
                .reset();
    }


    @Override
    public boolean validateObject(PooledObject<RefereeChain> p) {
        return p.getObject().isFinished;
    }
}
