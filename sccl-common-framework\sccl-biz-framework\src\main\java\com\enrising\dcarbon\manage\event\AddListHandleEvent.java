package com.enrising.dcarbon.manage.event;

import com.enrising.dcarbon.manage.BaseEntity;
import com.enrising.dcarbon.manage.WebHandleEvent;

import java.util.List;

/**
 * 新增列表事件
 *
 * <AUTHOR> ^ ▽ ^ *)
 * @date 2023-05-10 16:18
 * @email <EMAIL>
 */
public class AddListHandleEvent extends WebHandleEvent<List<BaseEntity>> {
    public AddListHandleEvent() {
        super();
    }

    public AddListHandleEvent(List<BaseEntity> source) {
        super(source);
    }
}
